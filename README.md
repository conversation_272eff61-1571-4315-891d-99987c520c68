TD4P (Test Data Automation for Passenger)
=====================

**A Groovy Library for producing test data or providing resources (request creation classes, test
run listeners vs.)
required to run test cases in its way in your soapui project.**

---------------

Getting Started
---------------

TD4P is available at the
company's [Repository](https://artifactory.thy.com/artifactory/dtoc-maven-preprod/com/thy/qa/td4p/1.15.11.1/).
It comes
with a sources jar file for users to be able to see doc info. TD4P uses many transitive
dependencies. Rather, Soapui
application uses them for its internal transactions. So, when you add TD4P to your project, you
might have some
dependency conflicts. In this case, it is inherently your responsibility to handle dependency
management. Also, it is
strongly not recommended to change the versions of the core libraries (ready-api-soapui, groovy-all)
of this project. As
for testing, this project has to use Junit4 test framework at runtime. So, you should add another
one if you use a
different test framework in your project.

```xml

<dependency>
  <groupId>com.thy.qa</groupId>
  <artifactId>td4p</artifactId>
  <version>1.15.11.1</version>
</dependency>
```

The GetPnr class represents the entry to the application. Once it's initialized, its Soapui project
is loaded and any
test case of which gets ready to be run.

The aim of this application is basically to generate pnr. Moreover, it also provides the mechanism
for running
functional tests on Soapui application when you have its test run listener (TestStepListener),
required test steps in
your test case and set required context parameters by calling TestCaseContext ->
setContextParameters(context) or
ReissueContext -> setContextParameters(context) before test steps.

The class GetPnr includes methods for generating pnr of types which are ticket, award ticket,
checkin, pay and fly, free
reservation, paid reservation and reissue.

> Its structure heavily depends on creating a properties map, running the test case of the loaded
> project and extracting
> the needed info (for example pnr, surname, ticket number...) if the test case has passed.

The class GetDocumentInfo also works the same way as GetPnr. But it is only used for getting
document (ticket or emd)
info required for Pares hot verification test.

HOW TO USE
---------------

Sample Usage ->

```groovy
List flights = [
        new Flight(origin: "CDG", destination: "SAW", dayToFlight: 90, fareType: FareType.BUSINESS),
        new Flight(origin: "SAW", destination: "CDG", dayToFlight: 99, fareType: FareType.BUSINESS)
]
PassengerCombination passengerCombination = new PassengerCombination(2, 1, 1)
passengerCombination.getPax().get(1).setMsNo("TK001479868")
TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
testCaseContext.setChannel(Channel.SMARTMOBILE)
String pnrNumber = new GetPnr().getTicketPnr(testCaseContext)
```

As in the example above, you need to create some objects first. ->

1. A list of flights (feel free for its order)
2. A PassengerCombination object (so-called passengers)
3. A TestCaseContext or ReissueContext (for reissue) object.

And you are now one method call (getTicketPnr, getAwardTicketPnr,..) away from getting the needed
test data.

You can also find more details in test packages.

As for the integration with soapui test cases, you don't need to create a GetPnr object (new
GetPnr()). But you need to
call TestCaseContext class's setContextParameters. You can find an example below. You are free to
place this integration
script into any script area (groovy script test step or setup script) in your test case before the
required test steps.

```groovy
List flights = [
        new Flight(origin: "ADB", destination: "CDG", stopover: 1, dayToFlight: 90, fareType: FareType.BUSINESS),
        new Flight(origin: "CDG", destination: "SAW", stopover: 0, dayToFlight: 99, fareType: FareType.BUSINESS) /* equivalent to the one without stopover parameter --> new Flight(origin: "CDG",destination: "SAW", dayToFlight: 99, fareType: FareType.BUSINESS) stopover parameter is added in this example for better understanding*/
]
PassengerCombination passengerCombination = new PassengerCombination(2, 1, 1)
passengerCombination.getPax().get(1).setMsNo("TK001479868") // This means setting ms no for the second passenger.
TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
testCaseContext.setChannel(Channel.SMARTMOBILE)
testCaseContext.setContextParameters(context)
```

Flight --->

Flight means a route (IST-ADB direct flight or ADB-JFK connecting flight with stopover 1) with or
without stop overs.
You must set origin, destination, dayToFlight(if not a flight needed to suitable for checkin or not
desired to be 0).
Fare Type parameter is optional. Stopover parameter is optional but you must set a value other than
0 for a route with
stop overs ( for example ADB-JFK -> ADB-IST and IST-JFK -> stopover must be 1 in this case). For the
example above,
groovy's map constructor is used to create Flight objects simply. But if you use java or any jvm
language, you must use
setter methods to set properties. It is your responsibility to set proper values.

PassengerCombination -->

It’s worth noting that if you use Passenger objects to create an instance of PassengerCombination,
you need to know
about Passenger class. You can find detail about PassengerCombination constructors in its source
file.

Passenger ->

Passenger class contains some properties ( passenger's name, surname, birth date, nationality,
PassengerCode enum, msNo(
optional), jetGencNo(optional) vs.). Actually there is no need to set any property. Passenger class
ships with some
default values (name, surname, nationality, hes code, tc no, passenger type). But for any membership
number, you must
use its setter method(setMsNo, setJetGencNo). If you set any passenger's m&s number by the method
setMsNo, this means
that the passenger info will be retrieved by calling a web service (getMemberDetails or
getJGMembershipInfo) instead of
the related Passenger class's properties. Passenger type is required to create an instance of
Passenger class in any
case.

TestCaseContext -->

This class aims at creating a map with required parameters due to given parameters by its
constructors. And this map is
used in the run method of any internal test case ( testCase.run(new StringToObjectMap(
propertiesMap), false) ) or for
setting the map's parameters for external soapui test case ( testCaseContext.setContextParameters(
context) ). Its
constructors require some input parameters ( routing type and flights are must, passenger
combination is optional only
if you have one adult passenger).

The most important info about this class is that you can use its methods to alter some test case
settings. To put it
briefly, payment or channel type can be altered. You can see the example usages below. For more
detail, you can see the
class's methods.

> For channel -> testCaseContext.setChannel(Channel.SMARTMOBILE)
>
> For payment -> testCaseContext.setPayment(new EFTPaymentInfo())

Automated Tests
---------------

Tests are placed into packages under the src/test/groovy source file and grouped by its type of
functionality. You can
easily figure out that which types of functionality are covered in packages.

Tests are typically run during the build process. But this project's tests are run before because
any test may fail to
produce test data due to test environment temporary errors or no option found to generate test data
for the given
criteria. This is intentional. Otherwise the build process is interrupted and artifactory uploading
can not be done. So
you shouldn't add execution goals for test coverage in pom.xml. Any contributer who wants to push
any piece of code must
ensure that he has already written test classes for his features (if his features are already
covered in current tests,
no need to add new test classes) and run "the project's all tests" (at least most of them)
successfully.

Suggestions
---------------

1. As explained before, this project has two functions which are generating test data and providing
   resources to
   integrate with your possibly soapui project. So, you should always keep it in your mind in the
   development process of
   any feature for this project that your new feature must conform to these two functions. That is
   to say, you must
   ensure that there will be no error or loss of functionality about integration with any soapui
   test case.
2. If you develop any feature (for example; a request creation class for a web service) and its
   average process time
   takes more than 200-250 ms, you might have a performance leak issue. In this case, you should
   find and fix that
   issue. As a quick suggestion, you should consider examining groovy's main closures for better
   understanding/handling
   or taking your code into parts.
3. Any test step's listener transactions may not comply with your requirements in terms of
   integration with your soapui
   test cases or automation project. In this case, tweaking the related test step's transactions by
   intercepting its
   method calls might be a good option or you can make a feature development request. This case
   worths mentioning
   because Td4p's test run listener's transactions might differ from your project's ones in terms of
   changing purposes
   like generating pnr or functional testing of web services.
