<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.thy.qa</groupId>
  <artifactId>td4p</artifactId>
  <version>1.15.11.1</version>
  <packaging>jar</packaging>

  <name>td4p</name>
  <url>http://maven.apache.org</url>

  <properties>
    <maven.compiler.release>11</maven.compiler.release>
    <java.version>11</java.version>
    <groovy.version>4.0.24</groovy.version>
    <soapui.version>3.1.0</soapui.version>
    <slf4j.version>1.7.36</slf4j.version>
    <log4j.version>2.23.1</log4j.version>
    <jetty.version>12.0.12</jetty.version>
    <jackson.version>2.17.2</jackson.version>
  </properties>

  <dependencies>

    <dependency>
      <groupId>com.smartbear</groupId>
      <artifactId>ready-api-soapui</artifactId>
      <version>${soapui.version}</version>
      <exclusions>
        <exclusion>
          <groupId>*</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.smartbear</groupId>
      <artifactId>ready-api-core</artifactId>
      <version>${soapui.version}</version>
      <exclusions>
        <exclusion>
          <groupId>*</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.smartbear.utils.analytics</groupId>
      <artifactId>analytics-core</artifactId>
      <version>2.5.0</version>
    </dependency>

    <dependency>
      <groupId>commons-beanutils</groupId>
      <artifactId>commons-beanutils</artifactId>
      <version>1.9.4</version>
    </dependency>

    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>${slf4j.version}</version>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-ext</artifactId>
      <version>${slf4j.version}</version>
    </dependency>

    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>33.2.1-jre</version>
    </dependency>

    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
      <version>${log4j.version}</version>
    </dependency>

    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-slf4j-impl</artifactId>
      <version>${log4j.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-layout-template-json</artifactId>
      <version>${log4j.version}</version>
    </dependency>

    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-server</artifactId>
      <version>${jetty.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-http</artifactId>
      <version>${jetty.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-util</artifactId>
      <version>${jetty.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-servlet</artifactId>
      <version>11.0.22</version>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.5.13</version>
    </dependency>

    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpcore</artifactId>
      <version>4.4.16</version>
    </dependency>

    <!-- jdk15 yerine jdk15on kullanılıyor -->
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcprov-jdk18on</artifactId>
      <version>1.78</version>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <version>${jackson.version}</version>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>${jackson.version}</version>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>${jackson.version}</version>
    </dependency>

    <dependency>
      <groupId>org.apache.groovy</groupId>
      <artifactId>groovy</artifactId>
      <version>${groovy.version}</version>
    </dependency>

    <dependency>
      <groupId>org.apache.groovy</groupId>
      <artifactId>groovy-datetime</artifactId>
      <version>${groovy.version}</version>
    </dependency>

    <dependency>
      <groupId>org.apache.groovy</groupId>
      <artifactId>groovy-json</artifactId>
      <version>${groovy.version}</version>
    </dependency>

    <dependency>
      <groupId>org.apache.groovy</groupId>
      <artifactId>groovy-xml</artifactId>
      <version>${groovy.version}</version>
    </dependency>

    <dependency>
      <groupId>net.minidev</groupId>
      <artifactId>json-smart</artifactId>
      <version>2.5.1</version>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.woodstox</groupId>
      <artifactId>woodstox-core</artifactId>
      <version>7.0.0</version>
    </dependency>

    <dependency>
      <groupId>org.apache.santuario</groupId>
      <artifactId>xmlsec</artifactId>
      <version>4.0.0</version>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.json</groupId>
      <artifactId>json</artifactId>
      <version>20231013</version>
    </dependency>

    <dependency>
      <groupId>org.mozilla</groupId>
      <artifactId>rhino</artifactId>
      <version>1.7R5</version>
    </dependency>

    <dependency>
      <groupId>com.google.oauth-client</groupId>
      <artifactId>google-oauth-client</artifactId>
      <version>1.35.0</version>
    </dependency>

    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant</artifactId>
      <version>1.10.14</version>
      <exclusions>
        <exclusion>
          <groupId>com.sun</groupId>
          <artifactId>tools</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.eclipse.xtend</groupId>
      <artifactId>org.eclipse.xtend.lib</artifactId>
      <version>2.35.0</version>
    </dependency>

    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <version>1.17.0</version>
    </dependency>

    <dependency>
      <groupId>jakarta.mail</groupId>
      <artifactId>jakarta.mail-api</artifactId>
      <version>2.1.3</version>
    </dependency>

    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.16.1</version>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.14.0</version>
    </dependency>

    <dependency>
      <groupId>commons-lang</groupId>
      <artifactId>commons-lang</artifactId>
      <version>2.6</version>
    </dependency>

    <dependency>
      <groupId>commons-cli</groupId>
      <artifactId>commons-cli</artifactId>
      <version>1.3.1</version>
    </dependency>

    <dependency>
      <groupId>wsdl4j</groupId>
      <artifactId>wsdl4j</artifactId>
      <version>1.6.3</version>
    </dependency>

    <dependency>
      <groupId>org.apache.xmlbeans</groupId>
      <artifactId>xmlbeans</artifactId>
      <version>3.1.1-sb-fixed</version>
    </dependency>

    <dependency>
      <groupId>org.apache.xmlbeans</groupId>
      <artifactId>xmlbeans-xpath</artifactId>
      <version>2.6.0</version>
    </dependency>

    <dependency>
      <groupId>javax.xml.stream</groupId>
      <artifactId>stax-api</artifactId>
      <version>1.0-2</version>
    </dependency>

    <dependency>
      <groupId>thoughtworks</groupId>
      <artifactId>xstream</artifactId>
      <version>1.3.1</version>
    </dependency>

    <dependency>
      <groupId>com.google.inject</groupId>
      <artifactId>guice</artifactId>
      <version>7.0.0</version>
    </dependency>

    <dependency>
      <groupId>com.fathzer</groupId>
      <artifactId>javaluator</artifactId>
      <version>3.0.4</version>
    </dependency>

    <dependency>
      <groupId>com.fifesoft</groupId>
      <artifactId>rsyntaxtextarea</artifactId>
      <version>3.4.1</version>
    </dependency>

    <dependency>
      <groupId>amf</groupId>
      <artifactId>flex-messaging-common</artifactId>
      <version>1.0</version>
    </dependency>

    <dependency>
      <groupId>amf</groupId>
      <artifactId>flex-messaging-core</artifactId>
      <version>1.0</version>
    </dependency>

    <dependency>
      <groupId>com.github.markusbernhardt</groupId>
      <artifactId>proxy-vole</artifactId>
      <version>1.0.4</version>
      <exclusions>
        <exclusion>
          <groupId>net.java.dev.jna</groupId>
          <artifactId>jna-platform</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>javax.jms</groupId>
      <artifactId>jms</artifactId>
      <version>1.1</version>
    </dependency>

    <dependency>
      <groupId>javax.mail</groupId>
      <artifactId>mail</artifactId>
      <version>1.4</version>
    </dependency>

    <!-- ready api core -->
    <dependency>
      <groupId>net.engio</groupId>
      <artifactId>mbassador</artifactId>
      <version>1.1.10</version>
    </dependency>

    <dependency>
      <groupId>xmlunit</groupId>
      <artifactId>xmlunit</artifactId>
      <version>1.2</version>
    </dependency>

    <dependency>
      <groupId>saxon</groupId>
      <artifactId>saxon</artifactId>
      <version>9.1.0.8j</version>
    </dependency>

    <dependency>
      <groupId>saxon</groupId>
      <artifactId>saxon-dom</artifactId>
      <version>9.1.0.8j</version>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>5.13.0</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.apache.groovy</groupId>
      <artifactId>groovy-test</artifactId>
      <version>${groovy.version}</version>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.gmavenplus</groupId>
        <artifactId>gmavenplus-plugin</artifactId>
        <version>4.0.1</version>
        <executions>
          <execution>
            <goals>
              <goal>addSources</goal>
              <goal>compile</goal>
              <goal>addTestSources</goal>
              <goal>compileTests</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <targetBytecode>11</targetBytecode>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.2.3</version>
        <configuration>
          <includes>
            <include>com.thy.qa.td4p.baseclasses.**</include>
            <include>com.thy.qa.td4p.request.**</include>
            <include>com.thy.qa.td4p.parser.**</include>
            <include>com.thy.qa.td4p.teststep.**</include>
            <include>com.thy.qa.td4p.passenger.**</include>
          </includes>
          <parallel>classes</parallel>
          <threadCount>20</threadCount>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>3.3.0</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>0.8.8</version>
        <executions>
          <execution>
            <id>default-prepare-agent</id>
            <phase>compile</phase>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <!-- attached to Maven test phase -->
          <execution>
            <id>default-report</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

</project>
