package com.thy.qa.td4p.dcsCheckin

import com.thy.qa.td4p.environment.AWSEnvironment
import com.thy.qa.td4p.environment.TroyaEnvironment
import org.junit.Test

class CheckInFlightCreator_Test {

    @Test
    void test_createFlightSegmentsByRoute() {
        List destinations = ['LAX', 'JFK', 'AYT', 'ESB', 'DIY', 'ADB', 'CDG', 'FRA', 'AMS', 'TZX']
        assert destinations.any {
            new CheckInFlightCreator().environment(AWSEnvironment.WSPREPROD01).environment(TroyaEnvironment.WEBT).createFlightsByRoute('IST', it).originDestinationOptions?.size()
        }
    }

}
