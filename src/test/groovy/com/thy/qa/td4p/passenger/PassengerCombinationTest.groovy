package com.thy.qa.td4p.passenger

import org.junit.Test

class PassengerCombinationTest {

    @Test
    void no_duplicate_names() {
        PassengerCombination passengerCombination = new PassengerCombination(5, 3, 5)
        assert passengerCombination.getPax().unique(true) { a -> a.name + " " + a.surname }.size() == 13
    }

    @Test
    void addPassenger_method() {
        PassengerCombination passengerCombination = new PassengerCombination()
        passengerCombination.addPassenger(PassengerCode.ADULT, 2)
        passengerCombination.addPassenger(PassengerCode.CHILD, 2)
        passengerCombination.addPassenger(PassengerCode.INFANT, 2)
        assert passengerCombination.getPax().size() == 6
    }

    @Test
    void should_access_to_internal_map_in_concurrent_threads() {
        PassengerCombination passengerCombination = new PassengerCombination()
        Map<String, String> map = [ADULT : "2",
                                   CHILD : "2",
                                   INFANT: "2"]
        map.entrySet().stream().parallel().forEach { entry ->
            passengerCombination.addPassenger(PassengerCode.valueOf(entry.key), Integer.parseInt(entry.value))
        }
    }

    @Test
    void map_constructor() {
        PassengerCombination passengerCombination = new PassengerCombination(["adult" : 2,
                                                                              "child" : 2,
                                                                              "infant": 2])
        assert passengerCombination.getPax().size() == 6
    }

    @Test
    void constructor_with_passenger_list() {
        Passenger passenger1 = new Passenger(PassengerCode.ADULT).with(true) {
            name = "John"
            surname = "Doe"
        }
        Passenger passenger2 = new Passenger(PassengerCode.CHILD).with(true) {
            name = "Jane"
            surname = "Doe"
        }
        Passenger passenger3 = new Passenger(PassengerCode.INFANT).with(true) {
            name = "Baby"
            surname = "Doe"
        }
        List<Passenger> passengers = [passenger1, passenger2, passenger3]
        PassengerCombination passengerCombination = new PassengerCombination(passengers)
        assert passengerCombination.getPax().size() == 3
        passengerCombination.getPax()[0].with {
            assert name == "John" && surname == "Doe" && passengerCode == PassengerCode.ADULT
        }
        passengerCombination.getPax()[1].with {
            assert name == "Jane" && surname == "Doe" && passengerCode == PassengerCode.CHILD
        }
        passengerCombination.getPax()[2].with {
            assert name == "Baby" && surname == "Doe" && passengerCode == PassengerCode.INFANT
        }
    }
}
