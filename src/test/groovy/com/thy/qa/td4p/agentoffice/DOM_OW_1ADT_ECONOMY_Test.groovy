package com.thy.qa.td4p.agentoffice

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class DOM_OW_1ADT_ECONOMY_Test {

    @Test
    void test() {
        GetPnr gt = new GetPnr()
        List flights = [new Flight(origin: "AYT", destination: "IST", stopover: 0, dayToFlight: 126, fareType: FareType.ECONOMY)]
        TestCaseContext tc = new TestCaseContext(RoutingType.ONEWAY, flights, new PassengerCombination([new Passenger(PassengerCode.ADULT)]))
        tc.setChannel(Channel.QRES_XML)
        String pnrNumber = gt.getTicketPnr(tc)
        assert pnrNumber
        List ticketNos = gt.getPassengerInfo()
        gt.log.info(ticketNos)
        assert ticketNos
    }

}
