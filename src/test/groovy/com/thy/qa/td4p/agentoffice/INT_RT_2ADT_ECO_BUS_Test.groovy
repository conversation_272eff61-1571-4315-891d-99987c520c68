package com.thy.qa.td4p.agentoffice

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class INT_RT_2ADT_ECO_BUS_Test {

    @Test
    void test() {
        GetPnr gt = new GetPnr()
        List flights = [new Flight(origin: "BCN", destination: "IST", dayToFlight: 83, fareType: FareType.ECONOMY),
                        new Flight(origin: "IST", destination: "BCN", dayToFlight: 92, fareType: FareType.BUSINESS)]
        PassengerCombination passengerCombination = new PassengerCombination(adult: 2)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.setChannel(Channel.QRES_XDE)
        String pnrNumber = gt.getTicketPnr(testCaseContext)
        assert pnrNumber
        List ticketNos = gt.getPassengerInfo()
        gt.log.info(ticketNos)
        assert ticketNos
    }
}