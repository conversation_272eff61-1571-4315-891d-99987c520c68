package com.thy.qa.td4p.baseclasses

import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.BrandCode
import org.junit.Test

import java.time.LocalDateTime

class FlightSegment_Test {

    @Test
    void flightSegmentTypeChecksOnCasting() {
        FlightSegment flightSegment = [
                fareBasisCode           : 'M',
                resBookDesigCode        : 'Y',
                brandCode               : 'FX',
                departureAirport        : 'JFK',
                arrivalAirport          : 'IST',
                operatingAirlineCode    : 'TK',
                flightNumber            : '4',
                departureDateTime       : '2022-05-24T12:45:00',
                arrivalDateTime         : '2022-05-25T05:20:00',
                validConnectionIndicator: 'false',
        ]
        assert flightSegment.operatingAirlineCode instanceof AirlineCode
        assert flightSegment.validConnectionIndicator instanceof Boolean
        assert !flightSegment.validConnectionIndicator
        assert flightSegment.departureDateTime instanceof LocalDateTime
        assert flightSegment.arrivalDateTime instanceof LocalDateTime
        assert flightSegment.brandCode instanceof BrandCode
    }

}
