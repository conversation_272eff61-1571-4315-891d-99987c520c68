package com.thy.qa.td4p.msmembership

import com.thy.qa.td4p.environment.BWSEnvironment
import com.thy.qa.td4p.environment.MSServicesEnvironment
import com.thy.qa.td4p.environment.TroyaEnvironment
import com.thy.qa.td4p.membership.DiscountType
import com.thy.qa.td4p.membership.LoginPreferenceType
import com.thy.qa.td4p.membership.MSMembership
import com.thy.qa.td4p.membership.MembershipType
import com.thy.qa.td4p.membership.context.*
import org.junit.Test

import java.time.LocalDate
import java.time.Period

class MSMembership_Test {

    @Test
    void createRefreshUpdateAddCompanionsMembership1() {
        MSMembership membershipInfo = new MSMembership().environment(MSServicesEnvironment.WSKURUMSALTEST)
        CreateMembershipContext createMembershipContext = new CreateMembershipContext()
        createMembershipContext.setPinNumber('180618')
        String msNo = membershipInfo.createMembership(createMembershipContext)
        assert msNo

        assert membershipInfo.refreshMembership(msNo)

        UpdateMembershipContext updateMembershipContext = new UpdateMembershipContext(msNo, DiscountType.TEACHER)
        assert membershipInfo.updateMembership(updateMembershipContext)

        AddCompanionsContext addCompanionsContext = new AddCompanionsContext(msNo, 2, 2, 2)
        assert membershipInfo.addCompanions(addCompanionsContext)
    }

    @Test
    void createRefreshAddCompanions2() {
        MSMembership membershipInfo = new MSMembership().environment(MSServicesEnvironment.WSCRMUAT)
        CreateMembershipContext createMembershipContext = new CreateMembershipContext()
        String msNo = membershipInfo.createMembership(createMembershipContext)
        assert msNo

        assert membershipInfo.refreshMembership(msNo)

        AddCompanionsContext addCompanionsContext = new AddCompanionsContext(msNo, 4, 0, 0)
        assert new MSMembership().environment(MSServicesEnvironment.WSKURUMSALTEST).addCompanions(addCompanionsContext)
    }

    @Test
    void createRefreshUpdateLoginPreferences3() {
        MSMembership membershipInfo = new MSMembership().environment(MSServicesEnvironment.WSCRMUAT)
        CreateMembershipContext createMembershipContext = new CreateMembershipContext()
        String msNo = membershipInfo.createMembership(createMembershipContext)
        assert msNo

        assert membershipInfo.refreshMembership(msNo)

        LoginPreferencesContext loginPreferencesContext = new LoginPreferencesContext(msNo, [LoginPreferenceType.MOBILE_NUMBER])
        assert new MSMembership().environment(MSServicesEnvironment.WSKURUMSALTEST).updateLoginPreferences(loginPreferencesContext)
    }

    @Test
    void createRefreshUpdateLoginPreferences4() {
        MSMembership membershipInfo = new MSMembership().environment(MSServicesEnvironment.WSCRMUAT)
        CreateMembershipContext createMembershipContext = new CreateMembershipContext()
        String msNo = membershipInfo.createMembership(createMembershipContext)
        assert msNo

        assert membershipInfo.refreshMembership(msNo)

        List<LoginPreferenceType> loginPreferenceTypes = [LoginPreferenceType.EMAIL_ADDRESS, LoginPreferenceType.MOBILE_NUMBER]
        LoginPreferencesContext loginPreferencesContext = new LoginPreferencesContext(msNo, loginPreferenceTypes)
        assert new MSMembership().environment(MSServicesEnvironment.WSKURUMSALTEST).updateLoginPreferences(loginPreferencesContext)
    }

    @Test
    void createRefreshUpdateMembership5() {
        MSMembership membershipInfo = new MSMembership()
        CreateMembershipContext createMembershipContext = new CreateMembershipContext()
        createMembershipContext.setBirthDate(LocalDate.now() - Period.ofYears(30))
        String msNo = membershipInfo.createMembership(createMembershipContext)
        assert msNo

        assert membershipInfo.refreshMembership(msNo)

        UpdateMembershipContext updateMembershipContext = new UpdateMembershipContext(msNo, DiscountType.STUDENT)
        assert membershipInfo.updateMembership(updateMembershipContext)
    }

    @Test
    void createRefreshUpdateMembership6() {
        MSMembership membershipInfo = new MSMembership()
        CreateMembershipContext createMembershipContext = new CreateMembershipContext(MembershipType.CHILD)
        String msNo = membershipInfo.createMembership(createMembershipContext)
        assert msNo

        assert membershipInfo.refreshMembership(msNo)

        UpdateMembershipContext updateMembershipContext = new UpdateMembershipContext(msNo, DiscountType.DISABLED)
        assert membershipInfo.updateMembership(updateMembershipContext)
    }

    @Test
    void createMembership6() {
        MSMembership membershipInfo = new MSMembership()
        CreateMembershipContext createMembershipContext = new CreateMembershipContext()
        assert membershipInfo.createMembership(createMembershipContext)
    }


    @Test
    void refreshMembership1() {
        MSMembership membershipInfo = new MSMembership()
        CreateMembershipContext createMembershipContext = new CreateMembershipContext()
        String msNo = membershipInfo.createMembership(createMembershipContext)

        assert membershipInfo.refreshMembership(msNo)
    }

    @Test
    void refreshMembership2() {
        MSMembership membershipInfo = new MSMembership()
        CreateMembershipContext createMembershipContext = new CreateMembershipContext()
        String msNo = membershipInfo.createMembership(createMembershipContext)

        assert new MSMembership().environment(BWSEnvironment.WSDEV01).refreshMembership(msNo)
    }

    @Test
    void refreshMembership3() {
        MSMembership membershipInfo = new MSMembership()
        CreateMembershipContext createMembershipContext = new CreateMembershipContext()
        String msNo = membershipInfo.createMembership(createMembershipContext)

        assert new MSMembership().retryCount(2).refreshMembership(msNo)
    }

    @Test
    void refreshMembership4() {
        MSMembership membershipInfo = new MSMembership()
        CreateMembershipContext createMembershipContext = new CreateMembershipContext()
        String msNo = membershipInfo.createMembership(createMembershipContext)

        MSMembership refresh = new MSMembership().environment(BWSEnvironment.WSPREPROD01).environment(TroyaEnvironment.GTEST).retryCount(8)
        assert refresh.refreshMembership(msNo)
    }


    @Test
    void loadMiles1() {
        MSMembership membershipInfo = new MSMembership().environment(MSServicesEnvironment.WSCRMUAT)
        LoadMilesContext loadMilesContext = new LoadMilesContext("TK451880717")
        assert membershipInfo.loadMiles(loadMilesContext)
    }

    @Test
    void loadMiles3() {
        MSMembership membershipInfo = new MSMembership().retryCount(0)
        LoadMilesContext membershipContext = new LoadMilesContext("TK264686253")
        assert membershipInfo.loadMiles(membershipContext)
    }

    @Test
    void loadMiles4() {
        MSMembership membershipInfo = new MSMembership().environment(MSServicesEnvironment.WSKURUMSALTEST)
        LoadMilesContext loadMilesContext = new LoadMilesContext("TK264686253")
        loadMilesContext.setRequiredMiles(50001)
        assert membershipInfo.loadMiles(loadMilesContext)
    }

}
