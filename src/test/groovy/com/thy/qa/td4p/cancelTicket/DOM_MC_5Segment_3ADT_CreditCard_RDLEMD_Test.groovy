package com.thy.qa.td4p.cancelTicket

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.cancellation.ConvertToTravelCoupon
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Ignore
import org.junit.Test

class DOM_MC_5Segment_3ADT_CreditCard_RDLEMD_Test {

    @Ignore
    @Test
    void test() {
        GetPnr gt = new GetPnr()
        List flights = [new Flight(origin: "ESB", destination: "IST", dayToFlight: 10, fareType: FareType.ECONOMY),
                        new Flight(origin: "IST", destination: "AYT", dayToFlight: 12, fareType: FareType.ECONOMY),
                        new Flight(origin: "AYT", destination: "IST", dayToFlight: 15, fareType: FareType.ECONOMY),
                        new Flight(origin: "IST", destination: "ADB", dayToFlight: 17, fareType: FareType.ECONOMY),
                        new Flight(origin: "ESB", destination: "IST", dayToFlight: 19, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination(3, 0, 0)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)

        String pnrNumber = gt.getTicketPnr(testCaseContext)
        assert pnrNumber
        String surname = gt.getSurname()

        ConvertToTravelCoupon ge = new ConvertToTravelCoupon()
        List rdlEmdList = ge.convertToTravelCoupons(pnrNumber, surname)
        assert rdlEmdList
    }

}
