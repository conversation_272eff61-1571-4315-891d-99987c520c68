package com.thy.qa.td4p.cancelTicket

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.cancellation.ConvertToTravelCoupon
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Ignore
import org.junit.Test

class DOM_OW_1ADT_CreditCard_RDLEMD_Test {

    @Ignore
    @Test
    void test() {
        GetPnr gt = new GetPnr()
        List flights = [new Flight(origin: "ESB", destination: "IST", dayToFlight: 1, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination(1, 0, 0)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)

        String pnrNumber = gt.getTicketPnr(testCaseContext)
        assert pnrNumber
        String surname = gt.getSurname()

        ConvertToTravelCoupon cttc = new ConvertToTravelCoupon()
        List rdlEmdList = cttc.convertToTravelCoupons(pnrNumber, surname)
        assert rdlEmdList
    }

}
