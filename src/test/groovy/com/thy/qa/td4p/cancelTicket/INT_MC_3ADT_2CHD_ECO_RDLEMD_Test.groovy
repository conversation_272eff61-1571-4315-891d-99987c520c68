package com.thy.qa.td4p.cancelTicket

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.cancellation.ConvertToTravelCoupon
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Ignore
import org.junit.Test

class INT_MC_3ADT_2CHD_ECO_RDLEMD_Test {

    @Ignore
    @Test
    void test() {
        GetPnr gt = new GetPnr().retryCount(10)
        List flights = [new Flight(origin: "IST", destination: "AMS", stopover: 0, dayToFlight: 84),
                        new Flight(origin: "AMS", destination: "IST", stopover: 0, dayToFlight: 90, fareType: FareType.ECONOMY),
                        new Flight(origin: "ESB", destination: "IST", stopover: 0, dayToFlight: 97, fareType: FareType.ECONOMY)]
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.MULTICITY, flights, new PassengerCombination(3, 2, 0))
        String pnrNumber = gt.getTicketPnr(testCaseContext)
        assert pnrNumber
        String surname = gt.getSurname()

        ConvertToTravelCoupon cttc = new ConvertToTravelCoupon()
        List rdlEmdList = cttc.convertToTravelCoupons(pnrNumber, surname)
        assert rdlEmdList

    }

}
