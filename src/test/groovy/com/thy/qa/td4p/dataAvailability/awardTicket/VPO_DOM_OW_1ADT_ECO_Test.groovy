package com.thy.qa.td4p.dataAvailability.awardTicket


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.Pnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.dataAvailability.AvailableData
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class VPO_DOM_OW_1ADT_ECO_Test {

    @Test
    void test() {
        String firstPassengerMsNo = "TK002934055"
        List flights = [new Flight(origin: "TZX", destination: "ADB", stopover: 1, dayToFlight: 132, fareType: FareType.BUSINESS)]
        PassengerCombination passengerCombination = new PassengerCombination([new Passenger(PassengerCode.ADULT)])
        Pnr pnr = new AvailableData().getAvailableAwardTicketData(firstPassengerMsNo, new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination))
        assert pnr.originDestinationOptions.get(0).flightSegments.get(0).getFlightNumber()
    }

}
