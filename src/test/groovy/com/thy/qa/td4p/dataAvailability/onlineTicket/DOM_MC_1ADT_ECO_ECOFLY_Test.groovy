package com.thy.qa.td4p.dataAvailability.onlineTicket


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.Pnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.dataAvailability.AvailableData
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.merchandising.request.SportsEquipment
import com.thy.qa.td4p.merchandising.request.seat.ExitSeat
import com.thy.qa.td4p.merchandising.speq.SportsEquipmentType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class DOM_MC_1ADT_ECO_ECOFLY_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "ERZ", destination: "ADB", stopover: 1, dayToFlight: 6, fareType: FareType.ECOFLY),
                        new Flight(origin: "ADB", destination: "IST", stopover: 0, dayToFlight: 15, fareType: FareType.ECOFLY),
                        new Flight(origin: "IST", destination: "COV", stopover: 0, dayToFlight: 18, fareType: FareType.ECOFLY),
                        new Flight(origin: "COV", destination: "AYT", stopover: 1, dayToFlight: 22, fareType: FareType.ECOFLY)]
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, new PassengerCombination([new Passenger(PassengerCode.ADULT)]))
        testCaseContext.addServiceRequest(new ExitSeat(1, 1))
        testCaseContext.addServiceRequest(new SportsEquipment(1, 1, 1, SportsEquipmentType.BIKE))
        Pnr pnr = new AvailableData().getAvailableOnlineTicketData(testCaseContext)
        assert pnr.originDestinationOptions.get(0).flightSegments.get(0).getFlightNumber()
    }
}
