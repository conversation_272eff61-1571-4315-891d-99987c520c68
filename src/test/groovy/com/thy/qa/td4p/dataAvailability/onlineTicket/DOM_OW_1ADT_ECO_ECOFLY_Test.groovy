package com.thy.qa.td4p.dataAvailability.onlineTicket


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.Pnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.dataAvailability.AvailableData
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class DOM_OW_1ADT_ECO_ECOFLY_Test {

    @Test
    void test() {
        List flights = [
                new Flight(origin: "ERZ", destination: "AYT", stopover: 1, dayToFlight: 6, fareType: FareType.ECOFLY)
        ]
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, new PassengerCombination([new Passenger(PassengerCode.ADULT)]))
        Pnr pnr = new AvailableData().retryCount(10).getAvailableOnlineTicketData(testCaseContext)
        assert pnr.originDestinationOptions.get(0).flightSegments.get(0).getFlightNumber()
    }
}
