package com.thy.qa.td4p.payandfly

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class SMARTMOBILE_FreeReservationPayAndFlyPnr_DOM_OW_Connecting_1ADT_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "ADB", destination: "ESB", stopover: 1, fareType: FareType.BUSINESS, dayToFlight: 30)]
        PassengerCombination passengerCombination = new PassengerCombination()
        passengerCombination.addPassenger(PassengerCode.ADULT, 1)
        passengerCombination.getPax().get(0).setMsNo("TK001221704")
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)
        testCaseContext.setChannel(Channel.SMARTMOBILE)
        String pnrNumber = new GetPnr().getFreeReservationPayAndFlyTicketPnr(testCaseContext)
        assert pnrNumber
    }

}