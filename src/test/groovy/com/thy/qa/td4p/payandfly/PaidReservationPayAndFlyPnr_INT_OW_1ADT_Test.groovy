package com.thy.qa.td4p.payandfly


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class PaidReservationPayAndFlyPnr_INT_OW_1ADT_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "IST", destination: "BCN", dayToFlight: 15, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination([new Passenger(PassengerCode.ADULT)])
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)
        String pnrNumber = new GetPnr().getPaidReservationPayAndFlyTicketPnr(testCaseContext)
        assert pnrNumber
    }

}