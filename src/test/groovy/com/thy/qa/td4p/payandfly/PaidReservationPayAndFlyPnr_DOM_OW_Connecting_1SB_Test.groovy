package com.thy.qa.td4p.payandfly

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class PaidReservationPayAndFlyPnr_DOM_OW_Connecting_1SB_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "ESB", destination: "ADB", stopover: 1, dayToFlight: 20, fareType: FareType.BUSINESS)]
        PassengerCombination passengerCombination = new PassengerCombination()
        passengerCombination.addPassenger(PassengerCode.DISABLED, 1)
        passengerCombination.getPax().get(0).setMsNo("TK420542414")
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)
        String pnrNumber = new GetPnr().getPaidReservationPayAndFlyTicketPnr(testCaseContext)
        assert pnrNumber
    }

}