package com.thy.qa.td4p.payandfly

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class SMARTMOBILE_FreeReservationPayAndFlyPnr_DOM_RT_2ADT_2CHD_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "ESB", destination: "IST", stopover: 0, dayToFlight: 130, fareType: FareType.ECONOMY),
                        new Flight(origin: "IST", destination: "ESB", stopover: 0, dayToFlight: 145, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination(2, 2, 0)
        passengerCombination.getPax().get(0).setMsNo("TK001221704")
        passengerCombination.getPax().get(1).setMsNo("TK490786391")
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.setChannel(Channel.SMARTMOBILE)
        String pnrNumber = new GetPnr().getFreeReservationPayAndFlyTicketPnr(testCaseContext)
        assert pnrNumber
    }

}