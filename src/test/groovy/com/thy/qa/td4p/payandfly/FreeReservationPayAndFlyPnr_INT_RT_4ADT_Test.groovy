package com.thy.qa.td4p.payandfly

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class FreeReservationPayAndFlyPnr_INT_RT_4ADT_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "JFK", destination: "IST", dayToFlight: 30, fareType: FareType.ECONOMY),
                        new Flight(origin: "IST", destination: "JFK", dayToFlight: 35, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination()
        passengerCombination.addPassenger(PassengerCode.ADULT, 4)
        passengerCombination.getPax().get(0).setMsNo("TK113970982")
        passengerCombination.getPax().get(1).setMsNo("TK490786391")
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        String pnrNumber = new GetPnr().getFreeReservationPayAndFlyTicketPnr(testCaseContext)
        assert pnrNumber
    }

}