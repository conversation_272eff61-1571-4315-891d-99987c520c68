package com.thy.qa.td4p.payandfly

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class PaidReservationPayAndFlyPnr_DOM_RT_Connecting_2ADT_1INF_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "ESB", destination: "ADB", stopover: 1, dayToFlight: 20, fareType: FareType.BUSINESS),
                        new Flight(origin: "ADB", destination: "ESB", stopover: 1, dayToFlight: 25, fareType: FareType.BUSINESS)]
        PassengerCombination passengerCombination = new PassengerCombination(2, 0, 1)
        passengerCombination.getPax().get(0).setMsNo("TK001221704")
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        String pnrNumber = new GetPnr().getPaidReservationPayAndFlyTicketPnr(testCaseContext)
        assert pnrNumber
    }

}