package com.thy.qa.td4p.payandfly

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class PaidReservationPayAndFlyPnr_INT_MC_2ADT_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "LHR", destination: "IST", dayToFlight: 30, fareType: FareType.ANY),
                        new Flight(origin: "IST", destination: "ADB", dayToFlight: 33, fareType: FareType.ANY)]
        PassengerCombination passengerCombination = new PassengerCombination()
        passengerCombination.addPassenger(PassengerCode.ADULT, 2)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.MULTICITY, flights, passengerCombination)
        String pnrNumber = new GetPnr().getPaidReservationPayAndFlyTicketPnr(testCaseContext)
        assert pnrNumber
    }

}