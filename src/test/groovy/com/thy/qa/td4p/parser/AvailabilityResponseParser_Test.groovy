package com.thy.qa.td4p.parser

import com.thy.qa.td4p.baseclasses.OriginDestinationOption
import com.thy.qa.td4p.enums.AirlineCode
import org.junit.Test

import java.time.LocalDateTime

class AvailabilityResponseParser_Test {

    @Test
    void international_availability_response_parser_test() {
        List originDestinationOptions1 = [
                new OriginDestinationOption(
                        [[fareBasisCode  : 'M', resBookDesigCode: 'Y', brandCode: 'FX', departureAirport: 'JFK',
                          arrivalAirport : 'IST', airlineCode: AirlineCode.TK, operatingAirlineCode: AirlineCode.TK,
                          flightNumber   : '4', departureDateTime: LocalDateTime.parse('2022-05-24T12:45:00'),
                          arrivalDateTime: LocalDateTime.parse('2022-05-25T05:20:00'), validConnectionIndicator: false,
                         ]]
                ),
                new OriginDestinationOption(
                        [[fareBasisCode  : 'T', resBookDesigCode: 'Y', brandCode: 'FX', departureAirport: 'EWR',
                          arrivalAirport : 'YYZ', airlineCode: AirlineCode.AC, operatingAirlineCode: 'AC',
                          flightNumber   : '8881', departureDateTime: LocalDateTime.parse('2022-05-24T14:50:00'),
                          arrivalDateTime: LocalDateTime.parse('2022-05-24T16:25:00'), validConnectionIndicator: true,
                         ],
                         [fareBasisCode  : 'Y', resBookDesigCode: 'Y', brandCode: 'RS', departureAirport: 'YYZ',
                          arrivalAirport : 'IST', airlineCode: AirlineCode.TK, operatingAirlineCode: 'TK',
                          flightNumber   : '18', departureDateTime: LocalDateTime.parse('2022-05-24T22:20:00'),
                          arrivalDateTime: LocalDateTime.parse('2022-05-25T15:10:00'), validConnectionIndicator: false,
                         ]]
                )
        ]
        String response = this.class.getResourceAsStream("/response/InternationalAvailabilityResponse.xml").text
        List originDestinationOptions = new AvailabilityResponseParser(response).parseOriginDestinationOptions(0)
        originDestinationOptions.eachWithIndex { originDestinationOption, optionIndex ->
            originDestinationOption.flightSegments.eachWithIndex { flightSegment, segmentIndex ->
                assert flightSegment.departureAirport == originDestinationOptions1[optionIndex].flightSegments[segmentIndex].departureAirport
                assert flightSegment.arrivalAirport == originDestinationOptions1[optionIndex].flightSegments[segmentIndex].arrivalAirport
                assert flightSegment.departureDateTime == originDestinationOptions1[optionIndex].flightSegments[segmentIndex].departureDateTime
                assert flightSegment.arrivalDateTime == originDestinationOptions1[optionIndex].flightSegments[segmentIndex].arrivalDateTime
                assert flightSegment.airlineCode == originDestinationOptions1[optionIndex].flightSegments[segmentIndex].airlineCode
                assert flightSegment.operatingAirlineCode == originDestinationOptions1[optionIndex].flightSegments[segmentIndex].operatingAirlineCode
                assert flightSegment.flightNumber == originDestinationOptions1[optionIndex].flightSegments[segmentIndex].flightNumber
                assert flightSegment.fareBasisCode == originDestinationOptions1[optionIndex].flightSegments[segmentIndex].fareBasisCode
                assert flightSegment.resBookDesigCode == originDestinationOptions1[optionIndex].flightSegments[segmentIndex].resBookDesigCode
                assert flightSegment.brandCode == originDestinationOptions1[optionIndex].flightSegments[segmentIndex].brandCode
                assert flightSegment.validConnectionIndicator == originDestinationOptions1[optionIndex].flightSegments[segmentIndex].validConnectionIndicator
            }
        }
    }

}
