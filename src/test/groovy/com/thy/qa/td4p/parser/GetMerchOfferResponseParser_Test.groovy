package com.thy.qa.td4p.parser

import com.thy.qa.td4p.merchandising.offer.AncillaryItemType
import com.thy.qa.td4p.merchandising.offer.Offer
import com.thy.qa.td4p.merchandising.offer.service.FreeReservationService
import com.thy.qa.td4p.merchandising.offer.service.ServiceCategory
import org.junit.Test

import java.time.Duration
import java.time.temporal.ChronoUnit

class GetMerchOfferResponseParser_Test {
    @Test
    void parseReservationOptions() {
        String response = '''<S:Envelope xmlns:env="http://schemas.xmlsoap.org/soap/envelope/" xmlns:S="http://schemas.xmlsoap.org/soap/envelope/">
    <env:Header/>
    <S:Body>
        <ns0:getMerchOfferResponse xmlns:ns0="http://service.thy.com/" xmlns:ns1="http://www.thy.com/ws/requestHeader"
                                   xmlns:ns2="http://www.thy.com/merch/service/v1/service"
                                   xmlns:ns3="http://www.thy.com/merch/service/v1/commons/cp"
                                   xmlns:ns4="http://www.thy.com/merch/service/v1/offer"
                                   xmlns:ns5="http://www.thy.com/ws/responseHeader">
            <ns0:getMerchOfferOtaResponse>
                <offerResponse>
                    <ns2:Header ResponseID="AC3DD908-28A9-4133-9EDF-AEDB261ED694"
                                RequestDT="2022-07-19T12:41:49.214+03:00" ResponseDT="2022-07-19T12:41:49.246+03:00"/>
                    <ns2:Result Status="OK" Code="00" Reason="Successful">
                        <ns2:Warnings>
                            <ns3:Description>
                                <ns3:Code>W102</ns3:Code>
                                <ns3:DescText>Inclusive Distribution Rule Not Found, Distribution Is Not Allowed
                                </ns3:DescText>
                                <ns3:DescKind>Warning</ns3:DescKind>
                                <ns3:PaxIndex>1</ns3:PaxIndex>
                                <ns3:ServiceKind Category="OPT_USDOT"/>
                            </ns3:Description>
                        </ns2:Warnings>
                    </ns2:Result>
                    <ns2:PricedOffer OfferID="01VDV76K19TVQ" ParentID="AC3DD908-28A9-4133-9EDF-AEDB261ED694" Owner="TK"
                                     TS="2022-07-19T12:41:49.214+03:00">
                        <ns4:OfferItem OfferItemID="01VDV76K19TVQ01" ParentID="01VDV76K19TVQ" MandatoryInd="true">
                            <ns4:Service ServiceID="01VDV76K19TVQ0101" ParentID="01VDV76K19TVQ01">
                                <ns4:ServiceKind Code="OPT_MS_48" Category="OPT_MS" Rfic="D" Rfisc="04C" Group="TS"
                                                 SubGroup="FG"/>
                                <ns4:PassengerRef>1</ns4:PassengerRef>
                                <ns4:PriceDetail>
                                    <ns3:NonChargeableReason>FREQUENT_FLYER</ns3:NonChargeableReason>
                                </ns4:PriceDetail>
                                <ns4:Specification SpecificationKind="CustomerFacingService" Code="CFS">
                                    <ns4:Parameter Name="OptionDuration" ValueType="Integer" Unit="Hour" Type="Simple"
                                                   UsageType="Predefined">
                                        <ns4:Value>48</ns4:Value>
                                    </ns4:Parameter>
                                </ns4:Specification>
                            </ns4:Service>
                            <ns4:OfferType>BUNDLE</ns4:OfferType>
                        </ns4:OfferItem>
                        <ns4:TimeLimit>
                            <ns4:offerExpiration dateTime="2022-07-19T13:41:49.214+03:00"/>
                        </ns4:TimeLimit>
                    </ns2:PricedOffer>
                </offerResponse>
            </ns0:getMerchOfferOtaResponse>
            <responseHeader>
                <ns5:statusCode>SUCCESS</ns5:statusCode>
                <ns5:clientTransactionId>CTID-TD4P-190722-124148565</ns5:clientTransactionId>
                <ns5:serverTransactionId>89743829-51b9-4cd7-06f0-f5de469cde78</ns5:serverTransactionId>
            </responseHeader>
        </ns0:getMerchOfferResponse>
    </S:Body>
</S:Envelope>'''

        GetMerchOfferResponseParser parser = new GetMerchOfferResponseParser(response)
        List<Offer> offers = parser.parseOffers()
        offers.get(0).with {
            assert it.offerItemId == '01VDV76K19TVQ01'
            assert it.services.get(0).getServiceCategory() == ServiceCategory.OPT_USDOT
            assert it.getServices().get(0).duration == Duration.of(48, ChronoUnit.HOURS)
            assert it.amount == 0
            assert it.currencyCode == ''
            it.services.get(0).with { FreeReservationService service ->
                assert service.ancillaryItemType == AncillaryItemType.fromString('04C')
                assert service.passengerIndex == 1
                assert service.serviceId == '01VDV76K19TVQ0101'
            }
        }
    }
}
