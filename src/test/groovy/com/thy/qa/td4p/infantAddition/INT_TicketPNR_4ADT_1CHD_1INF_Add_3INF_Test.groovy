package com.thy.qa.td4p.infantAddition


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.InfantAdditionContext
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

import static com.thy.qa.td4p.passenger.PassengerCode.INFANT

class INT_TicketPNR_4ADT_1CHD_1INF_Add_3INF_Test {

    @Test
    void test() {
        GetPnr gt = new GetPnr()
        List flights = [new Flight(origin: "IST", destination: "BCN", dayToFlight: 6, fareType: FareType.ANY),
                        new Flight(origin: "BCN", destination: "IST", dayToFlight: 11, fareType: FareType.ANY)]
        PassengerCombination passengerCombination = new PassengerCombination(4, 1, 1)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)

        String pnrNumber = gt.getTicketPnr(testCaseContext)
        assert pnrNumber

        String surname = gt.getSurname()
        PassengerCombination passengerCombinationInfantAddition = new PassengerCombination()
        passengerCombinationInfantAddition.addPassenger(INFANT, 3)
        InfantAdditionContext infantAdditionContext = new InfantAdditionContext(passengerCombinationInfantAddition)
        String infantAddedPnr = new GetPnr().getInfantAddedPnr(pnrNumber, surname, infantAdditionContext)
        assert infantAddedPnr
    }

}
