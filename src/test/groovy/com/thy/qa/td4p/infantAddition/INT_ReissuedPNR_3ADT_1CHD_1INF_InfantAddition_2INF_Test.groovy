package com.thy.qa.td4p.infantAddition

import com.thy.qa.td4p.*
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class INT_ReissuedPNR_3ADT_1CHD_1INF_InfantAddition_2INF_Test {

    @Test
    void test() {
        GetPnr gt = new GetPnr()
        List flights = [new Flight(origin: "BCN", destination: "IST", dayToFlight: 111, fareType: FareType.BUSINESS),
                        new Flight(origin: "CDG", destination: "BCN", dayToFlight: 131, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination(3, 1, 1)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        String pnrNumber = gt.getTicketPnr(testCaseContext)
        String surname = gt.getSurname()
        assert pnrNumber

        List reissueFlights = [new Flight(origin: "SAW", destination: "CDG", dayToFlight: 138, fareType: FareType.ECONOMY)]

        ReissueContext reissueContext = new ReissueContext(RoutingType.ONEWAY, reissueFlights, [2])
        String reissuePnr = gt.getReissuedPnr(pnrNumber, surname, reissueContext)
        assert reissuePnr

        PassengerCombination passengerCombinationInfantAddition = new PassengerCombination()
        passengerCombinationInfantAddition.addPassenger(PassengerCode.INFANT, 2)
        InfantAdditionContext infantAdditionContext = new InfantAdditionContext(passengerCombinationInfantAddition)
        String infantAddedPnr = new GetPnr().getInfantAddedPnr(reissuePnr, surname, infantAdditionContext)
        assert infantAddedPnr
    }

}
