package com.thy.qa.td4p.infantAddition

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.InfantAdditionContext
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class DOM_TicketPNR_1ADT_InfantAddition_1INF_Test {

    @Test
    void test() {
        GetPnr gt = new GetPnr()
        List flights = [new Flight(origin: "ADB", destination: "IST", dayToFlight: 50, fareType: FareType.ECONOMY),
                        new Flight(origin: "IST", destination: "ADB", dayToFlight: 70, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination(1, 0, 0)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)

        String pnrNumber = gt.getTicketPnr(testCaseContext)
        assert pnrNumber

        String surname = gt.getSurname()
        PassengerCombination passengerCombinationInfantAddition = new PassengerCombination()
        passengerCombinationInfantAddition.addPassenger(PassengerCode.INFANT, 1)
        InfantAdditionContext infantAdditionContext = new InfantAdditionContext(passengerCombinationInfantAddition)
        String infantAddedPnr = new GetPnr().getInfantAddedPnr(pnrNumber, surname, infantAdditionContext)
        assert infantAddedPnr
    }

}
