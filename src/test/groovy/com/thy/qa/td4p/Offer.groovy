package com.thy.qa.td4p

import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.environment.TroyaEnvironment
import com.thy.qa.td4p.merchandising.offer.service.SeatKind
import com.thy.qa.td4p.merchandising.request.seat.ExitSeat
import com.thy.qa.td4p.merchandising.request.seat.SeatPosition
import com.thy.qa.td4p.merchandising.request.seat.StandardSeat
import com.thy.qa.td4p.merchandising.seat.GetSeatMapResponseParser
import com.thy.qa.td4p.merchandising.seat.SeatInfo
import com.thy.qa.td4p.merchandising.seat.SeatMap
import com.thy.qa.td4p.merchandising.seat.SeatRequest
import com.thy.qa.td4p.payments.CreditCardPayment
import com.thy.qa.td4p.payments.MilesPayment
import com.thy.qa.td4p.pnr.Flow
import com.thy.qa.td4p.request.soap.GetMerchOfferRequest
import com.thy.qa.td4p.request.soap.PrePaymentRequest
import com.thy.qa.td4p.request.soap.payment.PaymentInfo
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import org.junit.Test

import java.time.LocalDateTime
import java.time.YearMonth

class Offer {
    @Test
    void test() {
        PrePaymentRequest prePaymentRequest = new PrePaymentRequest(channel: Channel.WEB3,
                sessionId: "sessionId",
                troyaEnvironment: TroyaEnvironment.WEBT,
                pnrNumber: "SAHDYU5",
                surname: "Doe",
                offers: [],
                flow: Flow.CASH_TICKETING,
                paymentInfo: new PaymentInfo().payment(new CreditCardPayment(YearMonth.of(2024, 12), "4242424242", "34242")).amount(100).currencyCode("TRY").decimalCount(2))

        println(prePaymentRequest.toString())
    }

    @Test
    void milesPrePaymentRequest() {
        PrePaymentRequest prePaymentRequest = new PrePaymentRequest(channel: Channel.WEB3,
                sessionId: "sessionId",
                troyaEnvironment: TroyaEnvironment.WEBT,
                pnrNumber: "SAHDYU5",
                surname: "Doe",
                offers: [],
                flow: Flow.AWARD_TICKETING,
                paymentInfo: new PaymentInfo().payment(new MilesPayment()).amount(100).currencyCode("TRY").decimalCount(2))

        println(prePaymentRequest.toString())
    }

    @Test
    void builderTest() {
        String getSeatMapResponse = new File('getSeatMapResponse.xml').text
        SeatMap seatMap = new GetSeatMapResponseParser().parse(getSeatMapResponse)
        List<SeatInfo> bassinetSeats = seatMap.bassinetSeats()
        SeatMap seatMap1 = SeatMap.ofFlight("TK1", LocalDateTime.now().plusDays(15).at)
        println(seatMap.toString())
    }

    @Test
    void getMerchOfferRequest() {
        GetMerchOfferRequest getMerchOfferRequest = new GetMerchOfferRequest(channel: Channel.WEB3,
                sessionId: "sessionId",
                troyaEnvironment: TroyaEnvironment.WEBT,
                pnrNumber: "SAHDYU5",
                currencyCode: "TRY",
                chargeableSeatRequests: [new SeatRequest(1, 1, SeatKind.EXIT, "05F", SeatPosition.WINDOW, true)],
                serviceRequests: [new ExitSeat(1, 1, SeatPosition.WINDOW), new StandardSeat(2, 2, SeatPosition.AISLE)],
                originDestinationOptions: [])
        GPathResult xml = new XmlSlurper().parseText(getMerchOfferRequest.toString())
        assert xml.depthFirst().findAll { it.name() == 'ServiceRequest' }.size() == 1
    }

    @Test
    void seatMapTest() {
        GetSeatMapResponseParser parser = new GetSeatMapResponseParser()
        File file = new File("map.xml")
        SeatMap seatMap = parser.parse(file.text)
        println(seatMap)
    }
}
