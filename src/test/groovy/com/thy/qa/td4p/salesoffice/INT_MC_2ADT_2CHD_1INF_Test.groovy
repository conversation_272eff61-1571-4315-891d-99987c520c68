package com.thy.qa.td4p.salesoffice

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class INT_MC_2ADT_2CHD_1INF_Test {

    @Test
    void test() {
        GetPnr gt = new GetPnr()
        List flights = [new Flight(origin: "IST", destination: "LIS", dayToFlight: 52),
                        new Flight(origin: "LIS", destination: "IST", dayToFlight: 62),
                        new Flight(origin: "IST", destination: "ADB", dayToFlight: 67)]
        PassengerCombination passengerCombination = new PassengerCombination(adult: 2, child: 2, infant: 1)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.MULTICITY, flights, passengerCombination)
        testCaseContext.setChannel(Channel.QRES_QXZ)
        testCaseContext.setContactInfo("Orçun Balcılar", TestCaseContext.contactEmail(), TestCaseContext.contactMobilePhoneNumber())
        String pnrNumber = gt.getTicketPnr(testCaseContext)
        assert pnrNumber
        List ticketNos = gt.getPassengerInfo()
        gt.log.info(ticketNos)
        assert ticketNos
    }
}