package com.thy.qa.td4p.salesoffice

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class INT_RT_2ADT_BUS_BUS_WithMsNos_Test {

    @Test
    void test() {
        GetPnr gt = new GetPnr()
        List flights = [new Flight(origin: "IST", destination: "CDG", dayToFlight: 130, fareType: FareType.BUSINESS),
                        new Flight(origin: "CDG", destination: "IST", dayToFlight: 135, fareType: FareType.BUSINESS)]
        PassengerCombination passengerCombination = new PassengerCombination()
        passengerCombination.addPassenger(PassengerCode.ADULT, 2)
        passengerCombination.getPax().get(1).setMsNo("TK002934055")
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.setChannel(Channel.QRES_FRA)
        String pnrNumber = gt.getTicketPnr(testCaseContext)
        assert pnrNumber
        List ticketNos = gt.getPassengerInfo()
        gt.log.info(ticketNos)
        assert ticketNos
    }

}
