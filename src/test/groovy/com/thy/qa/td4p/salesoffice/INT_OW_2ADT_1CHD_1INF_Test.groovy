package com.thy.qa.td4p.salesoffice

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class INT_OW_2ADT_1CHD_1INF_Test {

    @Test
    void test() {
        GetPnr gt = new GetPnr()
        List flights = [new Flight(origin: "BCN", destination: "ADB", stopover: 1, dayToFlight: 126)]
        TestCaseContext tc = new TestCaseContext(RoutingType.ONEWAY, flights, new PassengerCombination(adult: 2, child: 1, infant: 1))
        tc.setChannel(Channel.QRES_QGD)
        String pnrNumber = gt.getTicketPnr(tc)
        assert pnrNumber
        List ticketNos = gt.getPassengerInfo()
        gt.log.info(ticketNos)
        assert ticketNos
    }

}
