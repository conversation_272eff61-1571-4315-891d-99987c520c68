package com.thy.qa.td4p.checkinPnr


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.dcsCheckin.CheckinContext
import com.thy.qa.td4p.dcsCheckin.CheckinPnr
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.FlightHour
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class INT_OW_3ADT_MORETHAN6HOURS_CheckIn_Test {

    @Test
    void test() {
        GetPnr getPnr = new GetPnr()
        PassengerCombination passengerCombination = new PassengerCombination([
                new Passenger(PassengerCode.ADULT),
                new Passenger(PassengerCode.ADULT),
                new Passenger(PassengerCode.ADULT)
        ])
        passengerCombination.getPax().get(0).setName("Muhammet")
        passengerCombination.getPax().get(1).setName("Ahmet")
        passengerCombination.getPax().get(2).setName("Mehmet")
        passengerCombination.getPax().get(0).setSurname("AYTEKIN")
        passengerCombination.getPax().get(1).setSurname("AYTEKIN")
        passengerCombination.getPax().get(2).setSurname("AYTEKIN")
        List flights = [
                new Flight(origin: "IST", destination: "CDG", stopover: 0, dayToFlight: 0, fareType: FareType.ECONOMY)
        ]
        List flightHours = [FlightHour.MORETHAN6HOURS]

        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)

        String pnrNumber = getPnr.getTicketPnrForCheckin(testCaseContext, flightHours)
        String surname = getPnr.surname
        assert pnrNumber

        CheckinPnr checkinPnr = new CheckinPnr()
        CheckinContext checkinContext = new CheckinContext([1, 2, 3], 1)
        checkinPnr.doCheckinPnr(pnrNumber, surname, checkinContext)
        assert checkinPnr.getFailureReason() == null
    }

}
