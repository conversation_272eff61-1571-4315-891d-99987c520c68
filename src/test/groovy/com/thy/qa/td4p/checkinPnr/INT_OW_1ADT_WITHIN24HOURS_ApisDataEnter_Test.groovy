package com.thy.qa.td4p.checkinPnr


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.dcsCheckin.CheckinContext
import com.thy.qa.td4p.dcsCheckin.CheckinPnr
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.FlightHour
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class INT_OW_1ADT_WITHIN24HOURS_ApisDataEnter_Test {

    @Test
    void test() {
        GetPnr getPnr = new GetPnr()
        PassengerCombination passengerCombination = new PassengerCombination([new Passenger(PassengerCode.ADULT),
                                                                              new Passenger(PassengerCode.INFANT)])
        passengerCombination.getPax().get(0).setName("Muhammet")
        passengerCombination.getPax().get(0).setSurname("AYTEKIN")
        List flights = [new Flight(origin: "AYT", destination: "BCN", stopover: 1, dayToFlight: 0, fareType: FareType.ECONOMY)]
        List flightHours = [FlightHour.WITHIN24HOURS]

        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)

        String pnrNumber = getPnr.getTicketPnrForCheckin(testCaseContext, flightHours)
        String surname = getPnr.surname
        assert pnrNumber

        CheckinPnr checkinPnr = new CheckinPnr()
        CheckinContext checkinContext = new CheckinContext(2, [1])
        checkinPnr.enterAPIDocs(pnrNumber, surname, checkinContext)
        assert checkinPnr.getFailureReason() == null
    }

}
