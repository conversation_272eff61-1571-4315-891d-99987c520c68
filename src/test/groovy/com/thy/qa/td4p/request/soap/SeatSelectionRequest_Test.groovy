package com.thy.qa.td4p.request.soap

import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.environment.TroyaEnvironment
import com.thy.qa.td4p.merchandising.offer.service.SeatKind
import com.thy.qa.td4p.merchandising.request.seat.SeatPosition
import com.thy.qa.td4p.merchandising.seat.SeatRequest
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import org.junit.Test

class SeatSelectionRequest_Test {
    @Test
    void happyPath() {
        SeatSelectionRequest seatSelectionRequest = new SeatSelectionRequest(channel: Channel.SMARTMOBILE,
                troyaEnvironment: TroyaEnvironment.WEBT,
                seatRequests: [new SeatRequest(1, 1, SeatKind.NORMAL, '1A', SeatPosition.WINDOW, false),
                               new SeatRequest(3, 2, SeatKind.NORMAL, '1B', SeatPosition.AISLE, false),
                               new SeatRequest(4, 2, SeatKind.NORMAL, '1C', SeatPosition.MIDDLE, false)],
                pnrNumber: '123456',
                surname: 'TEST')
        GPathResult request = new XmlSlurper().parseText(seatSelectionRequest.toString())
        assert request.depthFirst().find { GPathResult node -> node.name() == 'SeatRequest' && node.@RowNumber == '1' && node.@SeatInRow == 'A' && node.@SeatPreference == 'WINDOW' && node.@FlightRefNumberRPHList == '1' && node.@TravelerRefNumberRPHList == '1' }
        assert request.depthFirst().find { GPathResult node -> node.name() == 'SeatRequest' && node.@RowNumber == '1' && node.@SeatInRow == 'B' && node.@SeatPreference == 'AISLE' && node.@FlightRefNumberRPHList == '2' && node.@TravelerRefNumberRPHList == '3' }
        assert request.depthFirst().find { GPathResult node -> node.name() == 'SeatRequest' && node.@RowNumber == '1' && node.@SeatInRow == 'C' && node.@SeatPreference == 'MIDDLE' && node.@FlightRefNumberRPHList == '2' && node.@TravelerRefNumberRPHList == '4' }
        assert request.depthFirst().find { GPathResult node -> node.name() == 'BookingReferenceID' && node.@ID == '123456' }
    }
}
