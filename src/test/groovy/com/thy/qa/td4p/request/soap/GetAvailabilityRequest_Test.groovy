package com.thy.qa.td4p.request.soap


import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import org.junit.Test

class GetAvailabilityRequest_Test {

    @Test
    void all_must_params() {
        String fareType = 'BUSINESS'
        List portCodes = ['IST', 'DXB']
        String msNo = 'TK002030758'
        String request = new GetAvailabilityRequest(routingType: 'ONEWAY', portCodes: portCodes, fareTypes: [fareType],
                daysToFlights: [5], passengerCodes: ['ADULT', 'ADULT', 'ADULT', 'ADULT', 'CHILD', 'CHILD'],
                msNo: msNo)
        GPathResult gPathResult = new XmlSlurper().parseText(request)
        GPathResult requestHeader = gPathResult.'**'.find { it.name() == 'requestHeader' }
        assert requestHeader.clientTransactionId.text().startsWith('CTID-TD4P')
        assert requestHeader.clientUsername.text() == 'WEB3'
        assert requestHeader.extraParameters.find { it.@key == 'SESSION_ID' }.@value.text().startsWith('SI-TD4P')
        assert requestHeader.extraParameters.find { it.@key == 'ALCS_SYSTEM' }.@value == 'WEBT'
        GPathResult oTA_AirAvailRQ = gPathResult.'**'.find { it.name() == 'OTA_AirAvailRQ' }
        assert oTA_AirAvailRQ.ProcessingInfo.@BaseFaresOnlyIndicator == 'false'
        assert oTA_AirAvailRQ.ProcessingInfo.@TargetSource == 'AWT'
        assert oTA_AirAvailRQ.TravelPreferences.FlightTypePref.@RoutingType == 'O'
        GPathResult originDestinationInformation = gPathResult.'**'.find { it.name() == 'OriginDestinationInformation' }
        assert originDestinationInformation.OriginLocation.@LocationCode == portCodes[0]
        assert originDestinationInformation.DestinationLocation.@LocationCode == portCodes[1]
        assert originDestinationInformation.TravelPreferences.CabinPref[0].@Cabin == 'BUSINESS'
        assert !originDestinationInformation.TravelPreferences.CabinPref[1]
        GPathResult airTravelerAvail = gPathResult.'**'.find { it.name() == 'AirTravelerAvail' }
        assert airTravelerAvail.'*'.find { it.name() == 'PassengerTypeQuantity' && it.@Code == 'ADULT' }.@Quantity == '4'
        assert airTravelerAvail.'*'.find { it.name() == 'PassengerTypeQuantity' && it.@Code == 'CHILD' }.@Quantity == '2'
        assert airTravelerAvail.AirTraveler.CustLoyalty.@MembershipID == msNo.drop(2)
        assert airTravelerAvail.AirTraveler.CustLoyalty.@ProgramID == msNo.take(2)
    }

    @Test
    void option_filters() {
        String airlineCode = 'TK'
        int mostStopOverCount = 0
        List portCodes = ['ESB', 'ADB']
        String request = new GetAvailabilityRequest(routingType: 'ONEWAY', portCodes: portCodes, fareTypes: ['ANY'],
                daysToFlights: [15], passengerCodes: ['ADULT', 'ADULT', 'ADULT', 'ADULT', 'CHILD', 'CHILD'],
                airlineCode: airlineCode, mostStopOverCount: mostStopOverCount)
        GPathResult gPathResult = new XmlSlurper().parseText(request)
        GPathResult oTA_AirAvailRQ = gPathResult.'**'.find { it.name() == 'OTA_AirAvailRQ' }
        assert oTA_AirAvailRQ.ProcessingInfo.@TargetSource == 'BrandedFares'
        assert oTA_AirAvailRQ.ProcessingInfo.@BaseFaresOnlyIndicator == 'true'
        assert oTA_AirAvailRQ.TravelPreferences.FlightTypePref.@RoutingType == 'O'
        GPathResult originDestinationInformation = oTA_AirAvailRQ.OriginDestinationInformation
        assert originDestinationInformation.OriginLocation.@LocationCode == portCodes[0]
        assert originDestinationInformation.DestinationLocation.@LocationCode == portCodes[1]
        assert originDestinationInformation.TravelPreferences.CabinPref[0].@Cabin == 'ECONOMY'
        assert originDestinationInformation.TravelPreferences.CabinPref[1].@Cabin == 'BUSINESS'
        GPathResult airTravelerAvail = gPathResult.'**'.find { it.name() == 'AirTravelerAvail' }
        assert airTravelerAvail.'*'.find { it.name() == 'PassengerTypeQuantity' && it.@Code == 'ADULT' }.@Quantity == '4'
        assert airTravelerAvail.'*'.find { it.name() == 'PassengerTypeQuantity' && it.@Code == 'CHILD' }.@Quantity == '2'
        GPathResult filterRequests = gPathResult.depthFirst().find { it.name() == 'filterRequests' }
        assert filterRequests.filterByStopOver.mostStopOverCount == mostStopOverCount
        assert filterRequests.filterByOperatedCompany.airlineCode == airlineCode
    }

    @Test
    void websdom_channel() {
        String request = new GetAvailabilityRequest(routingType: 'ONEWAY', portCodes: ['ESB', 'DXB'],
                fareTypes: ['ANY'], daysToFlights: [5],
                passengerCodes: ['ADULT', 'ADULT', 'CHILD'],
                channel: 'WEBSDOM_XML')
        GPathResult gPathResult = new XmlSlurper().parseText(request)
        def requestHeader = gPathResult.depthFirst().find { it.name() == 'requestHeader' }
        assert requestHeader.clientUsername == 'WEBSDOM'
        assert requestHeader.agencyOfficeCode == 'XML'
    }
    
    @Test
    void account_code() {
        String accountCode = 'KOTAN'
        String request = new GetAvailabilityRequest(routingType: 'ONEWAY', portCodes: ['ESB', 'DXB'],
                fareTypes: ['ANY'], daysToFlights: [5],
                passengerCodes: ['ADULT', 'ADULT', 'CHILD'],
                channel: 'KBS', accountCode: accountCode)
        GPathResult gPathResult = new XmlSlurper().parseText(request)
        assert gPathResult.depthFirst().find { it.name() == 'PriceRequestInformation' }.Account.@Code == accountCode
    }

    @Test
    void international_ydus_request() {
        String request = new GetAvailabilityRequest(routingType: 'ROUNDTRIP', portCodes: ['IST', 'JFK', 'JFK', 'IST'],
                fareTypes: ['ANY', 'ANY'], daysToFlights: [5, 10],
                passengerCodes: ['ADULT', 'ADULT', 'CHILD'],
                channel: 'WEB3')
        GPathResult gPathResult = new XmlSlurper().parseText(request)
        List originDestinationInformation = gPathResult.'**'.findAll { it.name() == 'OriginDestinationInformation' }
        assert originDestinationInformation[0].TravelPreferences.FlightTypePref.@MaxConnections == '1'
        assert originDestinationInformation[1].TravelPreferences.FlightTypePref.@MaxConnections == '0'
        assert gPathResult.depthFirst().find { it.name() == 'requestHeader' }.extraParameters.find { it.@key == 'BrandedFares' }.@value == 'T'
        assert gPathResult.depthFirst().find { it.name() == 'ProcessingInfo' }.@TargetSource == 'PREMIUM'
    }

}