package com.thy.qa.td4p.request.soap

import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.environment.TroyaEnvironment
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import org.junit.Test

import java.time.LocalDateTime

class GetSeatMap_Test {

    @Test
    void happyPath() {
        GetSeatMapRequest getSeatMapRequest = new GetSeatMapRequest([channel             : Channel.SMARTMOBILE,
                                                                     sessionId           : '123',
                                                                     troyaEnvironment    : TroyaEnvironment.WEBT,
                                                                     flightSegment       : [fareBasisCode  : 'M', resBookDesigCode: 'Y', brandCode: 'FX', departureAirport: 'JFK',
                                                                                            arrivalAirport : 'IST', airlineCode: AirlineCode.TK, operatingAirlineCode: AirlineCode.TK,
                                                                                            flightNumber   : '4', departureDateTime: LocalDateTime.parse('2022-05-24T12:45:00'),
                                                                                            arrivalDateTime: LocalDateTime.parse('2022-05-25T05:20:00'), validConnectionIndicator: false,
                                                                     ],
                                                                     pnrNumber           : '123456',
                                                                     seatFareCurrencyCode: 'TRY'])

        GPathResult request = new XmlSlurper(false, true).parseText(getSeatMapRequest.toString())
        assert request.depthFirst().find { GPathResult node -> node.name() == 'currency' }.text() == 'TRY'
    }
}
