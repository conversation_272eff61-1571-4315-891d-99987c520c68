package com.thy.qa.td4p.request.soap

import com.thy.qa.td4p.enums.Channel
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import org.junit.Test

class RetrieveReservationDetailRequest_Test {

    @Test
    void smartmobile_channel() {
        String pnrNumber = 'QRSJAA'
        String surname = 'BALCILAR'
        String request = new RetrieveReservationDetailRequest(channel: Channel.SMARTMOBILE,
                sessionId: 'SI-TD4P-424242',
                troyaEnvironment: 'WEBT',
                pnrNumber: pnrNumber,
                surname: surname)
        GPathResult result = new XmlSlurper().parseText(request)
        Iterator children = result.depthFirst()
        GPathResult requestHeader = (GPathResult) children.find { GPathResult it -> it.name() == 'requestHeader' }
        assert requestHeader.clientUsername == 'SMARTMOBIL'
        GPathResult uniqueID = (GPathResult) children.find { GPathResult it -> it.name() == 'UniqueID' }
        assert uniqueID.@ID == pnrNumber
        GPathResult personName = (GPathResult) children.find { GPathResult it -> it.name() == 'PersonName' }
        assert personName.Surname == surname
    }

}
