package com.thy.qa.td4p.request.soap

import com.thy.qa.td4p.baseclasses.FlightSegment
import com.thy.qa.td4p.baseclasses.OriginDestinationOption
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.environment.TroyaEnvironment
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.payments.CreditCardPayment
import com.thy.qa.td4p.payments.EFTPayment
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import org.junit.Test

import java.time.LocalDateTime
import java.time.YearMonth

class ReissueRequest_Test {

    Channel channel = 'WEB3'
    TroyaEnvironment troyaEnvironment = 'WEBT'
    String sessionId = 'SI-TD4P-332949829842'
    String pnrNumber = 'R4JSKL'
    String surname = 'BALCILAR'
    String msNo = 'TK0001827'

    List originDestinationOptions = [
            new OriginDestinationOption(
                    [[fareBasisCode  : 'M', resBookDesigCode: 'Y', brandCode: 'FX', departureAirport: 'JFK',
                      arrivalAirport : 'IST', airlineCode: AirlineCode.TK, airlineCode: AirlineCode.TK, operatingAirlineCode: AirlineCode.TK,
                      flightNumber   : '4', departureDateTime: LocalDateTime.parse('2022-05-24T12:45:00'),
                      arrivalDateTime: LocalDateTime.parse('2022-05-25T05:20:00'), validConnectionIndicator: false,
                     ]]
            ),
            new OriginDestinationOption(
                    [[fareBasisCode  : 'T', resBookDesigCode: 'Y', brandCode: 'FX', departureAirport: 'EWR',
                      arrivalAirport : 'YYZ', airlineCode: AirlineCode.TK, airlineCode: AirlineCode.AC, operatingAirlineCode: 'AC',
                      flightNumber   : '8881', departureDateTime: LocalDateTime.parse('2022-05-24T14:50:00'),
                      arrivalDateTime: LocalDateTime.parse('2022-05-24T16:25:00'), validConnectionIndicator: true,
                     ],
                     [fareBasisCode  : 'Y', resBookDesigCode: 'Y', brandCode: 'RS', departureAirport: 'YYZ',
                      arrivalAirport : 'IST', airlineCode: AirlineCode.TK, airlineCode: AirlineCode.TK, operatingAirlineCode: 'TK',
                      flightNumber   : '18', departureDateTime: LocalDateTime.parse('2022-05-24T22:20:00'),
                      arrivalDateTime: LocalDateTime.parse('2022-05-25T15:10:00'), validConnectionIndicator: false,
                     ]]
            )
    ]

    List passengerCodes = [PassengerCode.ADULT, PassengerCode.ADULT, PassengerCode.ADULT]

    @Test
    void reissueItineraryUpdate_request() {
        String request = new ReissueItineraryUpdateRequest(
                channel: channel,
                sessionId: sessionId,
                troyaEnvironment: troyaEnvironment,
                originDestinationOptions: originDestinationOptions,
                passengerCodes: passengerCodes,
                pnrNumber: pnrNumber,
                surname: surname
        )
        GPathResult result = new XmlSlurper().parseText(request)
        GPathResult originDestinationOption = result.depthFirst().find { it.name() == 'OriginDestinationOptions' }.OriginDestinationOption
        originDestinationOption.eachWithIndex { element, i ->
            List flightSegments = originDestinationOptions[i].flightSegments
            element.FlightSegment.eachWithIndex { segment, index ->
                FlightSegment flightSegment = flightSegments[index]
                assert segment.@FareBasisCode == flightSegment.fareBasisCode
                assert segment.@ResBookDesigCode == flightSegment.resBookDesigCode
                assert LocalDateTime.parse(<EMAIL>()) == flightSegment.departureDateTime
                assert LocalDateTime.parse(<EMAIL>()) == flightSegment.arrivalDateTime
                assert segment.@FlightNumber == flightSegment.flightNumber
                assert segment.@ValidConnectionInd == flightSegment.validConnectionIndicator
                assert segment.DepartureAirport.@LocationCode == flightSegment.departureAirport
                assert segment.ArrivalAirport.@LocationCode == flightSegment.arrivalAirport
                assert segment.OperatingAirline.@Code == flightSegment.operatingAirlineCode
            }
        }
        List passengers = result.depthFirst().findAll { it.name() == 'AirTraveler' }
        passengers.eachWithIndex { it, i ->
            assert it.@PassengerTypeCode == passengerCodes[i]
            assert it.TravelerRefNumber.@RPH == i + 1
        }
    }

    @Test
    void reissue_eft_payment() {
        ReissueItineraryUpdateRequest reissueItineraryUpdateRequest = new ReissueItineraryUpdateRequest(
                channel: channel,
                sessionId: sessionId,
                troyaEnvironment: troyaEnvironment,
                originDestinationOptions: originDestinationOptions,
                removedSegmentIndexes: [1, 2],
                passengerCodes: passengerCodes,
                pnrNumber: pnrNumber,
                surname: surname
        )
        String reissueRequest = new ReissueRequest(
                channel: channel,
                sessionId: sessionId,
                troyaEnvironment: troyaEnvironment,
                reissueItineraryUpdateRequest: reissueItineraryUpdateRequest.toString(),
                pnrNumber: pnrNumber,
                surname: surname,
                payment: new EFTPayment(),
                currencyCode: 'TRY',
                amount: 1,
                refundAmount: 2.45,
                miles: 0,
                refundMiles: 123
        )
        GPathResult result = new XmlSlurper().parseText(reissueRequest)
        GPathResult requestHeader = result.depthFirst().find { it.name() == 'requestHeader' }
        assert !requestHeader.children().find { it.name() == 'extraParameters' && it.@key == 'PAYMENT_TRACKING_ID' }
        assert !requestHeader.children().find { it.name() == 'extraParameters' && it.@key == 'PG_APPLICATION_MODE' }
    }

    @Test
    void reissue_no_payment() {
        ReissueItineraryUpdateRequest reissueItineraryUpdateRequest = new ReissueItineraryUpdateRequest(
                channel: channel,
                sessionId: sessionId,
                troyaEnvironment: troyaEnvironment,
                originDestinationOptions: originDestinationOptions,
                removedSegmentIndexes: [1, 2],
                passengerCodes: passengerCodes,
                pnrNumber: pnrNumber,
                surname: surname,
                tourCode: 'MILES',
                tourCodeType: 'AWARDCODE'
        )
        String reissueRequest = new ReissueRequest(
                channel: channel,
                sessionId: sessionId,
                troyaEnvironment: troyaEnvironment,
                reissueItineraryUpdateRequest: reissueItineraryUpdateRequest.toString(),
                pnrNumber: pnrNumber,
                surname: surname,
                msNo: 'TK0001827',
                payment: new CreditCardPayment(YearMonth.of(2030, 02), '123', '5610591081018250'),
                currencyCode: 'TRY',
                amount: 0,
                refundAmount: 2.45,
                miles: 0,
                refundMiles: 123
        )
        GPathResult result = new XmlSlurper().parseText(reissueRequest)
        GPathResult reissueOTARequest = result.depthFirst().find { it.name() == 'ReissueOTARequest' }
        assert reissueOTARequest.children().find { it.name() == 'createTicketOTARequest' }.isEmpty()
        assert !reissueOTARequest.children().find { it.name() == 'OTA_CancelRQ' }.isEmpty()
        assert !reissueOTARequest.children().find { it.name() == 'OTA_AirBookModifyRQ' }.isEmpty()
        assert !reissueOTARequest.children().find { it.name() == 'OTA_AirDemandTicketRQ' }.isEmpty()
    }

    @Test
    void reissue_award_request() {
        int miles = 1
        int refundMiles = 123
        ReissueItineraryUpdateRequest reissueItineraryUpdateRequest = new ReissueItineraryUpdateRequest(
                channel: channel,
                sessionId: sessionId,
                troyaEnvironment: troyaEnvironment,
                originDestinationOptions: originDestinationOptions,
                removedSegmentIndexes: [1, 2],
                passengerCodes: passengerCodes,
                pnrNumber: pnrNumber,
                surname: surname,
                tourCode: 'MILES',
                tourCodeType: 'AWARDCODE'
        )
        String reissueRequest = new ReissueRequest(
                channel: channel,
                sessionId: sessionId,
                troyaEnvironment: troyaEnvironment,
                reissueItineraryUpdateRequest: reissueItineraryUpdateRequest.toString(),
                pnrNumber: pnrNumber,
                surname: surname,
                msNo: msNo,
                payment: new CreditCardPayment(YearMonth.of(2030, 02), '123', '5610591081018250'),
                currencyCode: 'TRY',
                amount: 1,
                refundAmount: 2.45,
                miles: miles,
                refundMiles: refundMiles
        )
        GPathResult result = new XmlSlurper().parseText(reissueRequest)
        GPathResult reissueOTARequest = result.depthFirst().find { it.name() == 'ReissueOTARequest' }
        assert reissueOTARequest.OTA_CancelRQ.TPA_Extensions.TK_RQIntentionType.Application == 'AWT'
        assert reissueOTARequest.OTA_AirBookModifyRQ.AirReservation.PriceInfo.ItinTotalFare.@TicketDesignatorCode == 'MILES'
        assert reissueOTARequest.createTicketOTARequest.milesInfo.frequentFlyerId == msNo
        assert reissueOTARequest.reissueMilesMoneyDTO.amount == miles
        assert reissueOTARequest.reissueMilesMoneyDTO.refundAmount == refundMiles
        assert reissueOTARequest.reissueMilesMoneyDTO.currency.code == 'PTS'
    }

    @Test
    void reissue_cash_ticket_removed_segments() {
        List removedSegmentRphs = [[1, 2]]
        ReissueItineraryUpdateRequest reissueItineraryUpdateRequest = new ReissueItineraryUpdateRequest(
                channel: channel,
                sessionId: sessionId,
                troyaEnvironment: troyaEnvironment,
                originDestinationOptions: originDestinationOptions,
                removedSegmentIndexes: removedSegmentRphs,
                passengerCodes: passengerCodes,
                pnrNumber: pnrNumber,
                surname: surname
        )
        String reissueRequest = new ReissueRequest(
                channel: channel,
                sessionId: sessionId,
                troyaEnvironment: troyaEnvironment,
                reissueItineraryUpdateRequest: reissueItineraryUpdateRequest.toString(),
                pnrNumber: pnrNumber,
                surname: surname,
                payment: new CreditCardPayment(YearMonth.of(2030, 02), '123', '5610591081018250'),
                currencyCode: 'TRY',
                amount: 1,
                refundAmount: 2.45
        )
        GPathResult result = new XmlSlurper().parseText(reissueRequest)
        GPathResult reissueOTARequest = result.depthFirst().find { it.name() == 'ReissueOTARequest' }
        GPathResult otaAirBookModifyRq = reissueOTARequest.OTA_AirBookModifyRQ
        GPathResult originDestinationOption = otaAirBookModifyRq.AirReservation.AirItinerary.OriginDestinationOptions.OriginDestinationOption[-1]
        originDestinationOption.FlightSegment.eachWithIndex { flightSegment, i ->
            assert flightSegment.@RPH == removedSegmentRphs[0][i]
            assert flightSegment.Comment == 'REMOVE'
        }
    }

}
