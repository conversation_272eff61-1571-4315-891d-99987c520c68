package com.thy.qa.td4p.request.rest

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import org.junit.Test

class GetAvailabilityPostRequest_Test {

    @Test
    void all_must_params_post() {
        String fareType = 'BUSINESS'
        List passengerCodes = ['ADULT', 'ADULT', 'ADULT', 'ADULT', 'CHILD', 'CHILD']
        String request = new GetAvailabilityPostRequest(
                routingType: 'ONEWAY', portCodes: ['IST', 'JFK'], fareTypes: [fareType],
                daysToFlights: [5], passengerCodes: passengerCodes,
                msNo: 'TK002030758'
        )
        ObjectNode requestJson = new ObjectMapper().readTree(request)
        assert !requestJson.get('requestHeader').isMissingNode()
        assert requestJson.get('requestHeader').get('clientTransactionId').asText().length() > 0
        assert requestJson.get('OriginDestinationInformation').isArray()
        assert requestJson.get('OriginDestinationInformation').size() == 1
        assert requestJson.get('OriginDestinationInformation').get(0).get('CabinPreferences').get(0).get('Cabin').asText() == fareType
        assert requestJson.get('RoutingType').asText() == 'O'
        assert requestJson.get('PassengerTypeQuantity')*.get('Quantity')*.intValue().sum() == passengerCodes.size()
    }

}