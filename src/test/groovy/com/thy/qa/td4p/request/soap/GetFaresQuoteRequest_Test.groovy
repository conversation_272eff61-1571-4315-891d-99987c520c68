package com.thy.qa.td4p.request.soap

import com.thy.qa.td4p.enums.BrandCode
import com.thy.qa.td4p.enums.Channel
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import org.junit.Test

class GetFaresQuoteRequest_Test {

    @Test
    void brand_codes_request() {
        String pnrNumber = "RQES9W"
        List brandCodes = [BrandCode.EF, BrandCode.XF]
        List segmentIndexes = [1, 2]
        String request = new GetFaresQuoteRequest(pnrNumber: pnrNumber,
                brandCodes: brandCodes,
                segmentIndexes: segmentIndexes).toString()
        GPathResult result = new XmlSlurper().parseText(request)
        GPathResult fareInfos = result.depthFirst().find { it.name() == 'FareInfos' }
        fareInfos.eachWithIndex { node, index ->
            assert node.FareInfo[index].@RPH == segmentIndexes[index].toString()
            assert node.FareInfo[index].@RuleNumber == brandCodes[index].toString()
        }
        assert result.depthFirst().find { it.name() == 'BookingReferenceID' }.@ID == pnrNumber
    }

    @Test
    void award_request() {
        String pnrNumber = "RQES9W"
        String tourCodeType = "AWARDCODE"
        String tourCode = "MILES"
        String request = new GetFaresQuoteRequest(pnrNumber: pnrNumber,
                tourCode: tourCode,
                tourCodeType: tourCodeType,
                taxPaidByMiles: true).toString()
        GPathResult result = new XmlSlurper().parseText(request)
        GPathResult priceInfo = result.depthFirst().find { it.name() == 'PriceInfo' }
        assert priceInfo.ItinTotalFare.@InputTicketDesigCode == tourCodeType
        assert priceInfo.ItinTotalFare.@TicketDesignatorCode == tourCode
        assert priceInfo.PricingPref.@ExcludeInd == "true"
        assert result.depthFirst().find { it.name() == 'BookingReferenceID' }.@ID == pnrNumber
    }

    @Test
    void vpo_award_request() {
        String pnrNumber = "RQES9W"
        String tourCodeType = "AWARDCODE"
        String tourCode = "MILES"
        String request = new GetFaresQuoteRequest(pnrNumber: pnrNumber,
                tourCode: tourCode,
                tourCodeType: tourCodeType).toString()
        GPathResult result = new XmlSlurper().parseText(request)
        GPathResult priceInfo = result.depthFirst().find { it.name() == 'PriceInfo' }
        assert priceInfo.ItinTotalFare.@InputTicketDesigCode == tourCodeType
        assert priceInfo.ItinTotalFare.@TicketDesignatorCode == tourCode
        assert priceInfo.PricingPref.@ExcludeInd == "false"
        assert result.depthFirst().find { it.name() == 'BookingReferenceID' }.@ID == pnrNumber
    }

    @Test
    void sales_office_request() {
        String pnrNumber = "RQES9W"
        String surname = "BALCILAR"
        String request = new GetFaresQuoteRequest(pnrNumber: pnrNumber,
                surname: surname,
                channel: Channel.QRES_FRA).toString()
        GPathResult result = new XmlSlurper().parseText(request)
        GPathResult requestHeader = result.depthFirst().find { it.name() == 'requestHeader' }
        assert requestHeader.clientUsername == "QRES"
        assert requestHeader.agencyOfficeCode == "FRA"
        assert result.depthFirst().find { it.name() == 'BookingReferenceID' }.@ID == pnrNumber
        assert result.depthFirst().find { it.name() == 'Fulfillment' }.Name.Surname == surname
    }

    @Test
    void fare_construction_city_code() {
        String pnrNumber = "RQES9W"
        String fareConstructionCityCode = 'IST'
        String request = new GetFaresQuoteRequest(pnrNumber: pnrNumber,
                fareConstructionCityCode: fareConstructionCityCode).toString()
        GPathResult result = new XmlSlurper().parseText(request)
        GPathResult priceInfo = result.depthFirst().find { it.name() == 'PriceInfo' }
        assert priceInfo.ItinTotalFare.FareConstruction.@OriginCityCode == fareConstructionCityCode
        assert result.depthFirst().find { it.name() == 'BookingReferenceID' }.@ID == pnrNumber
    }

}
