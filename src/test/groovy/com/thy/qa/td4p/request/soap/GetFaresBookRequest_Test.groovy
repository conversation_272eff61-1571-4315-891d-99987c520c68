package com.thy.qa.td4p.request.soap

import com.thy.qa.td4p.baseclasses.FlightSegment
import com.thy.qa.td4p.baseclasses.OriginDestinationOption
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.passenger.PassengerCode
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import org.junit.Test

import java.time.LocalDateTime

class GetFaresBookRequest_Test {

    @Test
    void withExtraSeats() {
        List originDestinationOptions = [
                new OriginDestinationOption(
                        [[fareBasisCode  : 'M', resBookDesigCode: 'Y', brandCode: 'FX', departureAirport: 'JFK',
                          arrivalAirport : 'IST', airlineCode: AirlineCode.TK, operatingAirlineCode: AirlineCode.TK,
                          flightNumber   : '4', departureDateTime: LocalDateTime.parse('2022-05-24T12:45:00'),
                          arrivalDateTime: LocalDateTime.parse('2022-05-25T05:20:00'), validConnectionIndicator: false,
                         ]]
                ),
                new OriginDestinationOption(
                        [[fareBasisCode  : 'T', resBookDesigCode: 'Y', brandCode: 'FX', departureAirport: 'EWR',
                          arrivalAirport : 'YYZ', airlineCode: AirlineCode.TK, operatingAirlineCode: 'AC',
                          flightNumber   : '8881', departureDateTime: LocalDateTime.parse('2022-05-24T14:50:00'),
                          arrivalDateTime: LocalDateTime.parse('2022-05-24T16:25:00'), validConnectionIndicator: true,
                         ],
                         [fareBasisCode  : 'Y', resBookDesigCode: 'Y', brandCode: 'RS', departureAirport: 'YYZ',
                          arrivalAirport : 'IST', airlineCode: AirlineCode.TK, operatingAirlineCode: 'TK',
                          flightNumber   : '18', departureDateTime: LocalDateTime.parse('2022-05-24T22:20:00'),
                          arrivalDateTime: LocalDateTime.parse('2022-05-25T15:10:00'), validConnectionIndicator: false,
                         ]]
                )
        ]
        List passengerCodes = [PassengerCode.ADULT, PassengerCode.ADULT, PassengerCode.ADULT]
        List extraSeats = [0, 1, 0]
        String request = new GetFaresBookRequest(
                originDestinationOptions: originDestinationOptions,
                passengerCodes: passengerCodes,
                extraSeats: extraSeats
        )
        GPathResult result = new XmlSlurper().parseText(request)
        GPathResult originDestinationOption = result.depthFirst().find { it.name() == 'OriginDestinationOptions' }.OriginDestinationOption
        originDestinationOption.eachWithIndex { element, i ->
            List flightSegments = originDestinationOptions[i].flightSegments
            element.FlightSegment.eachWithIndex { segment, index ->
                FlightSegment flightSegment = flightSegments[index]
                assert segment.@FareBasisCode == flightSegment.fareBasisCode
                assert segment.@ResBookDesigCode == flightSegment.resBookDesigCode
                assert LocalDateTime.parse(<EMAIL>()) == flightSegment.departureDateTime
                assert LocalDateTime.parse(<EMAIL>()) == flightSegment.arrivalDateTime
                assert segment.@FlightNumber == flightSegment.flightNumber
                assert segment.@ValidConnectionInd == flightSegment.validConnectionIndicator
                assert segment.DepartureAirport.@LocationCode == flightSegment.departureAirport
                assert segment.ArrivalAirport.@LocationCode == flightSegment.arrivalAirport
                assert segment.OperatingAirline.@Code == flightSegment.operatingAirlineCode
            }
        }
        List brandCodes = originDestinationOptions*.flightSegments.sum()*.brandCode
        GPathResult fareInfos = result.depthFirst().find { it.name() == 'FareInfos' }
        fareInfos.FareInfo.eachWithIndex { element, i ->
            assert element.@RuleNumber == brandCodes[i]
            assert element.@RPH == i + 1
        }
        List passengers = result.depthFirst().findAll { it.name() == 'AirTraveler' }*.PassengerTypeQuantity
        assert passengers[0].@Code == PassengerCode.ADULT
        assert passengers[0].@Quantity == 1
        assert passengers[1].@Code == PassengerCode.ADULT
        assert passengers[1].@Quantity == 1
        assert passengers[1].@CodeContext == 'EXST'
        assert passengers[2].@Code == PassengerCode.ADULT
        assert passengers[2].@Quantity == 1
    }

    @Test
    void withoutExtraSeats() {
        List originDestinationOptions = [
                new OriginDestinationOption(
                        [[fareBasisCode  : 'M', resBookDesigCode: 'Y', brandCode: 'FX', departureAirport: 'JFK',
                          arrivalAirport : 'IST', operatingAirlineCode: AirlineCode.TK,
                          flightNumber   : '4', departureDateTime: LocalDateTime.parse('2022-05-24T12:45:00'),
                          arrivalDateTime: LocalDateTime.parse('2022-05-25T05:20:00'), validConnectionIndicator: false,
                         ]]
                )
        ]
        List passengerCodes = [PassengerCode.ADULT, PassengerCode.CHILD, PassengerCode.INFANT]
        String request = new GetFaresBookRequest(
                originDestinationOptions: originDestinationOptions,
                passengerCodes: passengerCodes
        )
        GPathResult result = new XmlSlurper().parseText(request)
        List passengers = result.depthFirst().findAll { it.name() == 'AirTraveler' }*.PassengerTypeQuantity
        assert passengers[0].@Code == PassengerCode.ADULT
        assert passengers[0].@Quantity == 1
        assert passengers[1].@Code == PassengerCode.CHILD
        assert passengers[1].@Quantity == 1
        assert passengers[2].@Code == PassengerCode.INFANT
        assert passengers[2].@Quantity == 1
    }

}
