package com.thy.qa.td4p.request.soap

import com.thy.qa.td4p.baseclasses.FlightSegment
import com.thy.qa.td4p.baseclasses.OriginDestinationOption
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.passenger.PassengerCode
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import org.junit.Test

import java.time.LocalDateTime

class GetFaresInitiateRequest_Test {
    @Test
    void all_params() {
        List originDestinationOptions = [
                new OriginDestinationOption(
                        [[fareBasisCode  : 'X', resBookDesigCode: 'Y', departureAirport: 'JFK',
                          arrivalAirport : 'IST', airlineCode: AirlineCode.TK, operatingAirlineCode: AirlineCode.TK,
                          flightNumber   : '4', departureDateTime: LocalDateTime.parse('2022-05-24T12:45:00'),
                          arrivalDateTime: LocalDateTime.parse('2022-05-25T05:20:00'), validConnectionIndicator: false,
                         ]]
                ),
                new OriginDestinationOption(
                        [[fareBasisCode  : 'X', resBookDesigCode: 'Y', departureAirport: 'EWR',
                          arrivalAirport : 'YYZ', airlineCode: AirlineCode.TK, operatingAirlineCode: 'AC',
                          flightNumber   : '8881', departureDateTime: LocalDateTime.parse('2022-05-24T14:50:00'),
                          arrivalDateTime: LocalDateTime.parse('2022-05-24T16:25:00'), validConnectionIndicator: true,
                         ],
                         [fareBasisCode  : 'X', resBookDesigCode: 'Y', departureAirport: 'YYZ',
                          arrivalAirport : 'IST', airlineCode: AirlineCode.TK, operatingAirlineCode: 'TK',
                          flightNumber   : '18', departureDateTime: LocalDateTime.parse('2022-05-24T22:20:00'),
                          arrivalDateTime: LocalDateTime.parse('2022-05-25T15:10:00'), validConnectionIndicator: false,
                         ]]
                )
        ]
        List passengerCodes = [PassengerCode.ADULT, PassengerCode.SENIOR, PassengerCode.TEACHER]
        String msNo = 'TK002934055'
        String request = new GetFaresInitiateRequest(
                originDestinationOptions: originDestinationOptions,
                passengerCodes: passengerCodes,
                msNo: msNo
        ).toString()
        GPathResult result = new XmlSlurper().parseText(request)
        GPathResult originDestinationOption = result.depthFirst().find { it.name() == 'OriginDestinationOptions' }.OriginDestinationOption
        originDestinationOption.eachWithIndex { element, i ->
            List flightSegments = originDestinationOptions[i].flightSegments
            element.FlightSegment.eachWithIndex { segment, index ->
                FlightSegment flightSegment = flightSegments[index]
                assert segment.@FareBasisCode == flightSegment.fareBasisCode
                assert segment.@ResBookDesigCode == flightSegment.resBookDesigCode
                assert LocalDateTime.parse(<EMAIL>()) == flightSegment.departureDateTime
                assert LocalDateTime.parse(<EMAIL>()) == flightSegment.arrivalDateTime
                assert segment.@FlightNumber == flightSegment.flightNumber
                assert segment.@ValidConnectionInd == flightSegment.validConnectionIndicator
                assert segment.DepartureAirport.@LocationCode == flightSegment.departureAirport
                assert segment.ArrivalAirport.@LocationCode == flightSegment.arrivalAirport
                assert segment.OperatingAirline.@Code == flightSegment.operatingAirlineCode
            }
        }
        GPathResult priceInfo = result.depthFirst().find { it.name() == 'PriceInfo' }
        assert priceInfo.ItinTotalFare.@InputTicketDesigCode == 'AWARDCODE'
        assert priceInfo.ItinTotalFare.@TicketDesignatorCode == 'MILES'
        List passengers = result.depthFirst().findAll { it.name() == 'AirTraveler' }
        String membershipId = msNo.drop(2)
        String programId = msNo.take(2)
        assert passengers[0].CustLoyalty.@MembershipID == membershipId
        assert passengers[0].CustLoyalty.@ProgramID == programId
        assert passengers[0].PassengerTypeQuantity.@Code == PassengerCode.ADULT
        assert passengers[0].PassengerTypeQuantity.@Quantity == 1
        assert passengers[1].CustLoyalty.@MembershipID == membershipId
        assert passengers[1].CustLoyalty.@ProgramID == programId
        assert passengers[1].PassengerTypeQuantity.@Code == PassengerCode.SENIOR
        assert passengers[1].PassengerTypeQuantity.@Quantity == 1
        assert passengers[2].CustLoyalty.@MembershipID == membershipId
        assert passengers[2].CustLoyalty.@ProgramID == programId
        assert passengers[2].PassengerTypeQuantity.@Code == PassengerCode.TEACHER
        assert passengers[2].PassengerTypeQuantity.@Quantity == 1
    }

    @Test
    void gmiles() {
        List originDestinationOptions = [
                new OriginDestinationOption(
                        [[fareBasisCode  : 'Y', resBookDesigCode: 'Y', departureAirport: 'JFK',
                          arrivalAirport : 'IST', airlineCode: AirlineCode.TK, operatingAirlineCode: AirlineCode.TK,
                          flightNumber   : '4', departureDateTime: LocalDateTime.parse('2022-05-24T12:45:00'),
                          arrivalDateTime: LocalDateTime.parse('2022-05-25T05:20:00'), validConnectionIndicator: false,
                         ]]
                )
        ]
        List passengerCodes = [PassengerCode.ADULT]
        String msNo = 'TK002934055'
        String request = new GetFaresInitiateRequest(
                originDestinationOptions: originDestinationOptions,
                passengerCodes: passengerCodes,
                msNo: msNo
        ).toString()
        GPathResult result = new XmlSlurper().parseText(request)
        GPathResult originDestinationOption = result.depthFirst().find { it.name() == 'OriginDestinationOptions' }.OriginDestinationOption
        originDestinationOption.eachWithIndex { element, i ->
            List flightSegments = originDestinationOptions[i].flightSegments
            element.FlightSegment.eachWithIndex { segment, index ->
                FlightSegment flightSegment = flightSegments[index]
                assert segment.@FareBasisCode == flightSegment.fareBasisCode
                assert segment.@ResBookDesigCode == flightSegment.resBookDesigCode
                assert LocalDateTime.parse(<EMAIL>()) == flightSegment.departureDateTime
                assert LocalDateTime.parse(<EMAIL>()) == flightSegment.arrivalDateTime
                assert segment.@FlightNumber == flightSegment.flightNumber
                assert segment.@ValidConnectionInd == flightSegment.validConnectionIndicator
                assert segment.DepartureAirport.@LocationCode == flightSegment.departureAirport
                assert segment.ArrivalAirport.@LocationCode == flightSegment.arrivalAirport
                assert segment.OperatingAirline.@Code == flightSegment.operatingAirlineCode
            }
        }
        GPathResult priceInfo = result.depthFirst().find { it.name() == 'PriceInfo' }
        assert priceInfo.ItinTotalFare.@InputTicketDesigCode == 'AWARDCODE'
        assert priceInfo.ItinTotalFare.@TicketDesignatorCode == 'MILESG'
        List passengers = result.depthFirst().findAll { it.name() == 'AirTraveler' }
        String membershipId = msNo.drop(2)
        String programId = msNo.take(2)
        assert passengers[0].CustLoyalty.@MembershipID == membershipId
        assert passengers[0].CustLoyalty.@ProgramID == programId
        assert passengers[0].PassengerTypeQuantity.@Code == PassengerCode.ADULT
        assert passengers[0].PassengerTypeQuantity.@Quantity == 1
    }
}
