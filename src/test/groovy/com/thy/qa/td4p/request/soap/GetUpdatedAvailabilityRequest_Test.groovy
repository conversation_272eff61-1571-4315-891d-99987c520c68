package com.thy.qa.td4p.request.soap

import com.thy.qa.td4p.enums.CabinType
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import org.junit.Test

class GetUpdatedAvailabilityRequest_Test {
    @Test
    void must_params() {
        String jSessionId = 's'
        String request = new GetUpdatedAvailabilityRequest(
                jSessionId: jSessionId,
                recommendationId: 0,
                outboundFlightId: 1,
                inboundFlightId: 0
        )
        GPathResult xml = new XmlSlurper().parseText(request)
        GPathResult requestHeader = xml.Body.getUpdatedAvailability.requestHeader
        GPathResult boundUpdateRequest = xml.Body.getUpdatedAvailability.BoundUpdateRequest
        GPathResult recommendationList = boundUpdateRequest.recommendationList
        GPathResult cabinList = boundUpdateRequest.cabinList
        assert requestHeader.clientUsername == 'WEB3'
        assert boundUpdateRequest.jSessionId == jSessionId
        assert recommendationList.recommendationId == 0
        assert recommendationList.outboundFlightId == 1
        assert recommendationList.inboundFlightId == 0
        assert boundUpdateRequest.boundToUpdate.boundId == 1
        assert cabinList.cabin[0] == CabinType.ECONOMY.name()
        assert cabinList.cabin[1] == CabinType.BUSINESS.name()
    }

    @Test
    void all_params() {
        String jSessionId = 's'
        String request = new GetUpdatedAvailabilityRequest(
                channel: 'SMARTMOBILE',
                jSessionId: jSessionId,
                pageTicket: 0,
                recommendationId: 5,
                outboundFlightId: 4,
                inboundFlightId: 1,
                boundId: 1,
                cabinType: CabinType.ECONOMY
        )
        GPathResult xml = new XmlSlurper().parseText(request)
        GPathResult requestHeader = xml.Body.getUpdatedAvailability.requestHeader
        GPathResult boundUpdateRequest = xml.Body.getUpdatedAvailability.BoundUpdateRequest
        GPathResult recommendationList = boundUpdateRequest.recommendationList
        GPathResult cabinList = boundUpdateRequest.cabinList
        assert requestHeader.clientUsername == 'SMARTMOBIL'
        assert boundUpdateRequest.jSessionId == jSessionId
        assert recommendationList.recommendationId == 5
        assert recommendationList.outboundFlightId == 4
        assert recommendationList.inboundFlightId == 1
        assert boundUpdateRequest.boundToUpdate.boundId == 1
        assert cabinList.cabin[0] == CabinType.ECONOMY.name()
        assert !cabinList.cabin[1]
    }

}
