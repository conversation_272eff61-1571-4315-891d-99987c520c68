package com.thy.qa.td4p.request.soap

import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.environment.TroyaEnvironment
import com.thy.qa.td4p.merchandising.offer.Offer
import com.thy.qa.td4p.parser.GetMerchOfferResponseParser
import com.thy.qa.td4p.payments.CreditCardPayment
import com.thy.qa.td4p.payments.EFTPayment
import com.thy.qa.td4p.pnr.Flow
import com.thy.qa.td4p.request.soap.payment.PaymentInfo
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult
import org.junit.Test

import java.time.YearMonth

class PaymentRequestTest {

    String pricedReservationGetMerchOfferResponse = '''<S:Envelope xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"
  xmlns:S="http://schemas.xmlsoap.org/soap/envelope/">
  <env:Header/>
  <S:Body>
    <ns0:getMerchOfferResponse xmlns:ns0="http://service.thy.com/"
      xmlns:ns5="http://www.thy.com/ws/responseHeader"
      xmlns:ns2="http://www.thy.com/merch/service/v1/service"
      xmlns:ns3="http://www.thy.com/merch/service/v1/commons/cp"
      xmlns:ns4="http://www.thy.com/merch/service/v1/offer"
      xmlns:ns1="http://www.thy.com/ws/requestHeader">
      <ns0:getMerchOfferOtaResponse>
        <offerResponse>
          <ns2:Header ResponseID="4F03F18D-B6D4-449C-972C-1262B641A6ED"
            RequestDT="2024-03-18T00:14:49.106+03:00" ResponseDT="2024-03-18T00:14:49.140+03:00"/>
          <ns2:Result Status="OK" Code="00" Reason="Successful"/>
          <ns2:PricedOffer OfferID="09GBTXN5Y62RF" ParentID="4F03F18D-B6D4-449C-972C-1262B641A6ED"
            Owner="TK" TS="2024-03-18T00:14:49.106+03:00">
            <ns4:OfferItem OfferItemID="09GBTXN5Y62RF01" ParentID="09GBTXN5Y62RF"
              MandatoryInd="true">
              <ns4:Service ServiceID="09GBTXN5Y62RF0101" ParentID="09GBTXN5Y62RF01">
                <ns4:ServiceKind Code="OPT_PRICED_360" Category="OPT_PRICED" Rfic="D" Rfisc="04C"
                  Group="TS" SubGroup="FG"/>
                <ns4:PassengerRef>1</ns4:PassengerRef>
                <ns4:PriceDetail>
                  <ns3:BaseAmount>
                    <ns3:PriceAlteration RuleType="BUNDLE" AlterationType="DISCOUNT" Rate="0">
                      <ns3:AlterationRateInfo Provider="Rule" Rate="0"
                        Code="BUNDLE-3268DE82-6268-4FB8-8312-AC9ABC0F5D5D"/>
                    </ns3:PriceAlteration>
                    <ns3:Initial Amount="500.00" CurrencyCode="TRY"/>
                    <ns3:InitialEquivalent Amount="15.00" CurrencyCode="EUR"/>
                    <ns3:Original Amount="500.00" CurrencyCode="TRY"/>
                    <ns3:Equivalent Amount="15.00" CurrencyCode="EUR"/>
                  </ns3:BaseAmount>
                </ns4:PriceDetail>
                <ns4:Specification SpecificationKind="CustomerFacingService" Code="CFS">
                  <ns4:Parameter Name="OptionDuration" ValueType="Integer" Unit="Hour" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>360</ns4:Value>
                  </ns4:Parameter>
                </ns4:Specification>
              </ns4:Service>
              <ns4:Service ServiceID="09GBTXN5Y62RF0102" ParentID="09GBTXN5Y62RF01">
                <ns4:ServiceKind Code="OPT_PRICED_360" Category="OPT_PRICED" Rfic="D" Rfisc="04C"
                  Group="TS" SubGroup="FG"/>
                <ns4:PassengerRef>2</ns4:PassengerRef>
                <ns4:PriceDetail>
                  <ns3:BaseAmount>
                    <ns3:PriceAlteration RuleType="BUNDLE" AlterationType="DISCOUNT" Rate="0">
                      <ns3:AlterationRateInfo Provider="Rule" Rate="0"
                        Code="BUNDLE-3268DE82-6268-4FB8-8312-AC9ABC0F5D5D"/>
                    </ns3:PriceAlteration>
                    <ns3:Initial Amount="500.00" CurrencyCode="TRY"/>
                    <ns3:InitialEquivalent Amount="15.00" CurrencyCode="EUR"/>
                    <ns3:Original Amount="500.00" CurrencyCode="TRY"/>
                    <ns3:Equivalent Amount="15.00" CurrencyCode="EUR"/>
                  </ns3:BaseAmount>
                </ns4:PriceDetail>
                <ns4:Specification SpecificationKind="CustomerFacingService" Code="CFS">
                  <ns4:Parameter Name="OptionDuration" ValueType="Integer" Unit="Hour" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>360</ns4:Value>
                  </ns4:Parameter>
                </ns4:Specification>
              </ns4:Service>
              <ns4:TotalPriceDetail>
                <ns3:BaseAmount>
                  <ns3:InitialEquivalent Amount="30.00" CurrencyCode="EUR"/>
                  <ns3:Equivalent Amount="30.00" CurrencyCode="EUR"/>
                </ns3:BaseAmount>
                <ns3:TotalAmount CurrencyCode="EUR" Amount="30.00"/>
              </ns4:TotalPriceDetail>
              <ns4:OfferType>BUNDLE</ns4:OfferType>
              <ns4:Description>
                <ns3:Description>
                  <ns3:DescKind>BundleCode</ns3:DescKind>
                </ns3:Description>
              </ns4:Description>
            </ns4:OfferItem>
            <ns4:OfferItem OfferItemID="09GBTXN5Y62RF02" ParentID="09GBTXN5Y62RF"
              MandatoryInd="true">
              <ns4:Service ServiceID="09GBTXN5Y62RF0201" ParentID="09GBTXN5Y62RF02">
                <ns4:ServiceKind Code="OPT_PRICED_24" Category="OPT_PRICED" Rfic="D" Rfisc="04C"
                  Group="TS" SubGroup="FG"/>
                <ns4:PassengerRef>1</ns4:PassengerRef>
                <ns4:PriceDetail>
                  <ns3:BaseAmount>
                    <ns3:PriceAlteration RuleType="BUNDLE" AlterationType="DISCOUNT" Rate="0">
                      <ns3:AlterationRateInfo Provider="Rule" Rate="0"
                        Code="BUNDLE-3268DE82-6268-4FB8-8312-AC9ABC0F5D5D"/>
                    </ns3:PriceAlteration>
                    <ns3:Initial Amount="20.00" CurrencyCode="TRY"/>
                    <ns3:InitialEquivalent Amount="1.00" CurrencyCode="EUR"/>
                    <ns3:Original Amount="20.00" CurrencyCode="TRY"/>
                    <ns3:Equivalent Amount="1.00" CurrencyCode="EUR"/>
                  </ns3:BaseAmount>
                </ns4:PriceDetail>
                <ns4:Specification SpecificationKind="CustomerFacingService" Code="CFS">
                  <ns4:Parameter Name="OptionDuration" ValueType="Integer" Unit="Hour" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>24</ns4:Value>
                  </ns4:Parameter>
                </ns4:Specification>
              </ns4:Service>
              <ns4:Service ServiceID="09GBTXN5Y62RF0202" ParentID="09GBTXN5Y62RF02">
                <ns4:ServiceKind Code="OPT_PRICED_24" Category="OPT_PRICED" Rfic="D" Rfisc="04C"
                  Group="TS" SubGroup="FG"/>
                <ns4:PassengerRef>2</ns4:PassengerRef>
                <ns4:PriceDetail>
                  <ns3:BaseAmount>
                    <ns3:PriceAlteration RuleType="BUNDLE" AlterationType="DISCOUNT" Rate="0">
                      <ns3:AlterationRateInfo Provider="Rule" Rate="0"
                        Code="BUNDLE-3268DE82-6268-4FB8-8312-AC9ABC0F5D5D"/>
                    </ns3:PriceAlteration>
                    <ns3:Initial Amount="20.00" CurrencyCode="TRY"/>
                    <ns3:InitialEquivalent Amount="1.00" CurrencyCode="EUR"/>
                    <ns3:Original Amount="20.00" CurrencyCode="TRY"/>
                    <ns3:Equivalent Amount="1.00" CurrencyCode="EUR"/>
                  </ns3:BaseAmount>
                </ns4:PriceDetail>
                <ns4:Specification SpecificationKind="CustomerFacingService" Code="CFS">
                  <ns4:Parameter Name="OptionDuration" ValueType="Integer" Unit="Hour" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>24</ns4:Value>
                  </ns4:Parameter>
                </ns4:Specification>
              </ns4:Service>
              <ns4:TotalPriceDetail>
                <ns3:BaseAmount>
                  <ns3:InitialEquivalent Amount="2.00" CurrencyCode="EUR"/>
                  <ns3:Equivalent Amount="2.00" CurrencyCode="EUR"/>
                </ns3:BaseAmount>
                <ns3:TotalAmount CurrencyCode="EUR" Amount="2.00"/>
              </ns4:TotalPriceDetail>
              <ns4:OfferType>BUNDLE</ns4:OfferType>
              <ns4:Description>
                <ns3:Description>
                  <ns3:DescKind>BundleCode</ns3:DescKind>
                </ns3:Description>
              </ns4:Description>
            </ns4:OfferItem>
            <ns4:TimeLimit>
              <ns4:offerExpiration dateTime="2024-03-18T01:14:49.106+03:00"/>
            </ns4:TimeLimit>
          </ns2:PricedOffer>
        </offerResponse>
      </ns0:getMerchOfferOtaResponse>
      <responseHeader>
        <ns5:statusCode>SUCCESS</ns5:statusCode>
        <ns5:clientTransactionId>CTID-TD4P-180324-001448047</ns5:clientTransactionId>
        <ns5:serverTransactionId>49d1365e-01cb-4fae-7dde-d97fe926c636</ns5:serverTransactionId>
      </responseHeader>
    </ns0:getMerchOfferResponse>
  </S:Body>
</S:Envelope>'''

    private static final CreditCardPayment creditCardPayment = new CreditCardPayment(YearMonth.of(2024, 12), "4242424242", "34242")

    @Test
    void priced_reservation_prePayment_request() {
        GetMerchOfferResponseParser parser = new GetMerchOfferResponseParser(pricedReservationGetMerchOfferResponse)
        List offers = parser.parseOffers()
        Offer offer = offers.get(0)
        PaymentInfo paymentInfo = new PaymentInfo().payment(creditCardPayment).amount(offer.amount).currencyCode(offer.currencyCode).decimalCount(2)
        PrePaymentRequest request = new PrePaymentRequest(channel: Channel.WEB3,
                sessionId: "sessionId",
                troyaEnvironment: TroyaEnvironment.WEBT,
                pnrNumber: "SAHDYU5",
                surname: "Doe",
                offers: [offer],
                paymentInfo: paymentInfo, flow: Flow.PAID_RESERVATION)
        GPathResult xml = new XmlSlurper().parseText(request.toString())
        assertMessageFunction(xml, 'X')
        assertCoverage(xml, 'reservation')
        assertBasket(xml, 'false')
        assertEmdFareItemCount(xml, 2)
    }

    @Test
    void purchase_basket_with_offers_test() {
        GetMerchOfferResponseParser parser = new GetMerchOfferResponseParser(new File(PaymentRequestTest.getResource('/response/GetMerchOfferResponse.xml').toURI()).text)
        List offers = parser.parseOffers()
        PurchaseBasketRequest request = new PurchaseBasketRequest(channel: Channel.WEB3,
                sessionId: "sessionId",
                troyaEnvironment: TroyaEnvironment.WEBT,
                pnrNumber: "SAHDYU5",
                surname: "Doe",
                offers: offers,
                paymentInfo: new PaymentInfo().payment(creditCardPayment).amount(1.toBigDecimal()).currencyCode('TRY').decimalCount(2), flow: Flow.CASH_TICKETING)
        request.toString()
    }

    @Test
    void purchase_reservation_request_test() {
        GetMerchOfferResponseParser parser = new GetMerchOfferResponseParser(pricedReservationGetMerchOfferResponse)
        List offers = parser.parseOffers()
        Offer offer = offers.get(0)
        PaymentInfo paymentInfo = new PaymentInfo().payment(new CreditCardPayment(YearMonth.of(2024, 12), "4242424242", "34242")).amount(offer.amount).currencyCode(offer.currencyCode).decimalCount(2)
        PurchaseReservationRequest request = new PurchaseReservationRequest(channel: Channel.WEB3,
                sessionId: "sessionId",
                troyaEnvironment: TroyaEnvironment.WEBT,
                pnrNumber: "SAHDYU5",
                surname: "Doe",
                offer: offer,
                paymentInfo: paymentInfo, flow: Flow.PAID_RESERVATION, paymentTrackingId: "paymentTrackingId")
        GPathResult xml = new XmlSlurper().parseText(request.toString())
        assertMessageFunction(xml, 'X')
        assertCoverage(xml, 'reservation')
        assertBasket(xml, 'false')
        assertEmdFareItemCount(xml, 2)
    }

    @Test
    void request_header_should_not_have_payment_tracking_id_when_it_is_null() {
        PaymentInfo paymentInfo = new PaymentInfo().payment(new EFTPayment()).amount(1.toBigDecimal()).currencyCode('TRY').decimalCount(2)
        PurchaseBasketRequest request = new PurchaseBasketRequest(channel: Channel.WEB3,
                sessionId: "sessionId",
                troyaEnvironment: TroyaEnvironment.WEBT,
                pnrNumber: "SAHDYU5",
                surname: "Doe",
                offers: [],
                paymentInfo: paymentInfo, flow: Flow.CASH_TICKETING)
        GPathResult xml = new XmlSlurper().parseText(request.toString())
        assertNodeWithAttributeNotExists(xml, 'extraParameters', 'key', 'PAYMENT_TRACKING_ID')
        assertMessageFunction(xml, 'E')
        assertNodeNotExists(xml, 'Coverage')
        assertBasket(xml, 'false')
    }

    @Test
    void pay_and_fly_coverage() {
        PaymentInfo paymentInfo = new PaymentInfo().payment(new EFTPayment()).amount(1.toBigDecimal()).currencyCode('TRY').decimalCount(2)
        PurchaseBasketRequest request = new PurchaseBasketRequest(channel: Channel.WEB3,
                sessionId: "sessionId",
                troyaEnvironment: TroyaEnvironment.WEBT,
                pnrNumber: "SAHDYU5",
                surname: "Doe",
                offers: [],
                paymentInfo: paymentInfo, flow: Flow.PAY_AND_FLY)
        GPathResult xml = new XmlSlurper().parseText(request.toString())
        assertMessageFunction(xml, 'E')
        assertCoverage(xml, 'payAndFly')
        assertBasket(xml, 'false')
    }

    private void assertMessageFunction(GPathResult xml, String function) {
        assert xml.'**'.find { it.name() == 'MessageFunction' }.@Function.text() == function
    }

    private void assertCoverage(GPathResult xml, String coverage) {
        assert xml.'**'.find { it.name() == 'Coverage' }.text() == coverage
    }

    private void assertBasket(GPathResult xml, String basket) {
        assert xml.'**'.find { it.name() == 'paymentFlowInfo' }.basket.text() == basket
    }

    private void assertEmdFareItemCount(GPathResult xml, int size) {
        assert xml.'**'.findAll { it.name() == 'emdFareItem' }.size() == size
    }

    private void assertNodeNotExists(GPathResult xml, String nodeName) {
        assert xml.'**'.find { it.name() == nodeName } == null
    }

    private void assertNodeWithAttributeNotExists(GPathResult xml, String nodeName, String attributeName, String attributeValue) {
        assert xml.'**'.find { it.name() == nodeName && it.getProperty("@$attributeName").text() == attributeValue } == null
    }
}
