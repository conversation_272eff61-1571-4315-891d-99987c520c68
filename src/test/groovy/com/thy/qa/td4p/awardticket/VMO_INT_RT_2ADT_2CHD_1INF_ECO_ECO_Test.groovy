package com.thy.qa.td4p.awardticket

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class VMO_INT_RT_2ADT_2CHD_1INF_ECO_ECO_Test {

    @Test
    void test() {
        String firstPassengerMsNo = "TK002934055"
        List flights = [new Flight(origin: "IST", destination: "SOF", stopover: 0, dayToFlight: 85, fareType: FareType.ECONOMY),
                        new Flight(origin: "SOF", destination: "IST", stopover: 0, dayToFlight: 95, fareType: FareType.ECONOMY)]

        PassengerCombination passengerCombination = new PassengerCombination(2, 2, 1)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.setAirlineCodes([AirlineCode.TK, AirlineCode.TK])
        String pnrNumber = new GetPnr().getAwardTicketPnr(firstPassengerMsNo, testCaseContext)
        assert pnrNumber
    }

}
