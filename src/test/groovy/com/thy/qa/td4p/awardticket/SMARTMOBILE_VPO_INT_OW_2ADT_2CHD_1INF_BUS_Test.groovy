package com.thy.qa.td4p.awardticket

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class SMARTMOBILE_VPO_INT_OW_2ADT_2CHD_1INF_BUS_Test {

    @Test
    void test() {
        String firstPassengerMsNo = "TK002934055"
        List flights = [new Flight(origin: "BCN", destination: "ADB", stopover: 1, dayToFlight: 77, fareType: FareType.BUSINESS)]
        PassengerCombination passengerCombination = new PassengerCombination(2, 2, 1)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)
        testCaseContext.setChannel(Channel.SMARTMOBILE)
        String pnrNumber = new GetPnr().getVPOAwardTicketPnr(firstPassengerMsNo, testCaseContext)
        assert pnrNumber
    }

}
