package com.thy.qa.td4p.awardticket


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class VMO_DOM_OW_Connecting_2ADT_1CHD_ANY_Test {

    @Test
    void test() {
        String firstPassengerMsNo = "TK002934055"
        List flights = [new Flight(origin: "TZX", destination: "ADB", stopover: 1, dayToFlight: 117, fareType: FareType.ANY)]
        List airlineCodes = [AirlineCode.TK, AirlineCode.TK]
        PassengerCombination passengerCombination = new PassengerCombination([new Passenger(PassengerCode.ADULT),
                                                                              new Passenger(PassengerCode.ADULT),
                                                                              new Passenger(PassengerCode.CHILD)])
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)
        testCaseContext.setAirlineCodes(airlineCodes)
        String pnrNumber = new GetPnr().getAwardTicketPnr(firstPassengerMsNo, testCaseContext)
        assert pnrNumber
    }

}