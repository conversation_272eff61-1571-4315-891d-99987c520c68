package com.thy.qa.td4p.awardticket

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.*
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class SMARTMOBILE_VMO_DOM_RT_Connecting_2ADT_2CHD_1INF_BUS_BUS_Test {

    @Test
    void test() {
        GetPnr gt = new GetPnr()
        String firstPassengerMsNo = "TK002934055"
        List flights = [new Flight(origin: "ADB", destination: "AYT", stopover: 1, dayToFlight: 65, fareType: FareType.ANY, fareClassCodes: [FareClassCode.X, FareClassCode.X]),
                        new Flight(origin: "AYT", destination: "ADB", stopover: 1, dayToFlight: 80, fareType: FareType.ANY, fareClassCodes: [FareClassCode.I, FareClassCode.I])]
        List airlineCodes = [AirlineCode.TK, AirlineCode.TK, AirlineCode.TK, AirlineCode.TK]
        PassengerCombination passengerCombination = new PassengerCombination([new Passenger(PassengerCode.ADULT),
                                                                              new Passenger(PassengerCode.ADULT),
                                                                              new Passenger(PassengerCode.CHILD),
                                                                              new Passenger(PassengerCode.CHILD),
                                                                              new Passenger(PassengerCode.INFANT)])
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.setAirlineCodes(airlineCodes)
        testCaseContext.setChannel(Channel.SMARTMOBILE)
        String pnrNumber = gt.getAwardTicketPnr(firstPassengerMsNo, testCaseContext)
        assert pnrNumber
    }

}
