package com.thy.qa.td4p.extraSeat

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.environment.AWSEnvironment
import com.thy.qa.td4p.environment.BWSEnvironment
import com.thy.qa.td4p.environment.TroyaEnvironment
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class SMARTMOBILE_INT_RT_2ADT_1CHD_1INF_BUS_BUS_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "BCN", destination: "IST", dayToFlight: 40, fareType: FareType.ECONOMY),
                        new Flight(origin: "IST", destination: "BCN", dayToFlight: 55, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination(2, 1, 1)
        passengerCombination.getPax().get(1).setMsNo("TK002934055")
        passengerCombination.getPax().get(0).addExtraSeat()
        passengerCombination.getPax().get(1).addExtraSeat()
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.setChannel(Channel.SMARTMOBILE)
        GetPnr getPnr = new GetPnr().environment(BWSEnvironment.WSPREPROD01).environment(AWSEnvironment.WSPREPROD01).environment(TroyaEnvironment.WEBT)
        String pnrNumber = getPnr.getTicketPnr(testCaseContext)
        assert pnrNumber
    }

}
