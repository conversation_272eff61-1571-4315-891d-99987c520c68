package com.thy.qa.td4p.extraSeat


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class DOM_RT_2ADT_2CHD_1STU_ECO {

    @Test
    public void test() {
        List flights = [
                new Flight(origin: "GZT", destination: "TZX", stopover: 1, dayToFlight: 5, fareType: FareType.ECONOMY),
                new Flight(origin: "TZX", destination: "GZT", stopover: 1, dayToFlight: 12, fareType: FareType.ECOFLY)
        ]
        List airlineCodes = [AirlineCode.TK, AirlineCode.TK]
        Passenger pax1 = new Passenger(PassengerCode.CHILD)
        Passenger pax2 = new Passenger(PassengerCode.ADULT)
        Passenger pax3 = new Passenger(PassengerCode.CHILD)
        Passenger pax4 = new Passenger(PassengerCode.ADULT)
        Passenger pax5 = new Passenger(PassengerCode.STUDENT)

        PassengerCombination passengerCombination = new PassengerCombination([pax1, pax2, pax3, pax4, pax5])
        passengerCombination.getPax().get(1).addExtraSeat()
        passengerCombination.getPax().get(4).addExtraSeat()
        //passengerCombination.getPax().get(1).setMsNo("TK457420184")
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.setAirlineCodes(airlineCodes)
        String pnrNumber = new GetPnr().getTicketPnr(testCaseContext)
        assert pnrNumber
    }
}
