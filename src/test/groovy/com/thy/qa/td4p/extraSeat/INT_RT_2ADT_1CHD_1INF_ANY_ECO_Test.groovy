package com.thy.qa.td4p.extraSeat

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class INT_RT_2ADT_1CHD_1INF_ANY_ECO_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "IST", destination: "FCO", dayToFlight: 91, fareType: FareType.ECONOMY),
                        new Flight(origin: "FCO", destination: "IST", dayToFlight: 99, fareType: FareType.ECONOMY)]
        List airlineCodes = [AirlineCode.TK, AirlineCode.TK]
        PassengerCombination passengerCombination = new PassengerCombination(2, 1, 1)
        passengerCombination.getPax().get(1).setMsNo("TK002934055")
        passengerCombination.getPax().get(0).addExtraSeat()
        passengerCombination.getPax().get(1).addExtraSeat()
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.setAirlineCodes(airlineCodes)
        String pnrNumber = new GetPnr().getTicketPnr(testCaseContext)
        assert pnrNumber
    }
}