package com.thy.qa.td4p.extraSeat

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class DOM_MC_2ADT_2CHD_1INFANT_ECO_PureTK_ExtraSeat_Test {

    @Test
    public void test() {
        List flights = [
                new Flight(origin: "GZT", destination: "TZX", stopover: 1, dayToFlight: 17, fareType: FareType.EXTRAFLY),
                new Flight(origin: "ADB", destination: "ERZ", stopover: 1, dayToFlight: 20, fareType: FareType.ECONOMY)
        ]
        List airlineCodes = [AirlineCode.TK, AirlineCode.TK, AirlineCode.TK, AirlineCode.TK]

        Passenger pax1 = new Passenger(PassengerCode.ADULT)
        Passenger pax2 = new Passenger(PassengerCode.CHILD)
        Passenger pax3 = new Passenger(PassengerCode.INFANT)
        Passenger pax4 = new Passenger(PassengerCode.CHILD)
        Passenger pax5 = new Passenger(PassengerCode.ADULT)
        PassengerCombination passengerCombination = new PassengerCombination([pax1, pax2, pax3, pax4, pax5])
        passengerCombination.getPax().get(0).addExtraSeat()
        passengerCombination.getPax().get(3).addExtraSeat()

        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.MULTICITY, flights, passengerCombination)
        testCaseContext.setAirlineCodes(airlineCodes)
        String pnrNumber = new GetPnr().getTicketPnr(testCaseContext)
        assert pnrNumber
    }
}
