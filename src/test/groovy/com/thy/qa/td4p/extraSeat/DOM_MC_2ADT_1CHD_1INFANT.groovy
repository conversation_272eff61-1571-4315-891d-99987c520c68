package com.thy.qa.td4p.extraSeat

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.merchandising.offer.service.Unit
import com.thy.qa.td4p.merchandising.request.ExtraBaggage
import com.thy.qa.td4p.merchandising.request.seat.ExitSeat
import com.thy.qa.td4p.merchandising.request.seat.SeatPosition
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class DOM_MC_2ADT_1CHD_1INFANT {

    @Test
    void test() {
        List flights = [new Flight(origin: "IST", destination: "TZX", stopover: 0, dayToFlight: 11, fareType: FareType.ECONOMY)]
        List airlineCodes = [AirlineCode.TK, AirlineCode.ANY, AirlineCode.TK, AirlineCode.ANY]
        PassengerCombination passengerCombination = new PassengerCombination(3, 1, 1)
        passengerCombination.getPax().get(0).addExtraSeat()
        //passengerCombination.getPax().get(1).addExtraSeat()
        //passengerCombination.getPax().get(2).addExtraSeat()
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)
        testCaseContext.setAirlineCodes(airlineCodes)
        testCaseContext.addServiceRequest(new ExitSeat(3, 1, SeatPosition.MIDDLE))
        testCaseContext.addServiceRequest(new ExtraBaggage(2, 1, 30, Unit.KILOGRAM))
        GetPnr getPnr = new GetPnr().retryCount(10)
        String pnrNumber = getPnr.getTicketPnr(testCaseContext)
        assert pnrNumber
    }

}
