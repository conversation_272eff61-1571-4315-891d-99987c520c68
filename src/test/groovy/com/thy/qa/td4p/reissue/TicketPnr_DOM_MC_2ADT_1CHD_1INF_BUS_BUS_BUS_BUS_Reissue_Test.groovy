package com.thy.qa.td4p.reissue


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.ReissueContext
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class TicketPnr_DOM_MC_2ADT_1CHD_1INF_BUS_BUS_BUS_BUS_Reissue_Test {

    @Test
    void test() {
        GetPnr gt = new GetPnr()
        List airlineCodes = [AirlineCode.TK, AirlineCode.TK, AirlineCode.TK, AirlineCode.TK]
        List flights = [
                new Flight(origin: "AYT", destination: "IST", stopover: 0, dayToFlight: 78, fareType: FareType.BUSINESS),
                new Flight(origin: "ADB", destination: "IST", stopover: 0, dayToFlight: 97, fareType: FareType.BUSINESS),
                new Flight(origin: "ESB", destination: "IST", stopover: 0, dayToFlight: 107, fareType: FareType.BUSINESS),
                new Flight(origin: "IST", destination: "TZX", stopover: 0, dayToFlight: 116, fareType: FareType.BUSINESS)
        ]
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.MULTICITY, flights, new PassengerCombination(2, 1, 1))
        testCaseContext.setAirlineCodes(airlineCodes)
        String pnrNumber = gt.getTicketPnr(testCaseContext)
        assert pnrNumber

        List reissueflights = [
                new Flight(origin: "COV", destination: "IST", stopover: 1, dayToFlight: 80, fareType: FareType.BUSINESS),
                new Flight(origin: "IST", destination: "DIY", stopover: 1, dayToFlight: 100, fareType: FareType.BUSINESS),
                new Flight(origin: "DIY", destination: "IST", stopover: 0, dayToFlight: 110, fareType: FareType.BUSINESS)
        ]
        ReissueContext reissueContext = new ReissueContext(RoutingType.MULTICITY, reissueflights, [1, 2, 3])
        String reissuePnr = gt.getReissuedPnr(pnrNumber, gt.getSurname(), reissueContext)
        assert reissuePnr
    }

}
