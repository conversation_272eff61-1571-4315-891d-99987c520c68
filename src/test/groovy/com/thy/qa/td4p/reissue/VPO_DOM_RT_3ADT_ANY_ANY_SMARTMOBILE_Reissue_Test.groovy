package com.thy.qa.td4p.reissue


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.ReissueContext
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

import java.time.Duration

class VPO_DOM_RT_3ADT_ANY_ANY_SMARTMOBILE_Reissue_Test {

    @Test
    void test() {
        String firstPassengerMsNo = "TK002934055"
        List flights = [
                new Flight(origin: "IST", destination: "ADB", dayToFlight: 62, fareType: FareType.ECONOMY),
                new Flight(origin: "ADB", destination: "IST", dayToFlight: 68)
        ]
        PassengerCombination passengerCombination = new PassengerCombination()
        passengerCombination.addPassenger(PassengerCode.ADULT, 3)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        GetPnr gt = new GetPnr()
        String pnrNumber = gt.getVPOAwardTicketPnr(firstPassengerMsNo, testCaseContext)
        assert pnrNumber

        List reissueFlights = [
                new Flight(origin: "IST", destination: "ADB", dayToFlight: 72, fareType: FareType.ECONOMY),
                new Flight(origin: "ADB", destination: "IST", dayToFlight: 78)
        ]

        ReissueContext reissueContext = new ReissueContext(RoutingType.ROUNDTRIP, reissueFlights, [1, 2])
        reissueContext.setChannel(Channel.SMARTMOBILE)
        reissueContext.setReissueDelay(Duration.ofSeconds(7))
        String reissuePnr = new GetPnr().getReissuedPnr(pnrNumber, gt.getSurname(), reissueContext)
        assert reissuePnr
    }

}
