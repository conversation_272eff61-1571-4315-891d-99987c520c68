package com.thy.qa.td4p.reissue


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.ReissueContext
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class TicketPnr_INT_RT_3ADT_1CHD_BUS_ECO_Reissue_Test {

    @Test
    void test() {
        GetPnr gt = new GetPnr()
        List flights = [new Flight(origin: "BCN", destination: "IST", dayToFlight: 95, fareType: FareType.BUSINESS),
                        new Flight(origin: "CDG", destination: "BCN", dayToFlight: 110, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination(3, 1, 0)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.MULTICITY, flights, passengerCombination)
        String pnrNumber = gt.getTicketPnr(testCaseContext)
        assert pnrNumber

        List reissueFlights = [new Flight(origin: "IST", destination: "CDG", dayToFlight: 110, fareType: FareType.ECONOMY)]

        ReissueContext reissueContext = new ReissueContext(RoutingType.ONEWAY, reissueFlights, [2])
        String reissuePnr = gt.getReissuedPnr(pnrNumber, gt.getSurname(), reissueContext)
        assert reissuePnr
    }
}