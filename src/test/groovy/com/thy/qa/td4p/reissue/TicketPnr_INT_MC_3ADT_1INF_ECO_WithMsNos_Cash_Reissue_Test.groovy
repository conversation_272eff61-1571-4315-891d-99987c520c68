package com.thy.qa.td4p.reissue


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.ReissueContext
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import com.thy.qa.td4p.payments.CashPayment
import org.junit.Test

class TicketPnr_INT_MC_3ADT_1INF_ECO_WithMsNos_Cash_Reissue_Test {

    @Test
    void test() {
        GetPnr gt = new GetPnr()
        List flights = [
                new Flight(origin: "IST", destination: "CPT", dayToFlight: 160, fareType: FareType.ECONOMY),
                new Flight(origin: "CPT", destination: "IST", dayToFlight: 170, fareType: FareType.ECONOMY),
                new Flight(origin: "IST", destination: "BCN", dayToFlight: 180, fareType: FareType.ECONOMY)
        ]

        PassengerCombination passengerCombination = new PassengerCombination(3, 0, 1)
        passengerCombination.getPax().get(0).setMsNo("TK001359044")
        passengerCombination.getPax().get(2).setMsNo("TK300792047")
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.MULTICITY, flights, passengerCombination)
        testCaseContext.setPayment(new CashPayment())

        String pnrNumber = gt.getTicketPnr(testCaseContext)
        assert pnrNumber

        ReissueContext reissueContext = new ReissueContext([1, 3])
        reissueContext.setPayment(new CashPayment())
        String reissuePnr = gt.getReissuedPnr(pnrNumber, gt.getSurname(), reissueContext)
        assert reissuePnr
    }

}
