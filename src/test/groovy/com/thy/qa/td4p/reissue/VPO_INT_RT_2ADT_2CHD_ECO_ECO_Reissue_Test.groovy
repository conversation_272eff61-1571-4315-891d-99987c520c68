package com.thy.qa.td4p.reissue


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.ReissueContext
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class VPO_INT_RT_2ADT_2CHD_ECO_ECO_Reissue_Test {

    @Test
    void test() {
        GetPnr gt = new GetPnr()
        String firstPassengerMsNo = "TK002934055"
        List flights = [new Flight(origin: "BCN", destination: "IST", dayToFlight: 73, fareType: FareType.ECONOMY),
                        new Flight(origin: "IST", destination: "BCN", dayToFlight: 83, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination(2, 2, 0)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)

        String pnrNumber = gt.getVPOAwardTicketPnr(firstPassengerMsNo, testCaseContext)
        assert pnrNumber

        List<Flight> addedFlights = [new Flight(origin: "CDG", destination: "IST", dayToFlight: 61, fareType: FareType.ECONOMY_PROMOTIONAL)]

        ReissueContext reissueContext = new ReissueContext(RoutingType.ONEWAY, addedFlights, [1])
        String reissuePnr = gt.getReissuedPnr(pnrNumber, gt.getSurname(), reissueContext)
        assert reissuePnr
    }

}
