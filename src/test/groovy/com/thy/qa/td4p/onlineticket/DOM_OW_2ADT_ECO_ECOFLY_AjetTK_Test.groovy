package com.thy.qa.td4p.onlineticket

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class DOM_OW_2ADT_ECO_ECOFLY_AjetTK_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "DIY", destination: "ADB", stopover: 1, dayToFlight: 47, fareType: FareType.ECONOMY)]
        List airlineCodes = [AirlineCode.VF,
                             AirlineCode.TK]
        PassengerCombination passengerCombination = new PassengerCombination(adult: 1)
        passengerCombination.getPax().get(0).setMsNo("TK457420184")
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)
        testCaseContext.setAirlineCodes(airlineCodes)
        String pnrNumber = new GetPnr().getTicketPnr(testCaseContext)
        assert pnrNumber
    }

}
