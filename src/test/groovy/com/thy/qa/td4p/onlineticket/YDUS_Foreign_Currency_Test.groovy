package com.thy.qa.td4p.onlineticket


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import com.thy.qa.td4p.payments.CreditCardPayment
import org.junit.Test

import java.time.LocalDate
import java.time.YearMonth

class YDUS_Foreign_Currency_Test {

    @Test
    void foreign_currency_test() {
        List flights = [new Flight(origin: "FRA", destination: "IST", dayToFlight: 45, fareType: FareType.ECONOMY),
                        new Flight(origin: "IST", destination: "FAR", dayToFlight: 50, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination([new Passenger(PassengerCode.ADULT)])
        passengerCombination.getPax().get(0).setBirthDate(LocalDate.of(1986, 3, 8))
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.setPayment(new CreditCardPayment(YearMonth.of(2024, 9), '3455', '***************'))
        testCaseContext.setAirlineCodes([AirlineCode.TK, AirlineCode.TK])
        String pnrNumber = new GetPnr().getTicketPnr(testCaseContext)
        assert pnrNumber
    }

}
