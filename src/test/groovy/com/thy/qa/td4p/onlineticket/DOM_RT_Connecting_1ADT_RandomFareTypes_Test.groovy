package com.thy.qa.td4p.onlineticket

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class DOM_RT_Connecting_1ADT_RandomFareTypes_Test {

    @Test
    void test() {
        List flights = [
                new Flight(origin: "GZT", destination: "AYT", stopover: 1, dayToFlight: 58),
                new Flight(origin: "AYT", destination: "GZT", stopover: 1, dayToFlight: 67)
        ]
        List airlineCodes = [AirlineCode.TK, AirlineCode.TK, AirlineCode.AJ, AirlineCode.AJ]
        PassengerCombination passengerCombination = new PassengerCombination()
        passengerCombination.addPassenger(PassengerCode.ADULT, 1)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.setAirlineCodes(airlineCodes)
        String pnrNumber = new GetPnr().getTicketPnr(testCaseContext)
        assert pnrNumber
    }
}