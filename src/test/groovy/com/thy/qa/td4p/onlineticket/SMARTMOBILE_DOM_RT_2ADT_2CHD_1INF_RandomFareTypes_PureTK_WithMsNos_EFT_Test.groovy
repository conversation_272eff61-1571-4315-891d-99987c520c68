package com.thy.qa.td4p.onlineticket

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import com.thy.qa.td4p.payments.EFTPayment
import org.junit.Test

class SMARTMOBILE_DOM_RT_2ADT_2CHD_1INF_RandomFareTypes_PureTK_WithMsNos_EFT_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "IST", destination: "DIY", stopover: 0, dayToFlight: 7),
                        new Flight(origin: "DIY", destination: "IST", stopover: 0, dayToFlight: 10)]
        List airlineCodes = [AirlineCode.TK, AirlineCode.TK]
        PassengerCombination passengerCombination = new PassengerCombination(2, 2, 1)
        passengerCombination.getPax().get(0).setMsNo("TK002934055")
        passengerCombination.getPax().get(1).setMsNo("TK001221704")
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.setAirlineCodes(airlineCodes)
        testCaseContext.setPayment(new EFTPayment())
        testCaseContext.setChannel(Channel.SMARTMOBILE)
        String pnrNumber = new GetPnr().getTicketPnr(testCaseContext)
        assert pnrNumber
    }
}