package com.thy.qa.td4p.onlineticket

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.environment.AWSEnvironment
import com.thy.qa.td4p.environment.BWSEnvironment
import com.thy.qa.td4p.environment.TroyaEnvironment
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class SMARTMOBILE_INT_RT_2ADT_1CHD_1INF_BUS_BUS_WithMsNos_Test {

    @Test
    void test() {
        List flights = [
                new Flight(origin: "BCN", destination: "IST", dayToFlight: 90, fareType: FareType.ANY),
                new Flight(origin: "IST", destination: "BCN", dayToFlight: 99, fareType: FareType.ANY)
        ]
        PassengerCombination passengerCombination = new PassengerCombination(2, 1, 1)
        passengerCombination.getPax().get(1).setMsNo("TK002934055")
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.setChannel(Channel.SMARTMOBILE)
        //testCaseContext.setContextParameters(context)
        GetPnr.setEnvironment(BWSEnvironment.WSPREPROD01)
        GetPnr getPnr = new GetPnr().environment(AWSEnvironment.WSDEV01).environment(BWSEnvironment.WSPREPROD01).environment(TroyaEnvironment.WEBT).retryCount(5)
        String pnrNumber = getPnr.getTicketPnr(testCaseContext)
        assert pnrNumber
    }
}