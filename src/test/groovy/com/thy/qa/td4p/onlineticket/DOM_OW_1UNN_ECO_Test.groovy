package com.thy.qa.td4p.onlineticket

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import com.thy.qa.td4p.payments.CreditCardPayment
import org.junit.Test

import java.time.YearMonth

class DOM_OW_1UNN_ECO_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "ERZ", destination: "IST", stopover: 0, dayToFlight: 6, fareType: FareType.ECONOMY)]
        List airlineCodes = [AirlineCode.TK]
        CreditCardPayment payment = new CreditCardPayment(YearMonth.of(2030, 02), "123", "****************")
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, new PassengerCombination([new Passenger(PassengerCode.ADULT)]))
        testCaseContext.setAirlineCodes(airlineCodes)
        testCaseContext.setPayment(payment)
        String pnrNumber = new GetPnr().getTicketPnr(testCaseContext)
        assert pnrNumber
    }
}
