package com.thy.qa.td4p.onlineticket

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.FareClassCode
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class DOM_MC_2ADT_ECO_ECOFLY_PureAjet_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "IST", destination: "ESB", dayToFlight: 15, fareType: FareType.ECONOMY, stopover: 0, fareClassCodes: [FareClassCode.T]),
                        /*new Flight(origin: "ESB", destination: "ADB", dayToFlight: 18, fareType: FareType.ECOFLY, stopover: 1)*/]
        List airlineCodes = [AirlineCode.TK, AirlineCode.ANY, AirlineCode.ANY, AirlineCode.ANY]
        PassengerCombination passengerCombination = new PassengerCombination(adult: 2)
        //passengerCombination.getPax().get(1).setMsNo("TK457420184")
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)
        testCaseContext.setAirlineCodes(airlineCodes)
        String pnrNumber = new GetPnr().getTicketPnr(testCaseContext)
        assert pnrNumber
    }

}
