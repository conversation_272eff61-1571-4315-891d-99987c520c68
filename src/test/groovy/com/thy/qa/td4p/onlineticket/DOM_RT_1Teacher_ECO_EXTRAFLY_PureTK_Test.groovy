package com.thy.qa.td4p.onlineticket


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class DOM_RT_1Teacher_ECO_EXTRAFLY_PureTK_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "IST", destination: "AYT", dayToFlight: 52, fareType: FareType.ECONOMY),
                        new Flight(origin: "AYT", destination: "IST", dayToFlight: 59, fareType: FareType.EXTRAFLY)]
        List airlineCodes = [AirlineCode.TK, AirlineCode.TK]
        PassengerCombination passengerCombination = new PassengerCombination([new Passenger(PassengerCode.TEACHER)])
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.setAirlineCodes(airlineCodes)
        String pnrNumber = new GetPnr().getTicketPnr(testCaseContext)
        assert pnrNumber
    }

}
