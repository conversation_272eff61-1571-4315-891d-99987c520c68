package com.thy.qa.td4p.onlineticket


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import com.thy.qa.td4p.payments.CreditCardPayment
import org.junit.Test

import java.time.YearMonth

class SMARTMOBILE_DOM_OW_1ADT_ECO_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "ADB", destination: "IST", stopover: 0, dayToFlight: 6, fareType: FareType.ECONOMY)]
        CreditCardPayment payment = new CreditCardPayment(YearMonth.of(2030, 02), "123", "****************")
        TestCaseContext tc = new TestCaseContext(RoutingType.ONEWAY, flights, new PassengerCombination([new Passenger(PassengerCode.ADULT)]))
        tc.setPayment(payment)
        tc.setChannel(Channel.SMARTMOBILE)
        String pnrNumber = new GetPnr().getTicketPnr(tc)
        assert pnrNumber
    }

}
