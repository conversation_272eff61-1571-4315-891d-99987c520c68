package com.thy.qa.td4p.onlineticket

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class DOM_RT_4ADT_2CHD_2INF_ANY_ANY_PureTK_Test {

    @Test
    void test() {
        List flights = [
                new Flight(origin: "COV", destination: "DIY", stopover: 1, dayToFlight: 32),
                new Flight(origin: "DIY", destination: "COV", stopover: 1, dayToFlight: 47)
        ]
        List airlineCodes = [
                AirlineCode.TK,
                AirlineCode.TK,
                AirlineCode.TK,
                AirlineCode.TK
        ]

        PassengerCombination passengerCombination = new PassengerCombination(adult: 4, child: 2, infant: 2)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.setAirlineCodes(airlineCodes)

        String pnrNumber = new GetPnr().getTicketPnr(testCaseContext)
        assert pnrNumber
    }
}