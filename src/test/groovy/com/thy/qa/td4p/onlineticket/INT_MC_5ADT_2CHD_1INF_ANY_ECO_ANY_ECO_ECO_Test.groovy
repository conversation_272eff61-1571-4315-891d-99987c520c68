package com.thy.qa.td4p.onlineticket

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class INT_MC_5ADT_2CHD_1INF_ANY_ECO_ANY_ECO_ECO_Test {

    @Test
    void test() {
        GetPnr gt = new GetPnr().retryCount(10)
        List flights = [new Flight(origin: "IST", destination: "CPT", stopover: 0, dayToFlight: 54),
                        new Flight(origin: "CPT", destination: "IST", stopover: 0, dayToFlight: 60, fareType: FareType.ECONOMY),
                        new Flight(origin: "IST", destination: "VIE", stopover: 0, dayToFlight: 77),
                        new Flight(origin: "VIE", destination: "IST", stopover: 0, dayToFlight: 89),
                        new Flight(origin: "IST", destination: "ESB", stopover: 0, dayToFlight: 93, fareType: FareType.ECONOMY)]
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.MULTICITY, flights, new PassengerCombination(5, 2, 1))
        String pnrNumber = gt.getTicketPnr(testCaseContext)
        assert pnrNumber
    }

}
