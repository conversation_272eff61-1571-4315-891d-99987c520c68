package com.thy.qa.td4p.onlineticket

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class DOM_RT_3ADT_1SB_1INF_ECO_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "AYT", destination: "ADB", stopover: 1, dayToFlight: 167, fareType: FareType.ECONOMY),
                        new Flight(origin: "ADB", destination: "AYT", stopover: 1, dayToFlight: 171, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination()
        passengerCombination.addPassenger(PassengerCode.ADULT, 3)
        passengerCombination.addPassenger(PassengerCode.DISABLED, 1)
        passengerCombination.addPassenger(PassengerCode.INFANT, 1)
        passengerCombination.getPax().get(3).setMsNo("TK420542414")
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.setAirlineCodes([AirlineCode.ANY, AirlineCode.ANY, AirlineCode.ANY, AirlineCode.ANY])
        testCaseContext.setContactInfo(4, TestCaseContext.contactEmail(), TestCaseContext.contactMobilePhoneNumber())
        String pnrNumber = new GetPnr().getTicketPnr(testCaseContext)
        assert pnrNumber
    }

}
