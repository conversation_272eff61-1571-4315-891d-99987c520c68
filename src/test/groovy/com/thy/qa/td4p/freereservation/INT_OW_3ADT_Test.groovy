package com.thy.qa.td4p.freereservation

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class INT_OW_3ADT_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "JFK", destination: "IST", dayToFlight: 7)]
        PassengerCombination passengers = new PassengerCombination(3, 0, 0)
        passengers.getPax().get(1).setMsNo("TK001221704")
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengers)
        String pnrNumber = new GetPnr().getFreeReservationPnr(testCaseContext)
        assert pnrNumber
    }

}
