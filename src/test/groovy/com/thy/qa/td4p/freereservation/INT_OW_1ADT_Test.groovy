package com.thy.qa.td4p.freereservation

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class INT_OW_1ADT_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "JFK", destination: "IST", stopover: 0, dayToFlight: 99)]
        PassengerCombination passengers = new PassengerCombination([new Passenger(PassengerCode.ADULT)])
        TestCaseContext context = new TestCaseContext(RoutingType.ONEWAY, flights, passengers)
        String pnrNumber = new GetPnr().getFreeReservationPnr(context)
        assert pnrNumber
    }

}
