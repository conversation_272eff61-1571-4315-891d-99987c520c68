package com.thy.qa.td4p.freereservation

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class SMARTMOBILE_DOM_RT_3ADT_2CHD_3INF_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "KCM", destination: "IST", dayToFlight: 75, fareType: FareType.ECONOMY),
                        new Flight(origin: "IST", destination: "KCM", dayToFlight: 90, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination(3, 2, 3)
        passengerCombination.getPax().get(0).setMsNo("TK001221704")
        passengerCombination.getPax().get(1).setMsNo("TK001970640")
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.setChannel(Channel.SMARTMOBILE)
        String pnrNumber = new GetPnr().getFreeReservationPnr(testCaseContext)
        assert pnrNumber
    }

}