package com.thy.qa.td4p.freereservation

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class INT_OW_2ADT_1CHD_1INF_BUS_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "JFK", destination: "IST", dayToFlight: 99, fareType: FareType.BUSINESS)]
        PassengerCombination passengers = new PassengerCombination(2, 2, 1)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengers)
        String pnrNumber = new GetPnr().getFreeReservationPnr(testCaseContext)
        assert pnrNumber
    }

}
