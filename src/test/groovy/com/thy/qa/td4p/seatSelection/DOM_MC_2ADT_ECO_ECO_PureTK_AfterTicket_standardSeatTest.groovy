package com.thy.qa.td4p.seatSelection

import com.eviware.soapui.model.testsuite.TestRunner
import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.merchandising.offer.service.Unit
import com.thy.qa.td4p.merchandising.request.ExtraBaggage
import com.thy.qa.td4p.merchandising.request.ServiceRequestsContext
import com.thy.qa.td4p.merchandising.request.seat.ExitSeat
import com.thy.qa.td4p.merchandising.request.seat.SeatPosition
import com.thy.qa.td4p.passenger.PassengerCombination
import com.thy.qa.td4p.payments.CashPayment
import org.junit.Test

class DOM_MC_2ADT_ECO_ECO_PureTK_AfterTicket_standardSeatTest {

    @Test
    void test() {
        List flights = [new Flight(origin: "ESB", destination: "IST", dayToFlight: 25, fareType: FareType.ECOFLY)]
        List airlineCodes = [AirlineCode.TK, AirlineCode.TK]
        PassengerCombination passengerCombination = new PassengerCombination(adult: 2, infant: 1)
        passengerCombination.getPax().get(0).addExtraSeat()

        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)
        testCaseContext.setPayment(new CashPayment())
        testCaseContext.setAirlineCodes(airlineCodes)

        GetPnr getPnr = new GetPnr()
        String pnrNumber = getPnr.getTicketPnr(testCaseContext)
        String surname = getPnr.getSurname()
        assert pnrNumber

        GetPnr ancillaryAddGetPnr = new GetPnr()
        ExitSeat exitSeat = new ExitSeat(2, 1, SeatPosition.WINDOW)
        List ancillaryServices = [exitSeat, new ExtraBaggage(1, 1, 56, Unit.KILOGRAM)]
        ServiceRequestsContext ancillaryServicesContext = new ServiceRequestsContext(ancillaryServices)
        ancillaryServicesContext.setPayment(new CashPayment())
        ancillaryAddGetPnr.purchaseAncillaryServices(pnrNumber, surname, ancillaryServicesContext)
        assert ancillaryAddGetPnr.runner.status == TestRunner.Status.FINISHED
    }

}
