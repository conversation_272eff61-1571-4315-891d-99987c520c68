package com.thy.qa.td4p.seatSelection

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.merchandising.request.SportsEquipment
import com.thy.qa.td4p.merchandising.request.seat.*
import com.thy.qa.td4p.merchandising.speq.SportsEquipmentType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class DOM_MC_2ADT_1CHD_1INF_ECO_ECOFLY_Ticketing {

    @Test
    void test() {
        List flights = [new Flight(origin: "IST", destination: "ADB", dayToFlight: 35, fareType: FareType.ECOFLY),
                        new Flight(origin: "ADB", destination: "IST", dayToFlight: 46, fareType: FareType.ECOFLY)]
        List airlineCodes = [AirlineCode.TK, AirlineCode.TK]
        PassengerCombination passengerCombination = new PassengerCombination(adult: 3, child: 1, infant: 1)
        //passengerCombination.getPax().get(1).addExtraSeat()

        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.setAirlineCodes(airlineCodes)

        StandardSeat standardSeat = new StandardSeat(1, 1, SeatPosition.WINDOW)
        BabyBassinetSeat babyBassinetSeat = new BabyBassinetSeat(1, 2)
        LegroomSeat legroomSeat = new LegroomSeat(2, 2)
        ExitSeat exitSeat2 = new ExitSeat(3, 2)
        exitSeat2.setSeatPosition(SeatPosition.WINDOW)
        SportsEquipment sportsEquipment = new SportsEquipment(2, 1, 1, SportsEquipmentType.BIKE)

        List serviceRequests = [standardSeat, exitSeat2, sportsEquipment]

        testCaseContext.setServiceRequests(serviceRequests)
        testCaseContext.setChannel(Channel.QRES_QII)

        GetPnr gt = new GetPnr()
        String pnrNumber = gt.getTicketPnr(testCaseContext)
        assert pnrNumber
    }

}
