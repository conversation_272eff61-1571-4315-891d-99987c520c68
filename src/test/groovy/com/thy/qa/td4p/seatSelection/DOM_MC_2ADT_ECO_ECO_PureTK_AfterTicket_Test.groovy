package com.thy.qa.td4p.seatSelection

import com.eviware.soapui.model.testsuite.TestRunner
import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.merchandising.offer.service.Unit
import com.thy.qa.td4p.merchandising.request.ExtraBaggage
import com.thy.qa.td4p.merchandising.request.ServiceRequestsContext
import com.thy.qa.td4p.merchandising.request.SportsEquipment
import com.thy.qa.td4p.merchandising.request.seat.ExitSeat
import com.thy.qa.td4p.merchandising.request.seat.StandardSeat
import com.thy.qa.td4p.merchandising.speq.SportsEquipmentType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class DOM_MC_2ADT_ECO_ECO_PureTK_AfterTicket_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "IST", destination: "AYT", dayToFlight: 65, fareType: FareType.ECONOMY),
                        new Flight(origin: "AYT", destination: "IST", dayToFlight: 68, fareType: FareType.ECONOMY)]
        List airlineCodes = [AirlineCode.TK, AirlineCode.TK]
        PassengerCombination passengerCombination = new PassengerCombination(adult: 2)

        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.setAirlineCodes(airlineCodes)

        GetPnr getPnr = new GetPnr()
        testCaseContext.addServiceRequest(new StandardSeat(1, 1))
        String pnrNumber = getPnr.getTicketPnr(testCaseContext)
        String surname = getPnr.getSurname()
        assert pnrNumber

        GetPnr getPnr1 = new GetPnr()
        ExtraBaggage extraBaggage = new ExtraBaggage(1, 1, 20, Unit.KILOGRAM)
        SportsEquipment sportsEquipment = new SportsEquipment(1, 1, 2, SportsEquipmentType.BIKE)
        StandardSeat standardSeat2 = new StandardSeat(1, 2)
        ExitSeat exitSeat = new ExitSeat(2, 2)
        List ancillaryServices = [sportsEquipment, standardSeat2, exitSeat, extraBaggage]
        ServiceRequestsContext ancillaryServicesContext = new ServiceRequestsContext(ancillaryServices)
        getPnr1.purchaseAncillaryServices(pnrNumber, surname, ancillaryServicesContext)
        assert getPnr1.runner.status == TestRunner.Status.FINISHED

        GetPnr getPnr3 = new GetPnr()
        SportsEquipment sportsEquipment3 = new SportsEquipment(1, 1, 2, SportsEquipmentType.BIKE)
        List ancillaryServices2 = [sportsEquipment3]
        ServiceRequestsContext ancillaryServicesContext2 = new ServiceRequestsContext(ancillaryServices2)
        getPnr3.purchaseAncillaryServices(pnrNumber, surname, ancillaryServicesContext2)
        assert getPnr3.runner.status == TestRunner.Status.FINISHED
    }

}
