package com.thy.qa.td4p.seatSelection

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.merchandising.offer.service.Unit
import com.thy.qa.td4p.merchandising.request.ExtraBaggage
import com.thy.qa.td4p.merchandising.request.seat.ExitSeat
import com.thy.qa.td4p.merchandising.request.seat.LegroomSeat
import com.thy.qa.td4p.merchandising.request.seat.SeatPosition
import com.thy.qa.td4p.merchandising.request.seat.StandardSeat
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class DOM_MC_2ADT_1CHD_ECO_ECOFLY_Ticketing {

    @Test
    void test() {
        List flights = [new Flight(origin: "IST", destination: "GZT", dayToFlight: 35, fareType: FareType.ECONOMY),
                        new Flight(origin: "ADB", destination: "IST", dayToFlight: 36, fareType: FareType.ECOFLY)]
        List airlineCodes = [AirlineCode.TK, AirlineCode.TK]
        PassengerCombination passengerCombination = new PassengerCombination(adult: 2, child: 1)

        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.MULTICITY, flights, passengerCombination)
        testCaseContext.setAirlineCodes(airlineCodes)

        StandardSeat standardSeat = new StandardSeat(1, 1)
        LegroomSeat extraLegroomSeat = new LegroomSeat(2, 1)
        ExitSeat exitSeat2 = new ExitSeat(2, 2)
        exitSeat2.setSeatPosition(SeatPosition.WINDOW)
        ExtraBaggage extraBaggage = new ExtraBaggage(2, 1, 40, Unit.KILOGRAM)

        testCaseContext.addServiceRequest(standardSeat)
        testCaseContext.addServiceRequest(extraLegroomSeat)
        testCaseContext.addServiceRequest(exitSeat2)
        testCaseContext.addServiceRequest(extraBaggage)

        GetPnr getPnr = new GetPnr()

        String pnrNumber = getPnr.getTicketPnr(testCaseContext)
        assert pnrNumber
    }

}
