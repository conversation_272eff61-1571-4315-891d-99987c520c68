package com.thy.qa.td4p.merchandising

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.merchandising.pet.PetType
import com.thy.qa.td4p.merchandising.request.PetCarriage
import com.thy.qa.td4p.merchandising.request.SportsEquipment
import com.thy.qa.td4p.merchandising.speq.SportsEquipmentType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class AVIH_Test {
    @Test
    void add_petc_for_a_flight() {
        List flights = [new Flight(origin: "ADB", destination: "COV", dayToFlight: 30, fareType: FareType.ECONOMY, stopover: 1),
                        new Flight(origin: "COV", destination: "ADB", dayToFlight: 36, fareType: FareType.ECONOMY, stopover: 1)]
        PassengerCombination passengerCombination = new PassengerCombination(adult: 1, infant: 1)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        GetPnr getPnr = new GetPnr()
        testCaseContext.addServiceRequest(new PetCarriage(1, 1, PetType.DOG_AIRCRAFT))
        testCaseContext.addServiceRequest(new PetCarriage(1, 2, PetType.DOG_AIRCRAFT))
        testCaseContext.addServiceRequest(new SportsEquipment(1, 1, 1, SportsEquipmentType.BIKE))
        String pnrNumber = getPnr.getTicketPnr(testCaseContext)
        assert pnrNumber
    }

}
