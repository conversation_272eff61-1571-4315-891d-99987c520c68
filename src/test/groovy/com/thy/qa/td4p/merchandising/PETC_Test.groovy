package com.thy.qa.td4p.merchandising

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.merchandising.pet.PetType
import com.thy.qa.td4p.merchandising.request.PetCarriage
import com.thy.qa.td4p.merchandising.request.seat.ExitSeat
import com.thy.qa.td4p.merchandising.request.seat.SeatPosition
import com.thy.qa.td4p.merchandising.request.seat.StandardSeat
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class PETC_Test {
    @Test
    void add_petc_for_a_flight() {
        List flights = [new Flight(origin: "IST", destination: "ASR", dayToFlight: 28, fareType: FareType.ECONOMY),
                        new Flight(origin: "ASR", destination: "IST", dayToFlight: 31, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination(adult: 3, child:1, infant: 1)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        GetPnr getPnr = new GetPnr()
        testCaseContext.addServiceRequest(new StandardSeat(1, 1, SeatPosition.WINDOW))
        testCaseContext.addServiceRequest(new StandardSeat(2, 1))
        testCaseContext.addServiceRequest(new StandardSeat(3, 1))
        testCaseContext.addServiceRequest(new StandardSeat(1, 2, SeatPosition.WINDOW))
        testCaseContext.addServiceRequest(new StandardSeat(2, 2))
        testCaseContext.addServiceRequest(new StandardSeat(3, 2))
        testCaseContext.addServiceRequest(new PetCarriage(1, 1, PetType.CAT_CABIN))
        testCaseContext.addServiceRequest(new PetCarriage(1, 2, PetType.CAT_CABIN))
        String pnrNumber = getPnr.getTicketPnr(testCaseContext)
        assert pnrNumber
    }

}
