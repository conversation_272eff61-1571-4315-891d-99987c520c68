package com.thy.qa.td4p.merchandising

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.merchandising.request.SportsEquipment
import com.thy.qa.td4p.merchandising.speq.SportsEquipmentType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class SPEQ_Test {
    @Test
    void should_add_speq_test() {
        GetPnr getPnr = new GetPnr()
        List flights = [new Flight(origin: "IST", destination: "AYT", dayToFlight: 35, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination(adult: 1, child: 1, infant: 1)
        passengerCombination.getPax().get(0).addExtraSeat()
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)
        testCaseContext.addServiceRequest(new SportsEquipment(1, 1, 1, SportsEquipmentType.BIKE))
        String pnrNumber = getPnr.getTicketPnr(testCaseContext)
        assert pnrNumber
    }
}
