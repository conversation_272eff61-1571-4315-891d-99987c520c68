package com.thy.qa.td4p.merchandising

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.merchandising.offer.service.Unit
import com.thy.qa.td4p.merchandising.request.ExtraBaggage
import com.thy.qa.td4p.merchandising.request.SportsEquipment
import com.thy.qa.td4p.merchandising.speq.SportsEquipmentType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class XBAG_Test {
    @Test
    void add_xbag_for_a_flight() {
        List flights = [new Flight(origin: "ADB", destination: "COV", dayToFlight: 28, fareType: FareType.ECONOMY, stopover: 1),
                        new Flight(origin: "COV", destination: "ADB", dayToFlight: 34, fareType: FareType.ECONOMY, stopover: 1)]
        PassengerCombination passengerCombination = new PassengerCombination(adult: 1, infant: 1)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        GetPnr getPnr = new GetPnr()
        testCaseContext.addServiceRequest(new ExtraBaggage(1, 2, 38, Unit.KILOGRAM))
        testCaseContext.addServiceRequest(new SportsEquipment(1, 1, 1, SportsEquipmentType.BIKE))
        String pnrNumber = getPnr.getTicketPnr(testCaseContext)
        assert pnrNumber
    }

}
