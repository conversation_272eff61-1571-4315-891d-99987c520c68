package com.thy.qa.td4p.merchandising

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.merchandising.request.seat.BabyBassinetSeat
import com.thy.qa.td4p.merchandising.request.seat.LegroomSeat
import com.thy.qa.td4p.merchandising.request.seat.StandardSeat
import com.thy.qa.td4p.passenger.PassengerCombination
import groovy.transform.CompileStatic
import groovy.xml.StreamingMarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.XmlUtil
import groovy.xml.slurpersupport.GPathResult
import org.junit.Ignore
import org.junit.Test

class SEAT_Test {
    @Test
    void should_select_baby_bassinet_seat() {
        GetPnr getPnr = new GetPnr()
        List flights = [new Flight(origin: "CDG", destination: "IST", dayToFlight: 15, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination(adult: 1, child: 1, infant: 1)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)
        testCaseContext.setAirlineCodes([AirlineCode.TK])
        testCaseContext.addServiceRequest(new BabyBassinetSeat(1, 1))
        String pnrNumber = getPnr.getTicketPnr(testCaseContext)
        assert pnrNumber
    }

    @Test
    void should_select_legroom_seat() {
        GetPnr getPnr = new GetPnr()
        List flights = [new Flight(origin: "IST", destination: "MUC", dayToFlight: 14, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination(adult: 2, child: 1, infant: 1)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)
        testCaseContext.setAirlineCodes([AirlineCode.TK])
        testCaseContext.addServiceRequest(new LegroomSeat(1, 1))
        testCaseContext.addServiceRequest(new LegroomSeat(2, 1))
        String pnrNumber = getPnr.getTicketPnr(testCaseContext)
        assert pnrNumber
    }

    @Test
    void should_select_seat_only_for_second_segment() {
        GetPnr getPnr = new GetPnr()
        List flights = [new Flight(origin: "IST", destination: "AYT", dayToFlight: 14, fareType: FareType.ECONOMY),
                        new Flight(origin: "AYT", destination: "IST", dayToFlight: 15, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination(adult: 2, child: 1, infant: 1)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        testCaseContext.addServiceRequest(new StandardSeat(2, 2))
        String pnrNumber = getPnr.getTicketPnr(testCaseContext)
        assert pnrNumber
    }

    @Test
    @Ignore
    void append_child_xml_on_the_fly() {
        GPathResult xml = new XmlSlurper(false, true).parseText('<root/>')
        xml << { node -> 'child' { xmls(it) } }
        println(XmlUtil.serialize(xml))
    }

    @Test
    @Ignore
    void create_xml_from_scratch() {
        println(XmlUtil.serialize(createXml()))
    }

    @CompileStatic
    Closure createXml() {
        StreamingMarkupBuilder builder = new StreamingMarkupBuilder()
        Closure a = (Closure) builder.bind { root ->
            root.invokeMethod('requestHeader', { child ->
                child.invokeMethod('EchoToken', 'Web')
                child.invokeMethod('currency', new Object[]{['b': 'c'], 1})
            })
        }
        return a
    }

    @CompileStatic
    def xmls(def builder) {
        builder.invokeMethod('requestHeader', { child ->
            child.invokeMethod('EchoToken', 'Web')
            child.invokeMethod('currency', new Object[]{['b': 'c'], 1})
        })
        return builder
    }
}
