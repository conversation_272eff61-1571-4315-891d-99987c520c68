package com.thy.qa.td4p.merchandising

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.merchandising.offer.service.Unit
import com.thy.qa.td4p.merchandising.request.ExtraBaggage
import com.thy.qa.td4p.merchandising.request.Lounge
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class Lounge_Test {
    @Test
    void lounge_test() {
        GetPnr getPnr = new GetPnr()
        List flights = [new Flight(origin: "IST", destination: "AYT", dayToFlight: 35, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination(adult: 1, child: 1, infant: 1)
        passengerCombination.getPax().get(0).addExtraSeat()
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)
        testCaseContext.setAirlineCodes([AirlineCode.TK])
        testCaseContext.addServiceRequest(new ExtraBaggage(2, 1, 30, Unit.KILOGRAM))
        testCaseContext.addServiceRequest(new Lounge(1, 1))
        String pnrNumber = getPnr.getTicketPnr(testCaseContext)
        assert pnrNumber
    }
}
