package com.thy.qa.td4p.merchandising

import com.eviware.soapui.model.testsuite.TestRunner
import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.merchandising.pet.PetType
import com.thy.qa.td4p.merchandising.request.ExtraBaggage
import com.thy.qa.td4p.merchandising.request.PetCarriage
import com.thy.qa.td4p.merchandising.request.ServiceRequestsContext
import com.thy.qa.td4p.merchandising.request.SportsEquipment
import com.thy.qa.td4p.merchandising.speq.SportsEquipmentType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class AVIH_Standalone_Test {
    @Test
    void add_petc_for_a_flight() {
        List flights = [new Flight(origin: "ADB", destination: "COV", dayToFlight: 30, fareType: FareType.ECONOMY, stopover: 1),
                        new Flight(origin: "COV", destination: "ADB", dayToFlight: 36, fareType: FareType.ECONOMY, stopover: 1)]
        PassengerCombination passengerCombination = new PassengerCombination(adult: 1, infant: 1)
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, passengerCombination)
        GetPnr getPnr = new GetPnr()

        String pnrNumber = getPnr.getTicketPnr(testCaseContext)
        String surname = getPnr.getSurname()
        assert pnrNumber

        GetPnr ancillaryAddGetPnr = new GetPnr()
        PetCarriage petCarriage1 = new PetCarriage(1, 1, PetType.DOG_AIRCRAFT)
        PetCarriage petCarriage2 = new PetCarriage(1, 2, PetType.DOG_AIRCRAFT)
        List ancillaryServices = [petCarriage1, petCarriage2, (new SportsEquipment(1, 1, 1, SportsEquipmentType.BIKE))]

        ServiceRequestsContext ancillaryServicesContext = new ServiceRequestsContext(ancillaryServices)

        ancillaryAddGetPnr.purchaseAncillaryServices(pnrNumber, surname, ancillaryServicesContext)
        assert ancillaryAddGetPnr.runner.status == TestRunner.Status.FINISHED
    }
}
