package com.thy.qa.td4p.pares

import com.thy.qa.td4p.GetDocumentInfo
import org.junit.Test

class DocumentInformation_Test {

    @Test
    void test2c() {
        List paymentDetails = new GetDocumentInfo().getPaymentInformation('2354565931604', 'EMDA')
        assert paymentDetails
    }

    @Test
    void test2d() {
        List paymentDetails = new GetDocumentInfo().getPaymentInformation('2354565931605', 'EMDA')
        assert paymentDetails
    }

    @Test
    void test2e() {
        List paymentDetails = new GetDocumentInfo().getPaymentInformation('2354565915237', 'EMDA')
        assert paymentDetails
    }


    @Test
    void test1a() {
        List paymentDetails = new GetDocumentInfo().getPaymentInformation('2352440161200', 'TKTT')
        assert paymentDetails
    }

    @Test
    void test1b() {
        List paymentDetails = new GetDocumentInfo().getPaymentInformation('2352440161201', 'TKTT')
        assert paymentDetails
    }

    @Test
    void test1c() {
        List paymentDetails = new GetDocumentInfo().getPaymentInformation('2352440163700', 'TKTT')
        assert paymentDetails
    }

    @Test
    void test1d() {
        List paymentDetails = new GetDocumentInfo().getPaymentInformation('2352440166200', 'TKTT')
        assert paymentDetails
    }


    @Test
    void test3a() {
        List paymentDetails = new GetDocumentInfo().getPaymentInformation('2352437479278', 'RFND')
        assert paymentDetails
    }

    @Test
    void test3b() {
        List paymentDetails = new GetDocumentInfo().getPaymentInformation('2352436300967', 'RFND')
        assert paymentDetails
    }

    @Test
    void test3c() {
        List paymentDetails = new GetDocumentInfo().getPaymentInformation('2352436536027', 'RFND')
        assert paymentDetails
    }

    @Test
    void test3d() {
        List paymentDetails = new GetDocumentInfo().getPaymentInformation('2352437485996', 'RFND')
        assert paymentDetails
    }

}
