package com.thy.qa.td4p.checkinOpenPnr


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FlightHour
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class INT_OW_Connecting_1ADT_RandomFareType_WITHIN24HOURS_Test {

    @Test
    void test() {
        PassengerCombination passengerCombination = new PassengerCombination([
                new Passenger(PassengerCode.ADULT)
        ])
        List flights = [
                new Flight(origin: "ADB", destination: "BCN", stopover: 1)
        ]
        List flightHours = [FlightHour.WITHIN24HOURS]
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)
        String pnrNumber = new GetPnr().getTicketPnrForCheckin(testCaseContext, flightHours)
        assert pnrNumber
    }

}
