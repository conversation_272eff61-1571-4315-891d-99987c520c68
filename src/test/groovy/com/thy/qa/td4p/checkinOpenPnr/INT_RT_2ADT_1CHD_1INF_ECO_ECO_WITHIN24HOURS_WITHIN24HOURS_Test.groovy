package com.thy.qa.td4p.checkinOpenPnr


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.FlightHour
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class INT_RT_2ADT_1CHD_1INF_ECO_ECO_WITHIN24HOURS_WITHIN24HOURS_Test {

    @Test
    void test() {
        PassengerCombination paxNums = new PassengerCombination([
                new Passenger(PassengerCode.ADULT),
                new Passenger(PassengerCode.ADULT),
                new Passenger(PassengerCode.CHILD),
                new Passenger(PassengerCode.INFANT)
        ])
        List flights = [
                new Flight(origin: "IST", destination: "BER", fareType: FareType.ECOFLY),
                new Flight(origin: "BER", destination: "IST", fareType: FareType.ECOFLY)
        ]
        List flightHours = [
                FlightHour.WITHIN24HOURS,
                FlightHour.WITHIN24HOURS
        ]
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, paxNums)
        String pnrNumber = new GetPnr().getTicketPnrForCheckin(testCaseContext, flightHours)
        assert pnrNumber
    }

}
