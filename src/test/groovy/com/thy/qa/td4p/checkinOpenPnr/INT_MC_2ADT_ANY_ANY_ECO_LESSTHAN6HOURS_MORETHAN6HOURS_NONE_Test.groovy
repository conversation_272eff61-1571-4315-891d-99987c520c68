package com.thy.qa.td4p.checkinOpenPnr


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.FlightHour
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class INT_MC_2ADT_ANY_ANY_ECO_LESSTHAN6HOURS_MORETHAN6HOURS_NONE_Test {

    @Test
    void test() {
        PassengerCombination pax = new PassengerCombination([new Passenger(PassengerCode.ADULT), new Passenger(PassengerCode.ADULT)])
        List flights = [
                new Flight(origin: "IST", destination: "ADB", stopover: 0, dayToFlight: 1),
                new Flight(origin: "ADB", destination: "IST", stopover: 0, dayToFlight: 1),
                new Flight(origin: "IST", destination: "LGW", stopover: 0, dayToFlight: 5, fareType: FareType.ECONOMY)
        ]
        List flightHours = [FlightHour.LESSTHAN6HOURS, FlightHour.MORETHAN6HOURS, FlightHour.NONE]
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.MULTICITY, flights, pax)
        String pnrNumber = new GetPnr().getTicketPnrForCheckin(testCaseContext, flightHours)
        assert pnrNumber
    }

}
