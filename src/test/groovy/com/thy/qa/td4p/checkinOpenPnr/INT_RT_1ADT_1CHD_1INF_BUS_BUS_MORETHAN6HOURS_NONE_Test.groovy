package com.thy.qa.td4p.checkinOpenPnr


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.FlightHour
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class INT_RT_1ADT_1CHD_1INF_BUS_BUS_MORETHAN6HOURS_NONE_Test {

    @Test
    void test() {
        PassengerCombination paxNums = new PassengerCombination([
                new Passenger(PassengerCode.ADULT),
                new Passenger(PassengerCode.CHILD),
                new Passenger(PassengerCode.INFANT)
        ])
        List flights = [
                new Flight(origin: "IST", destination: "FCO", fareType: FareType.BUSINESS),
                new Flight(origin: "FCO", destination: "IST", dayToFlight: 10, fareType: FareType.BUSINESS)
        ]
        List flightHours = [FlightHour.MORETHAN6HOURS, FlightHour.NONE]
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, paxNums)
        String pnrNumber = new GetPnr().getTicketPnrForCheckin(testCaseContext, flightHours)
        assert pnrNumber
    }

}
