package com.thy.qa.td4p.checkinOpenPnr

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.FlightHour
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class DOM_OW_Connecting_2ADT_ANY_LESSTHAN6HOURS_Test {

    @Test
    void test() {
        PassengerCombination paxNums = new PassengerCombination([new Passenger(PassengerCode.ADULT),
                                                                 new Passenger(PassengerCode.ADULT)])
        List flights = [new Flight(origin: "IST", destination: "ESB", stopover: 0, dayToFlight: 0, fareType: FareType.ANY)]
        List flightHours = [FlightHour.LESSTHAN6HOURS]
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, paxNums)
        String pnrNumber = new GetPnr().getTicketPnrForCheckin(testCaseContext, flightHours)
        assert pnrNumber
    }

}
