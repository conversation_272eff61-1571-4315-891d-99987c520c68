package com.thy.qa.td4p.checkinOpenPnr


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.FlightHour
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class VPO_INT_RT_1ADT_ANY_ANY_MORETHAN6HOURS_NONE_Test {

    @Test
    void test() {
        String firstPassengerMsNo = "TK002934055"
        PassengerCombination paxNums = new PassengerCombination([
                new Passenger(PassengerCode.ADULT)
        ])
        List flights = [
                new Flight(origin: "IST", destination: "AMS", stopover: 0, dayToFlight: 0, fareType: FareType.ANY),
                new Flight(origin: "AMS", destination: "IST", stopover: 0, dayToFlight: 10, fareType: FareType.ANY)
        ]
        List flightHours = [FlightHour.MORETHAN6HOURS, FlightHour.NONE]
        String pnrNumber = new GetPnr().getVPOAwardTicketPnrForCheckin(firstPassengerMsNo, new TestCaseContext(RoutingType.ROUNDTRIP, flights, paxNums), flightHours)
        assert pnrNumber
    }

}
