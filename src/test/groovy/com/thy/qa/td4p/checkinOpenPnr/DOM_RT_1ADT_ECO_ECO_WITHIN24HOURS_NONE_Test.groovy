package com.thy.qa.td4p.checkinOpenPnr

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.FlightHour
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class DOM_RT_1ADT_ECO_ECO_WITHIN24HOURS_NONE_Test {

    @Test
    void test() {
        PassengerCombination pax = new PassengerCombination([new Passenger(PassengerCode.ADULT)])
        List flights = [new Flight(origin: "IST", destination: "BCN", stopover: 0, dayToFlight: 0, fareType: FareType.BUSINESS),
                        new Flight(origin: "BCN", destination: "IST", stopover: 0, dayToFlight: 2, fareType: FareType.BUSINESS)]
        List flightHours = [FlightHour.WITHIN24HOURS, FlightHour.NONE]
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ROUNDTRIP, flights, pax)
        String pnrNumber = new GetPnr().getTicketPnrForCheckin(testCaseContext, flightHours)
        assert pnrNumber
    }

}
