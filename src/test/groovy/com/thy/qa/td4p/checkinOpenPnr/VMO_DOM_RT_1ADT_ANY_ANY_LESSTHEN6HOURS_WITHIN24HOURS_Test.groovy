package com.thy.qa.td4p.checkinOpenPnr


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.FlightHour
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

class VMO_DOM_RT_1ADT_ANY_ANY_LESSTHEN6HOURS_WITHIN24HOURS_Test {

    @Test
    void test() {
        String firstPassengerMsNo = "TK002934055"
        PassengerCombination paxNums = new PassengerCombination([new Passenger(PassengerCode.ADULT)])
        List flights = [new Flight(origin: "IST", destination: "ESB", stopover: 0, dayToFlight: 0, fareType: FareType.ANY),
                        new Flight(origin: "ESB", destination: "IST", stopover: 0, dayToFlight: 0, fareType: FareType.ANY)]
        List flightHours = [FlightHour.LESSTHAN6HOURS, FlightHour.WITHIN24HOURS]
        String pnrNumber = new GetPnr().getAwardTicketPnrForCheckin(firstPassengerMsNo, new TestCaseContext(RoutingType.ROUNDTRIP, flights, paxNums), flightHours)
        assert pnrNumber
    }

}
