package com.thy.qa.td4p.teststep

import com.eviware.soapui.model.testsuite.TestCaseRunContext
import com.eviware.soapui.model.testsuite.TestCaseRunner
import com.eviware.soapui.model.testsuite.TestStep
import com.eviware.soapui.model.testsuite.TestStepResult
import org.apache.logging.log4j.core.Logger
import org.junit.Before
import org.junit.Test
import org.mockito.Mockito

class ExecuteTroyaEntriesTestStepTest {

    ExecuteTroyaEntriesTestStep executeTroyaEntriesTestStep
    TestCaseRunner testCaseRunner
    TestCaseRunContext testCaseRunContext
    TestStep testStep
    TestStepResult testStepResult
    TestStepResult.TestStepStatus testStepStatus
    Logger log = Mockito.mock(Logger.class)

    @Before
    void setUp() {
        executeTroyaEntriesTestStep = Mockito.spy(new ExecuteTroyaEntriesTestStep(log: log))
        testCaseRunner = Mockito.mock(TestCaseRunner.class)
        testCaseRunContext = Mockito.mock(TestCaseRunContext.class)
        testStep = Mockito.mock(TestStep.class)
        testStepResult = Mockito.mock(TestStepResult.class)
        testStepStatus = Mockito.mock(TestStepResult.TestStepStatus.class)
    }

    @Test
    void testOnRetryCalledForSimultaneousChanges() {
        // Arrange
        testCaseRunContext.metaClass.setProperty("retryCount", 5)
        Mockito.when(testCaseRunContext.getTestRunner()).thenReturn(testCaseRunner)
        Mockito.when(testStepResult.getStatus()).thenReturn(testStepStatus)
        Mockito.when(testStepResult.status.toString()).thenReturn("FAIL")
        Mockito.when(testStepResult.getTestStep()).thenReturn(testStep)
        Mockito.when(testStep.getPropertyValue("RawRequest")).thenReturn("<clientTransactionId>123456</clientTransactionId>")
        Mockito.when(testStep.getPropertyValue("Response")).thenReturn("<S:Envelope xmlns:env=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:S=\"http://schemas.xmlsoap.org/soap/envelope/\"><env:Header/><S:Body><ns0:executeTroyaEntriesResponse xmlns:ns0=\"http://service.thy.com/\" xmlns:ns2=\"http://www.opentravel.org/OTA/2003/05\" xmlns:ns1=\"http://www.thy.com/ws/requestHeader\" xmlns:ns3=\"http://www.thy.com/ws/responseHeader\" xmlns:ns4=\"http://xml.amadeus.com/TIPNRR_23_1_1A\" xmlns:ns5=\"http://xml.amadeus.com/TMRXRR_23_1_1A\"><ns0:executeTroyaEntriesOTAResponse><troyaEntryResponseList><troyaEntry>BMITT</troyaEntry><responseText>ALL PROCESSED\n" +
                "</responseText></troyaEntryResponseList><troyaEntryResponseList><troyaEntry>*TKWHR2</troyaEntry><responseText> *** THIS IS AN INTERNET BOOKING  SEE CIC*17 ***\n" +
                "F 1.1BALCILAR/OUASQHDJMRS 2.1BALCILAR/CVHFWXNMS\n" +
                "3.1BALCILAR/XVLYEMISS\n" +
                "ITTTKIT 29AUG TKWHR2\n" +
                " 1 TK1959 Q  SA 28SEP  ISTAMS HK3   1345 1620 CABIN YE\n" +
                " 2 TK1956 V  TH 03OCT  AMSIST HK3   2310 0335*CABIN YE\n" +
                "FONE-ITT\n" +
                "TKT-Q29AUGITT000IT 1400 DMMY0-0\n" +
                "2.3 G CHD10\n" +
                "AP FAX-3 SSRCHLDYYHK1 /02JUL19\n" +
                "2.SSRCTCMYYHK3 905956567084/TR\n" +
                "3.SSRPCTCYYHK/ ORCUN BALCILAR/TR905956567084\n" +
                "4.SSRCTCEYYHK3 <EMAIL>/TR\n" +
                "5.1 SSRFOIDTKHK1 /NI39274984174\n" +
                "6.2 SSRFOIDTKHK1 /NI39274984174\n" +
                "7.3 SSRFOIDTKHK1 /NI39274984174\n" +
                "8.1 SSRDOCSYYHK1  /P////20OCT90/F//BALCILAR/OUASQHDJ\n" +
                "9.2 SSRDOCSYYHK1  /P////20OCT90/F//BALCILAR/CVHFWXN\n" +
                "10.3 SSRDOCSYYHK1  /P////02JUL19/F//BALCILAR/XVLYE\n" +
                "11.S1 SSRBFCLTKHK3 ECOFLY\n" +
                "12.S2 SSRBFCLTKHK3 ECOFLY\n" +
                "13.1 S1 SSRRQSTTKHK1 24A/CW \n" +
                "14.1 S1 SSRASVCTKHD1 A/0B5/RQST/CHARGEABLE WINDOW STANDART/A\n" +
                "15.1 S2 SSRRQSTTKHK1 14K/CW \n" +
                "16.1 S2 SSRASVCTKHD1 A/0B5/RQST/CHARGEABLE WINDOW STANDART/A\n" +
                "17.2 S1 SSRRQSTTKHK1 20A/CW \n" +
                "18.2 S1 SSRASVCTKHD1 A/0B5/RQST/CHARGEABLE WINDOW STANDART/A\n" +
                "19.2 S2 SSRRQSTTKHK1 31A/CW \n" +
                "20.2 S2 SSRASVCTKHD1 A/0B5/RQST/CHARGEABLE WINDOW STANDART/A\n" +
                "21.3 S1 SSRRQSTTKHK1 22A/CW \n" +
                "22.3 S1 SSRASVCTKHD1 A/0B5/RQST/CHARGEABLE WINDOW STANDART/A\n" +
                "23.3 S2 SSRRQSTTKHK1 36K/CW \n" +
                "24.3 S2 SSRASVCTKHD1 A/0B5/RQST/CHARGEABLE WINDOW STANDART/A\n" +
                "GEN FAX-OSITK CHAN R WEB 3.0\n" +
                "2.SSROTHSYY TERMS AND CONDITIONS ON WWW.TURKISHAIRLINES.COM/EN-TR/SEA\n" +
                "TASSIGNMENT\n" +
                "RMKS- .. LDTP IST(30-08-2024 23:59) ITTITSU29AUG\n" +
                "</responseText></troyaEntryResponseList><troyaEntryResponseList><troyaEntry>XI</troyaEntry><responseText>ITIN CNLD\n" +
                "\n" +
                "WARNING - SSR ITEMS FOR CANCELLED SEGMENT REMOVED TO HISTORY]\n" +
                "</responseText></troyaEntryResponseList><troyaEntryResponseList><troyaEntry>6P</troyaEntry><responseText>6P *\n" +
                "</responseText></troyaEntryResponseList><troyaEntryResponseList><troyaEntry>E</troyaEntry><responseText>SIMULTANEOUS CHANGES\n" +
                "</responseText></troyaEntryResponseList></ns0:executeTroyaEntriesOTAResponse><responseHeader><ns3:statusCode>SUCCESS</ns3:statusCode><ns3:clientTransactionId>CTID-TD4P-15-290824-134535776</ns3:clientTransactionId><ns3:serverTransactionId>e1ba5c33-fedc-443c-d521-c4120236a2dc</ns3:serverTransactionId></responseHeader></ns0:executeTroyaEntriesResponse></S:Body></S:Envelope>")

        // Act
        executeTroyaEntriesTestStep.afterStep(testCaseRunner, testCaseRunContext, testStepResult)

        // Assert onRetry method is called
        Mockito.verify(executeTroyaEntriesTestStep).onRetry(testCaseRunContext)
    }
}