package com.thy.qa.td4p.paidreservation

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.Test

import java.time.Duration

class INT_MC_2ADT_PRIMEFLY_PRIMEFLY_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "AMS", destination: "IST", dayToFlight: 48, fareType: FareType.PRIMEFLY),
                        new Flight(origin: "IST", destination: "COV", dayToFlight: 53, fareType: FareType.PRIMEFLY)]
        PassengerCombination passengerCombination = new PassengerCombination([new Passenger(PassengerCode.ADULT), new Passenger(PassengerCode.ADULT)])
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.MULTICITY, flights, passengerCombination)
        GetPnr getPnr = new GetPnr()
        String pnrNumber = getPnr.getPaidReservationPnr(testCaseContext)
        Duration duration = getPnr.getPnr().getTimeLimit()
        println(duration.toHours())
        assert pnrNumber
    }

}