package com.thy.qa.td4p.paidreservation


import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import com.thy.qa.td4p.payments.EFTPayment
import org.junit.Test

class INT_OW_1YTH_ECO_Test {

    @Test
    void test() {
        List flights = [new Flight(origin: "LHR", destination: "IST", dayToFlight: 40, fareType: FareType.ECONOMY)]
        PassengerCombination passengerCombination = new PassengerCombination([new Passenger(PassengerCode.ADULT)/*, new Passenger(PassengerCode.ADULT)*/])
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.ONEWAY, flights, passengerCombination)
        String pnrNumber = new GetPnr().getPaidReservationPnr(testCaseContext)
        assert pnrNumber
    }

}
