package com.thy.qa.td4p.document

import com.thy.qa.td4p.Flight
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.enums.FlightSegmentStatus
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCombination
import org.junit.BeforeClass
import org.junit.Test

class ElectronicDocumentTest {
    private static List tickets

    @BeforeClass
    static void before() {
        tickets = getTicket()
    }

    @Test
    void flown() {
        assert Ticket.ofNumber(tickets[0]).changeSegmentStatusTo(FlightSegmentStatus.FLOWN, 2)
    }

    @Test
    void addInvoluntaryIndicator() {
        assert Ticket.ofNumber(tickets[1]).addInvoluntaryIndicator(1)
    }

    @Test
    void addSIndicator() {
        assert Ticket.ofNumber(tickets[2]).addSIndicator(1)
    }

    private static List getTicket() {
        List flights = [new Flight(origin: "ERZ", destination: "IST", stopover: 0, dayToFlight: 20, fareType: FareType.ECONOMY),
                        new Flight(origin: "AYT", destination: "IST", stopover: 0, dayToFlight: 26, fareType: FareType.ANY)]
        TestCaseContext testCaseContext = new TestCaseContext(RoutingType.MULTICITY, flights, new PassengerCombination(3, 0, 0))
        GetPnr getPnr = new GetPnr()
        getPnr.getTicketPnr(testCaseContext)
        return getPnr.getPassengerInfo()*.get(0)
    }
}
