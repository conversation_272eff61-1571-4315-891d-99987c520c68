<S:Envelope xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"
  xmlns:S="http://schemas.xmlsoap.org/soap/envelope/">
  <env:Header/>
  <S:Body>
    <ns0:getMerchOfferResponse xmlns:ns0="http://service.thy.com/"
      xmlns:ns2="http://www.thy.com/merch/service/v1/service"
      xmlns:ns3="http://www.thy.com/merch/service/v1/commons/cp"
      xmlns:ns4="http://www.thy.com/merch/service/v1/offer"
      xmlns:ns5="http://www.thy.com/ws/responseHeader">
      <ns0:getMerchOfferOtaResponse>
        <offerResponse>
          <ns2:Header ResponseID="E3C6B596-D952-4D19-A80E-6A2E321A1191"
            RequestDT="2024-03-21T14:47:54.166+03:00" ResponseDT="2024-03-21T14:47:54.359+03:00"/>
          <ns2:Result Status="OK" Code="00" Reason="Successful"/>
          <ns2:PricedOffer OfferID="013IV42YSRHJA" ParentID="E3C6B596-D952-4D19-A80E-6A2E321A1191"
            Owner="TK" TS="2024-03-21T14:47:54.166+03:00">
            <ns4:OfferItem OfferItemID="013IV42YSRHJA01" ParentID="013IV42YSRHJA"
              MandatoryInd="true">
              <ns4:Service ServiceID="013IV42YSRHJA0101" ParentID="013IV42YSRHJA01">
                <ns4:ServiceKind Code="SEAT_EXIT_AISLE" Category="SEAT" Rfic="D" Rfisc="0B5"
                  Group="TS" SubGroup="BO"/>
                <ns4:PassengerRef>1</ns4:PassengerRef>
                <ns4:SegmentRefs>1</ns4:SegmentRefs>
                <ns4:PriceDetail>
                  <ns3:BaseAmount>
                    <ns3:Initial Amount="60.00" CurrencyCode="TRY"/>
                    <ns3:InitialEquivalent Amount="60.00" CurrencyCode="TRY"/>
                    <ns3:Original Amount="60.00" CurrencyCode="TRY"/>
                    <ns3:Equivalent Amount="60.00" CurrencyCode="TRY"/>
                  </ns3:BaseAmount>
                </ns4:PriceDetail>
                <ns4:Specification SpecificationKind="CustomerFacingService" Code="CFS">
                  <ns4:Parameter Name="SeatKind" ValueType="String" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>EXIT</ns4:Value>
                  </ns4:Parameter>
                  <ns4:Parameter Name="SeatPosition" ValueType="String" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>AISLE</ns4:Value>
                  </ns4:Parameter>
                </ns4:Specification>
              </ns4:Service>
              <ns4:TotalPriceDetail>
                <ns3:BaseAmount>
                  <ns3:InitialEquivalent Amount="60.00" CurrencyCode="TRY"/>
                  <ns3:Equivalent Amount="60.00" CurrencyCode="TRY"/>
                </ns3:BaseAmount>
                <ns3:TotalAmount CurrencyCode="TRY" Amount="60.00"/>
              </ns4:TotalPriceDetail>
              <ns4:OfferType>A_LA_CARTE</ns4:OfferType>
            </ns4:OfferItem>
            <ns4:OfferItem OfferItemID="013IV42YSRHJA02" ParentID="013IV42YSRHJA"
              MandatoryInd="true">
              <ns4:Service ServiceID="013IV42YSRHJA0201" ParentID="013IV42YSRHJA02">
                <ns4:ServiceKind Code="SEAT_EXIT_AISLE" Category="SEAT" Rfic="D" Rfisc="0B5"
                  Group="TS" SubGroup="BO"/>
                <ns4:PassengerRef>2</ns4:PassengerRef>
                <ns4:SegmentRefs>1</ns4:SegmentRefs>
                <ns4:PriceDetail>
                  <ns3:BaseAmount>
                    <ns3:Initial Amount="60.00" CurrencyCode="TRY"/>
                    <ns3:InitialEquivalent Amount="60.00" CurrencyCode="TRY"/>
                    <ns3:Original Amount="60.00" CurrencyCode="TRY"/>
                    <ns3:Equivalent Amount="60.00" CurrencyCode="TRY"/>
                  </ns3:BaseAmount>
                </ns4:PriceDetail>
                <ns4:Specification SpecificationKind="CustomerFacingService" Code="CFS">
                  <ns4:Parameter Name="SeatKind" ValueType="String" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>EXIT</ns4:Value>
                  </ns4:Parameter>
                  <ns4:Parameter Name="SeatPosition" ValueType="String" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>AISLE</ns4:Value>
                  </ns4:Parameter>
                </ns4:Specification>
              </ns4:Service>
              <ns4:TotalPriceDetail>
                <ns3:BaseAmount>
                  <ns3:InitialEquivalent Amount="60.00" CurrencyCode="TRY"/>
                  <ns3:Equivalent Amount="60.00" CurrencyCode="TRY"/>
                </ns3:BaseAmount>
                <ns3:TotalAmount CurrencyCode="TRY" Amount="60.00"/>
              </ns4:TotalPriceDetail>
              <ns4:OfferType>A_LA_CARTE</ns4:OfferType>
            </ns4:OfferItem>
            <ns4:OfferItem OfferItemID="013IV42YSRHJA03" ParentID="013IV42YSRHJA"
              MandatoryInd="true">
              <ns4:Service ServiceID="013IV42YSRHJA0301" ParentID="013IV42YSRHJA03">
                <ns4:ServiceKind Code="XBAG_WEIGHT_1KG_SCALABLE" Category="XBAG" Rfic="C"
                  Rfisc="0DG" Group="BG"/>
                <ns4:PassengerRef>1</ns4:PassengerRef>
                <ns4:SegmentRefs>1</ns4:SegmentRefs>
                <ns4:PriceDetail>
                  <ns3:BaseAmount>
                    <ns3:Initial Amount="420.00" CurrencyCode="TRY"/>
                    <ns3:InitialEquivalent Amount="420.00" CurrencyCode="TRY"/>
                    <ns3:Original Amount="420.00" CurrencyCode="TRY"/>
                    <ns3:Equivalent Amount="420.00" CurrencyCode="TRY"/>
                  </ns3:BaseAmount>
                </ns4:PriceDetail>
                <ns4:Specification SpecificationKind="CustomerFacingService" Code="CFS">
                  <ns4:Parameter Name="Unit" ValueType="String" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>Kilogram</ns4:Value>
                  </ns4:Parameter>
                  <ns4:Parameter Name="Amount" ValueType="Integer" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>12</ns4:Value>
                  </ns4:Parameter>
                </ns4:Specification>
              </ns4:Service>
              <ns4:TotalPriceDetail>
                <ns3:BaseAmount>
                  <ns3:InitialEquivalent Amount="420.00" CurrencyCode="TRY"/>
                  <ns3:Equivalent Amount="420.00" CurrencyCode="TRY"/>
                </ns3:BaseAmount>
                <ns3:TotalAmount CurrencyCode="TRY" Amount="420.00"/>
              </ns4:TotalPriceDetail>
              <ns4:OfferType>A_LA_CARTE</ns4:OfferType>
            </ns4:OfferItem>
            <ns4:OfferItem OfferItemID="013IV42YSRHJA04" ParentID="013IV42YSRHJA"
              MandatoryInd="true">
              <ns4:Service ServiceID="013IV42YSRHJA0401" ParentID="013IV42YSRHJA04">
                <ns4:ServiceKind Code="XBAG_WEIGHT_15KG" Category="XBAG" Rfic="C" Rfisc="0DG"
                  Group="BG"/>
                <ns4:PassengerRef>2</ns4:PassengerRef>
                <ns4:SegmentRefs>1</ns4:SegmentRefs>
                <ns4:PriceDetail>
                  <ns3:BaseAmount>
                    <ns3:Initial Amount="525.00" CurrencyCode="TRY"/>
                    <ns3:InitialEquivalent Amount="525.00" CurrencyCode="TRY"/>
                    <ns3:Original Amount="525.00" CurrencyCode="TRY"/>
                    <ns3:Equivalent Amount="525.00" CurrencyCode="TRY"/>
                  </ns3:BaseAmount>
                </ns4:PriceDetail>
                <ns4:Specification SpecificationKind="CustomerFacingService" Code="CFS">
                  <ns4:Parameter Name="Unit" ValueType="String" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>Kilogram</ns4:Value>
                  </ns4:Parameter>
                  <ns4:Parameter Name="Amount" ValueType="Integer" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>15</ns4:Value>
                  </ns4:Parameter>
                </ns4:Specification>
              </ns4:Service>
              <ns4:TotalPriceDetail>
                <ns3:BaseAmount>
                  <ns3:InitialEquivalent Amount="525.00" CurrencyCode="TRY"/>
                  <ns3:Equivalent Amount="525.00" CurrencyCode="TRY"/>
                </ns3:BaseAmount>
                <ns3:TotalAmount CurrencyCode="TRY" Amount="525.00"/>
              </ns4:TotalPriceDetail>
              <ns4:OfferType>A_LA_CARTE</ns4:OfferType>
            </ns4:OfferItem>
            <ns4:OfferItem OfferItemID="013IV42YSRHJA05" ParentID="013IV42YSRHJA"
              MandatoryInd="true">
              <ns4:Service ServiceID="013IV42YSRHJA0501" ParentID="013IV42YSRHJA05">
                <ns4:ServiceKind Code="LNG_IST_DOM_STD_1" Category="LNG" Rfic="E" Rfisc="05P"
                  Group="LG"/>
                <ns4:PassengerRef>1</ns4:PassengerRef>
                <ns4:SegmentRefs>1</ns4:SegmentRefs>
                <ns4:PriceDetail>
                  <ns3:BaseAmount>
                    <ns3:Initial Amount="720.00" CurrencyCode="TRY"/>
                    <ns3:InitialEquivalent Amount="720.00" CurrencyCode="TRY"/>
                    <ns3:Original Amount="720.00" CurrencyCode="TRY"/>
                    <ns3:Equivalent Amount="720.00" CurrencyCode="TRY"/>
                  </ns3:BaseAmount>
                </ns4:PriceDetail>
                <ns4:Specification SpecificationKind="CustomerFacingService" Code="CFS">
                  <ns4:Parameter Name="Route" ValueType="String" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>DOM</ns4:Value>
                  </ns4:Parameter>
                  <ns4:Parameter Name="LoungeKind" ValueType="String" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>Standard</ns4:Value>
                  </ns4:Parameter>
                  <ns4:Parameter Name="Port" ValueType="String" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>IST</ns4:Value>
                  </ns4:Parameter>
                </ns4:Specification>
              </ns4:Service>
              <ns4:TotalPriceDetail>
                <ns3:BaseAmount>
                  <ns3:InitialEquivalent Amount="720.00" CurrencyCode="TRY"/>
                  <ns3:Equivalent Amount="720.00" CurrencyCode="TRY"/>
                </ns3:BaseAmount>
                <ns3:TotalAmount CurrencyCode="TRY" Amount="720.00"/>
              </ns4:TotalPriceDetail>
              <ns4:OfferType>A_LA_CARTE</ns4:OfferType>
            </ns4:OfferItem>
            <ns4:OfferItem OfferItemID="013IV42YSRHJA06" ParentID="013IV42YSRHJA"
              MandatoryInd="true">
              <ns4:Service ServiceID="013IV42YSRHJA0601" ParentID="013IV42YSRHJA06">
                <ns4:ServiceKind Code="LNG_IST_DOM_STD_1" Category="LNG" Rfic="E" Rfisc="05P"
                  Group="LG"/>
                <ns4:PassengerRef>2</ns4:PassengerRef>
                <ns4:SegmentRefs>1</ns4:SegmentRefs>
                <ns4:PriceDetail>
                  <ns3:BaseAmount>
                    <ns3:Initial Amount="720.00" CurrencyCode="TRY"/>
                    <ns3:InitialEquivalent Amount="720.00" CurrencyCode="TRY"/>
                    <ns3:Original Amount="720.00" CurrencyCode="TRY"/>
                    <ns3:Equivalent Amount="720.00" CurrencyCode="TRY"/>
                  </ns3:BaseAmount>
                </ns4:PriceDetail>
                <ns4:Specification SpecificationKind="CustomerFacingService" Code="CFS">
                  <ns4:Parameter Name="Route" ValueType="String" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>DOM</ns4:Value>
                  </ns4:Parameter>
                  <ns4:Parameter Name="LoungeKind" ValueType="String" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>Standard</ns4:Value>
                  </ns4:Parameter>
                  <ns4:Parameter Name="Port" ValueType="String" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>IST</ns4:Value>
                  </ns4:Parameter>
                </ns4:Specification>
              </ns4:Service>
              <ns4:TotalPriceDetail>
                <ns3:BaseAmount>
                  <ns3:InitialEquivalent Amount="720.00" CurrencyCode="TRY"/>
                  <ns3:Equivalent Amount="720.00" CurrencyCode="TRY"/>
                </ns3:BaseAmount>
                <ns3:TotalAmount CurrencyCode="TRY" Amount="720.00"/>
              </ns4:TotalPriceDetail>
              <ns4:OfferType>A_LA_CARTE</ns4:OfferType>
            </ns4:OfferItem>
            <ns4:OfferItem OfferItemID="013IV42YSRHJA07" ParentID="013IV42YSRHJA"
              MandatoryInd="true">
              <ns4:Service ServiceID="013IV42YSRHJA0701" ParentID="013IV42YSRHJA07">
                <ns4:ServiceKind Code="SPEQ_CLIMBING_1PC" Category="SPEQ" Rfic="C" Rfisc="0O0"
                  Group="BG" SubGroup="SP"/>
                <ns4:PassengerRef>1</ns4:PassengerRef>
                <ns4:SegmentRefs>1</ns4:SegmentRefs>
                <ns4:PriceDetail>
                  <ns3:BaseAmount>
                    <ns3:Initial Amount="209.00" CurrencyCode="TRY"/>
                    <ns3:InitialEquivalent Amount="209.00" CurrencyCode="TRY"/>
                    <ns3:Original Amount="209.00" CurrencyCode="TRY"/>
                    <ns3:Equivalent Amount="209.00" CurrencyCode="TRY"/>
                  </ns3:BaseAmount>
                </ns4:PriceDetail>
                <ns4:Specification SpecificationKind="CustomerFacingService" Code="CFS">
                  <ns4:Parameter Name="Unit" ValueType="String" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>Piece</ns4:Value>
                  </ns4:Parameter>
                  <ns4:Parameter Name="Amount" ValueType="Integer" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>1</ns4:Value>
                  </ns4:Parameter>
                  <ns4:Parameter Name="SPEQName" ValueType="String" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>CLIMBING</ns4:Value>
                  </ns4:Parameter>
                </ns4:Specification>
              </ns4:Service>
              <ns4:TotalPriceDetail>
                <ns3:BaseAmount>
                  <ns3:InitialEquivalent Amount="209.00" CurrencyCode="TRY"/>
                  <ns3:Equivalent Amount="209.00" CurrencyCode="TRY"/>
                </ns3:BaseAmount>
                <ns3:TotalAmount CurrencyCode="TRY" Amount="209.00"/>
              </ns4:TotalPriceDetail>
              <ns4:OfferType>A_LA_CARTE</ns4:OfferType>
            </ns4:OfferItem>
            <ns4:OfferItem OfferItemID="013IV42YSRHJA08" ParentID="013IV42YSRHJA"
              MandatoryInd="true">
              <ns4:Service ServiceID="013IV42YSRHJA0801" ParentID="013IV42YSRHJA08">
                <ns4:ServiceKind Code="SPEQ_TENNIS_SQUASH_BADMINT_SCALABLE" Category="SPEQ" Rfic="C"
                  Rfisc="0OX" Group="BG" SubGroup="SP"/>
                <ns4:PassengerRef>2</ns4:PassengerRef>
                <ns4:SegmentRefs>1</ns4:SegmentRefs>
                <ns4:PriceDetail>
                  <ns3:BaseAmount>
                    <ns3:Initial Amount="418.00" CurrencyCode="TRY"/>
                    <ns3:InitialEquivalent Amount="418.00" CurrencyCode="TRY"/>
                    <ns3:Original Amount="418.00" CurrencyCode="TRY"/>
                    <ns3:Equivalent Amount="418.00" CurrencyCode="TRY"/>
                  </ns3:BaseAmount>
                </ns4:PriceDetail>
                <ns4:Specification SpecificationKind="CustomerFacingService" Code="CFS">
                  <ns4:Parameter Name="Unit" ValueType="String" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>Piece</ns4:Value>
                  </ns4:Parameter>
                  <ns4:Parameter Name="Amount" ValueType="Integer" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>2</ns4:Value>
                  </ns4:Parameter>
                  <ns4:Parameter Name="SPEQName" ValueType="String" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>TENNIS_SQUASH_BADMINT</ns4:Value>
                  </ns4:Parameter>
                </ns4:Specification>
              </ns4:Service>
              <ns4:TotalPriceDetail>
                <ns3:BaseAmount>
                  <ns3:InitialEquivalent Amount="418.00" CurrencyCode="TRY"/>
                  <ns3:Equivalent Amount="418.00" CurrencyCode="TRY"/>
                </ns3:BaseAmount>
                <ns3:TotalAmount CurrencyCode="TRY" Amount="418.00"/>
              </ns4:TotalPriceDetail>
              <ns4:OfferType>A_LA_CARTE</ns4:OfferType>
            </ns4:OfferItem>
            <ns4:OfferItem OfferItemID="013IV42YSRHJA09" ParentID="013IV42YSRHJA"
              MandatoryInd="true">
              <ns4:Service ServiceID="013IV42YSRHJA0901" ParentID="013IV42YSRHJA09">
                <ns4:ServiceKind Code="AVIH_0_15" Category="AVIH" Rfic="C" Rfisc="0BS" Group="PT"
                  SubGroup="PH"/>
                <ns4:PassengerRef>1</ns4:PassengerRef>
                <ns4:SegmentRefs>1</ns4:SegmentRefs>
                <ns4:PriceDetail>
                  <ns3:BaseAmount>
                    <ns3:Initial Amount="285.00" CurrencyCode="TRY"/>
                    <ns3:InitialEquivalent Amount="285.00" CurrencyCode="TRY"/>
                    <ns3:Original Amount="285.00" CurrencyCode="TRY"/>
                    <ns3:Equivalent Amount="285.00" CurrencyCode="TRY"/>
                  </ns3:BaseAmount>
                </ns4:PriceDetail>
                <ns4:Specification SpecificationKind="CustomerFacingService" Code="CFS">
                  <ns4:Parameter Name="Unit" ValueType="String" Type="Simple"
                    UsageType="Predefined">
                    <ns4:Value>Kilogram</ns4:Value>
                  </ns4:Parameter>
                  <ns4:Parameter Name="Amount" ValueType="Integer" Type="Range"
                    UsageType="Predefined">
                    <ns4:Value>0</ns4:Value>
                    <ns4:Value>15</ns4:Value>
                  </ns4:Parameter>
                  <ns4:Parameter Name="Family" ValueType="String" Type="Simple"
                    UsageType="Required">
                    <ns4:Value>CAT</ns4:Value>
                    <ns4:DefinedValues>CAT</ns4:DefinedValues>
                    <ns4:DefinedValues>DOG</ns4:DefinedValues>
                    <ns4:DefinedValues>BIRD</ns4:DefinedValues>
                  </ns4:Parameter>
                  <ns4:Parameter Name="Species" ValueType="String" Type="Simple"
                    UsageType="Optional">
                    <ns4:Value>TURKISH VAN</ns4:Value>
                  </ns4:Parameter>
                  <ns4:Parameter Name="Dimensions" ValueType="String" Type="Simple"
                    UsageType="Optional">
                    <ns4:Value>10x5x8</ns4:Value>
                  </ns4:Parameter>
                </ns4:Specification>
              </ns4:Service>
              <ns4:TotalPriceDetail>
                <ns3:BaseAmount>
                  <ns3:InitialEquivalent Amount="285.00" CurrencyCode="TRY"/>
                  <ns3:Equivalent Amount="285.00" CurrencyCode="TRY"/>
                </ns3:BaseAmount>
                <ns3:TotalAmount CurrencyCode="TRY" Amount="285.00"/>
              </ns4:TotalPriceDetail>
              <ns4:OfferType>A_LA_CARTE</ns4:OfferType>
            </ns4:OfferItem>
            <ns4:TimeLimit>
              <ns4:offerExpiration dateTime="2024-03-21T15:47:54.166+03:00"/>
            </ns4:TimeLimit>
          </ns2:PricedOffer>
        </offerResponse>
      </ns0:getMerchOfferOtaResponse>
      <responseHeader>
        <ns5:statusCode>SUCCESS</ns5:statusCode>
        <ns5:clientTransactionId>5fdfe126-f28f-4592-b6c9-088c4176884b</ns5:clientTransactionId>
        <ns5:serverTransactionId>ae1855f6-6e62-4865-9001-b6fa312db7e3</ns5:serverTransactionId>
      </responseHeader>
    </ns0:getMerchOfferResponse>
  </S:Body>
</S:Envelope>