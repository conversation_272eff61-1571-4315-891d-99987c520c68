<S:Envelope xmlns:env="http://schemas.xmlsoap.org/soap/envelope/" xmlns:S="http://schemas.xmlsoap.org/soap/envelope/">
    <env:Header/>
    <S:Body>
        <ns0:getAvailabilityResponse xmlns:ns0="http://service.thy.com/"
                                     xmlns:ns2="http://www.opentravel.org/OTA/2003/05"
                                     xmlns:ns3="http://www.thy.com/ws/responseHeader"
                                     xmlns:ns1="http://www.thy.com/ws/requestHeader">
            <ns0:createAvailabilityOTAResponse>
                <availabilityOTAResponse>
                    <createOTAAirRoute>
                        <ns2:OTA_AirAvailRS Version="0">
                            <ns2:OriginDestinationInformation RPH="IST_ADA_30MAY2022">
                                <ns2:DepartureDateTime>2022-05-30T00:00:00.000+03:00</ns2:DepartureDateTime>
                                <ns2:ArrivalDateTime>2022-05-30T00:00:00.000+03:00</ns2:ArrivalDateTime>
                                <ns2:OriginLocation LocationCode="IST" AlternateLocationInd="false"/>
                                <ns2:DestinationLocation LocationCode="ADA" AlternateLocationInd="false"/>
                                <ns2:OriginDestinationOptions>
                                    <ns2:OriginDestinationOption>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T13:55:00.000+03:00"
                                                           ArrivalDateTime="2022-05-30T15:25:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK2462"
                                                           JourneyDuration="P0DT1H30M0.000S" Ticket="eTicket"
                                                           CodeshareInd="false" DateChangeNbr="FALSE">
                                            <ns2:DepartureAirport LocationCode="IST"/>
                                            <ns2:ArrivalAirport LocationCode="ADA"/>
                                            <ns2:OperatingAirline CompanyShortName="TK"/>
                                            <ns2:Equipment Value="UNKNOWN_PLANE" AirEquipType="33D"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:BUSINESS"
                                                                   ResBookDesigCode="J" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="JBU"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="O" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="OEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:BUSINESS"
                                                                   ResBookDesigCode="J" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="JBU"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="O" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="OEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="O" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="OEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="O" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="OXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="O" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="OXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="O" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="OXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="O" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="OPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="O" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="OPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="O" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="OPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:BUSINESS"
                                                                   ResBookDesigCode="J" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="JBU"/>
                                        </ns2:FlightSegment>
                                    </ns2:OriginDestinationOption>
                                    <ns2:OriginDestinationOption>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T16:10:00.000+03:00"
                                                           ArrivalDateTime="2022-05-30T17:35:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK7280"
                                                           JourneyDuration="P0DT1H25M0.000S" Ticket="eTicket"
                                                           CodeshareInd="false" DateChangeNbr="FALSE">
                                            <ns2:DepartureAirport LocationCode="SAW"/>
                                            <ns2:ArrivalAirport LocationCode="ADA"/>
                                            <ns2:OperatingAirline CompanyShortName="AJ"/>
                                            <ns2:Equipment Value="UNKNOWN_PLANE" AirEquipType="73V"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BPF"/>
                                        </ns2:FlightSegment>
                                    </ns2:OriginDestinationOption>
                                    <ns2:OriginDestinationOption>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T16:50:00.000+03:00"
                                                           ArrivalDateTime="2022-05-30T18:20:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK2466"
                                                           JourneyDuration="P0DT1H30M0.000S" Ticket="eTicket"
                                                           CodeshareInd="false" DateChangeNbr="FALSE">
                                            <ns2:DepartureAirport LocationCode="IST"/>
                                            <ns2:ArrivalAirport LocationCode="ADA"/>
                                            <ns2:OperatingAirline CompanyShortName="TK"/>
                                            <ns2:Equipment Value="BOEING B737-800" AirEquipType="78D"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:BUSINESS"
                                                                   ResBookDesigCode="J" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="JBU"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:BUSINESS"
                                                                   ResBookDesigCode="J" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="JBU"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:BUSINESS"
                                                                   ResBookDesigCode="J" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="JBU"/>
                                        </ns2:FlightSegment>
                                    </ns2:OriginDestinationOption>
                                    <ns2:OriginDestinationOption>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T18:25:00.000+03:00"
                                                           ArrivalDateTime="2022-05-30T19:50:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK2468"
                                                           JourneyDuration="P0DT1H25M0.000S" Ticket="eTicket"
                                                           CodeshareInd="false" DateChangeNbr="FALSE">
                                            <ns2:DepartureAirport LocationCode="IST"/>
                                            <ns2:ArrivalAirport LocationCode="ADA"/>
                                            <ns2:OperatingAirline CompanyShortName="TK"/>
                                            <ns2:Equipment Value="BOEING B737-900ER" AirEquipType="79B"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:BUSINESS"
                                                                   ResBookDesigCode="J" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="JBU"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="M" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="MEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:BUSINESS"
                                                                   ResBookDesigCode="J" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="JBU"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="M" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="MEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="M" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="MEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="M" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="MXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="M" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="MXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="M" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="MXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="M" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="MPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="M" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="MPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="M" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="MPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:BUSINESS"
                                                                   ResBookDesigCode="J" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="JBU"/>
                                        </ns2:FlightSegment>
                                    </ns2:OriginDestinationOption>
                                    <ns2:OriginDestinationOption>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T20:45:00.000+03:00"
                                                           ArrivalDateTime="2022-05-30T22:10:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK2470"
                                                           JourneyDuration="P0DT1H25M0.000S" Ticket="eTicket"
                                                           CodeshareInd="false" DateChangeNbr="FALSE">
                                            <ns2:DepartureAirport LocationCode="IST"/>
                                            <ns2:ArrivalAirport LocationCode="ADA"/>
                                            <ns2:OperatingAirline CompanyShortName="TK"/>
                                            <ns2:Equipment Value="AIRBUS A321-231" AirEquipType="321"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:BUSINESS"
                                                                   ResBookDesigCode="J" ResBookDesigQuantity="8"
                                                                   ResBookDesigStatusCode="JBU"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="M" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="MEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:BUSINESS"
                                                                   ResBookDesigCode="J" ResBookDesigQuantity="8"
                                                                   ResBookDesigStatusCode="JBU"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="M" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="MEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="M" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="MEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="M" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="MXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="M" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="MXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="M" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="MXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="M" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="MPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="M" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="MPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="M" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="MPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:BUSINESS"
                                                                   ResBookDesigCode="J" ResBookDesigQuantity="8"
                                                                   ResBookDesigStatusCode="JBU"/>
                                        </ns2:FlightSegment>
                                    </ns2:OriginDestinationOption>
                                    <ns2:OriginDestinationOption>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T23:00:00.000+03:00"
                                                           ArrivalDateTime="2022-05-31T00:05:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK7248"
                                                           JourneyDuration="P0DT1H5M0.000S" Ticket="eTicket"
                                                           GroundDuration="P0DT6H15M0.000S" CodeshareInd="false"
                                                           DateChangeNbr="TRUE">
                                            <ns2:DepartureAirport LocationCode="SAW"/>
                                            <ns2:ArrivalAirport LocationCode="ESB"/>
                                            <ns2:OperatingAirline CompanyShortName="AJ"/>
                                            <ns2:Equipment Value="UNKNOWN_PLANE" AirEquipType="73D"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="E" ResBookDesigQuantity="5"
                                                                   ResBookDesigStatusCode="BEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="E" ResBookDesigQuantity="5"
                                                                   ResBookDesigStatusCode="BEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="E" ResBookDesigQuantity="5"
                                                                   ResBookDesigStatusCode="BEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="E" ResBookDesigQuantity="5"
                                                                   ResBookDesigStatusCode="BXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="E" ResBookDesigQuantity="5"
                                                                   ResBookDesigStatusCode="BXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="E" ResBookDesigQuantity="5"
                                                                   ResBookDesigStatusCode="BXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="E" ResBookDesigQuantity="5"
                                                                   ResBookDesigStatusCode="BPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="E" ResBookDesigQuantity="5"
                                                                   ResBookDesigStatusCode="BPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="E" ResBookDesigQuantity="5"
                                                                   ResBookDesigStatusCode="BPF"/>
                                        </ns2:FlightSegment>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-31T06:20:00.000+03:00"
                                                           ArrivalDateTime="2022-05-31T07:20:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK7100"
                                                           JourneyDuration="P0DT1H0M0.000S" Ticket="eTicket"
                                                           CodeshareInd="false" DateChangeNbr="FALSE">
                                            <ns2:DepartureAirport LocationCode="ESB"/>
                                            <ns2:ArrivalAirport LocationCode="ADA"/>
                                            <ns2:OperatingAirline CompanyShortName="AJ"/>
                                            <ns2:Equipment Value="UNKNOWN_PLANE" AirEquipType="7D3"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:2&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:2&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:2&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:2&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:2&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:2&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:2&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:2&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:2&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="B" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="BPF"/>
                                        </ns2:FlightSegment>
                                    </ns2:OriginDestinationOption>
                                    <ns2:OriginDestinationOption>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T23:10:00.000+03:00"
                                                           ArrivalDateTime="2022-05-31T00:35:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK2474"
                                                           JourneyDuration="P0DT1H25M0.000S" Ticket="eTicket"
                                                           CodeshareInd="false" DateChangeNbr="TRUE">
                                            <ns2:DepartureAirport LocationCode="IST"/>
                                            <ns2:ArrivalAirport LocationCode="ADA"/>
                                            <ns2:OperatingAirline CompanyShortName="TK"/>
                                            <ns2:Equipment Value="BOEING B737-800" AirEquipType="78D"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:BUSINESS"
                                                                   ResBookDesigCode="J" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="JBU"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="O" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="OEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:BUSINESS"
                                                                   ResBookDesigCode="J" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="JBU"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="O" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="OEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="O" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="OEF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="O" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="OXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="O" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="OXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="O" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="OXF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="O" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="OPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:CNN&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="O" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="OPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:INF&#x9; SegmentIndex:1&#x9; Cabin:ECONOMY"
                                                                   ResBookDesigCode="O" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="OPF"/>
                                            <ns2:BookingClassAvail RPH="Pax:ADT&#x9; SegmentIndex:1&#x9; Cabin:BUSINESS"
                                                                   ResBookDesigCode="J" ResBookDesigQuantity="9"
                                                                   ResBookDesigStatusCode="JBU"/>
                                        </ns2:FlightSegment>
                                    </ns2:OriginDestinationOption>
                                    <ns2:OriginDestinationOption>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T01:15:00.000+03:00"
                                                           ArrivalDateTime="2022-05-30T02:40:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK2476"
                                                           JourneyDuration="P0DT1H25M0.000S" Ticket="eTicket"
                                                           CodeshareInd="false" DateChangeNbr="FALSE">
                                            <ns2:DepartureAirport LocationCode="IST"/>
                                            <ns2:ArrivalAirport LocationCode="ADA"/>
                                            <ns2:OperatingAirline CompanyShortName="TK"/>
                                            <ns2:Equipment Value="BOEING B737-800" AirEquipType="78D"/>
                                        </ns2:FlightSegment>
                                    </ns2:OriginDestinationOption>
                                    <ns2:OriginDestinationOption>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T06:00:00.000+03:00"
                                                           ArrivalDateTime="2022-05-30T07:20:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK2454"
                                                           JourneyDuration="P0DT1H20M0.000S" Ticket="eTicket"
                                                           CodeshareInd="false" DateChangeNbr="FALSE">
                                            <ns2:DepartureAirport LocationCode="IST"/>
                                            <ns2:ArrivalAirport LocationCode="ADA"/>
                                            <ns2:OperatingAirline CompanyShortName="TK"/>
                                            <ns2:Equipment Value="BOEING B737-800" AirEquipType="78D"/>
                                        </ns2:FlightSegment>
                                    </ns2:OriginDestinationOption>
                                    <ns2:OriginDestinationOption>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T06:25:00.000+03:00"
                                                           ArrivalDateTime="2022-05-30T07:50:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK2458"
                                                           JourneyDuration="P0DT1H25M0.000S" Ticket="eTicket"
                                                           CodeshareInd="false" DateChangeNbr="FALSE">
                                            <ns2:DepartureAirport LocationCode="IST"/>
                                            <ns2:ArrivalAirport LocationCode="ADA"/>
                                            <ns2:OperatingAirline CompanyShortName="TK"/>
                                            <ns2:Equipment Value="AIRBUS A321-231" AirEquipType="32R"/>
                                        </ns2:FlightSegment>
                                    </ns2:OriginDestinationOption>
                                    <ns2:OriginDestinationOption>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T08:00:00.000+03:00"
                                                           ArrivalDateTime="2022-05-30T09:20:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK2472"
                                                           JourneyDuration="P0DT1H20M0.000S" Ticket="eTicket"
                                                           CodeshareInd="false" DateChangeNbr="FALSE">
                                            <ns2:DepartureAirport LocationCode="IST"/>
                                            <ns2:ArrivalAirport LocationCode="ADA"/>
                                            <ns2:OperatingAirline CompanyShortName="TK"/>
                                            <ns2:Equipment Value="AIRBUS A320" AirEquipType="32N"/>
                                        </ns2:FlightSegment>
                                    </ns2:OriginDestinationOption>
                                    <ns2:OriginDestinationOption>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T10:55:00.000+03:00"
                                                           ArrivalDateTime="2022-05-30T12:20:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK2480"
                                                           JourneyDuration="P0DT1H25M0.000S" Ticket="eTicket"
                                                           CodeshareInd="false" DateChangeNbr="FALSE">
                                            <ns2:DepartureAirport LocationCode="IST"/>
                                            <ns2:ArrivalAirport LocationCode="ADA"/>
                                            <ns2:OperatingAirline CompanyShortName="TK"/>
                                            <ns2:Equipment Value="AIRBUS A320" AirEquipType="32N"/>
                                        </ns2:FlightSegment>
                                    </ns2:OriginDestinationOption>
                                    <ns2:OriginDestinationOption>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T12:20:00.000+03:00"
                                                           ArrivalDateTime="2022-05-30T13:45:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK2460"
                                                           JourneyDuration="P0DT1H25M0.000S" Ticket="eTicket"
                                                           CodeshareInd="false" DateChangeNbr="FALSE">
                                            <ns2:DepartureAirport LocationCode="IST"/>
                                            <ns2:ArrivalAirport LocationCode="ADA"/>
                                            <ns2:OperatingAirline CompanyShortName="TK"/>
                                            <ns2:Equipment Value="AIRBUS A320" AirEquipType="32N"/>
                                        </ns2:FlightSegment>
                                    </ns2:OriginDestinationOption>
                                    <ns2:OriginDestinationOption>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T08:00:00.000+03:00"
                                                           ArrivalDateTime="2022-05-30T08:55:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK7244"
                                                           JourneyDuration="P0DT0H55M0.000S" Ticket="eTicket"
                                                           GroundDuration="P0DT2H0M0.000S" CodeshareInd="false"
                                                           DateChangeNbr="FALSE">
                                            <ns2:DepartureAirport LocationCode="SAW"/>
                                            <ns2:ArrivalAirport LocationCode="ESB"/>
                                            <ns2:OperatingAirline CompanyShortName="AJ"/>
                                            <ns2:Equipment Value="UNKNOWN_PLANE" AirEquipType="7D3"/>
                                        </ns2:FlightSegment>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T10:55:00.000+03:00"
                                                           ArrivalDateTime="2022-05-30T11:55:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK7102"
                                                           JourneyDuration="P0DT1H0M0.000S" Ticket="eTicket"
                                                           CodeshareInd="false" DateChangeNbr="FALSE">
                                            <ns2:DepartureAirport LocationCode="ESB"/>
                                            <ns2:ArrivalAirport LocationCode="ADA"/>
                                            <ns2:OperatingAirline CompanyShortName="AJ"/>
                                            <ns2:Equipment Value="UNKNOWN_PLANE" AirEquipType="7D3"/>
                                        </ns2:FlightSegment>
                                    </ns2:OriginDestinationOption>
                                    <ns2:OriginDestinationOption>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T08:00:00.000+03:00"
                                                           ArrivalDateTime="2022-05-30T09:00:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK2108"
                                                           JourneyDuration="P0DT1H0M0.000S" Ticket="eTicket"
                                                           GroundDuration="P0DT1H55M0.000S" CodeshareInd="false"
                                                           DateChangeNbr="FALSE">
                                            <ns2:DepartureAirport LocationCode="IST"/>
                                            <ns2:ArrivalAirport LocationCode="ESB"/>
                                            <ns2:OperatingAirline CompanyShortName="TK"/>
                                            <ns2:Equipment Value="UNKNOWN_PLANE" AirEquipType="77B"/>
                                        </ns2:FlightSegment>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T10:55:00.000+03:00"
                                                           ArrivalDateTime="2022-05-30T11:55:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK7102"
                                                           JourneyDuration="P0DT1H0M0.000S" Ticket="eTicket"
                                                           CodeshareInd="false" DateChangeNbr="FALSE">
                                            <ns2:DepartureAirport LocationCode="ESB"/>
                                            <ns2:ArrivalAirport LocationCode="ADA"/>
                                            <ns2:OperatingAirline CompanyShortName="AJ"/>
                                            <ns2:Equipment Value="UNKNOWN_PLANE" AirEquipType="7D3"/>
                                        </ns2:FlightSegment>
                                    </ns2:OriginDestinationOption>
                                    <ns2:OriginDestinationOption>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T07:00:00.000+03:00"
                                                           ArrivalDateTime="2022-05-30T08:00:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK2118"
                                                           JourneyDuration="P0DT1H0M0.000S" Ticket="eTicket"
                                                           GroundDuration="P0DT2H55M0.000S" CodeshareInd="false"
                                                           DateChangeNbr="FALSE">
                                            <ns2:DepartureAirport LocationCode="IST"/>
                                            <ns2:ArrivalAirport LocationCode="ESB"/>
                                            <ns2:OperatingAirline CompanyShortName="TK"/>
                                            <ns2:Equipment Value="AIRBUS A319-100" AirEquipType="316"/>
                                        </ns2:FlightSegment>
                                        <ns2:FlightSegment DepartureDateTime="2022-05-30T10:55:00.000+03:00"
                                                           ArrivalDateTime="2022-05-30T11:55:00.000+03:00"
                                                           StopQuantity="0" FlightNumber="TK7102"
                                                           JourneyDuration="P0DT1H0M0.000S" Ticket="eTicket"
                                                           CodeshareInd="false" DateChangeNbr="FALSE">
                                            <ns2:DepartureAirport LocationCode="ESB"/>
                                            <ns2:ArrivalAirport LocationCode="ADA"/>
                                            <ns2:OperatingAirline CompanyShortName="AJ"/>
                                            <ns2:Equipment Value="UNKNOWN_PLANE" AirEquipType="7D3"/>
                                        </ns2:FlightSegment>
                                    </ns2:OriginDestinationOption>
                                </ns2:OriginDestinationOptions>
                            </ns2:OriginDestinationInformation>
                            <ns2:Comment/>
                        </ns2:OTA_AirAvailRS>
                        <extraOTAAvailabilityInfoListType>
                            <extraOTAAvailabilityInfoList RPH="IST_ADA_30MAY2022" isAllFlightsFullCodeShare="false"
                                                          isIndeedHasMoreFlightsForAnotherPortInTheSameCity="false">
                                <extraOTAFlightInfoListType>
                                    <extraOTAFlightInfoList flightNumber="TK2462" isCodeShare="false"
                                                            isFullCodeShare="false" isDomestic="true"
                                                            isFullInternational="false" isFullAvailable="true"
                                                            isElectronicTicketAvailable="true"
                                                            isPureAnadoluJetFlight="false" StandbyIndicator="false"
                                                            isMarketable="true">
                                        <bookingPriceInfoType>
                                            <ns2:PTC_FareBreakdowns>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2462">JBU
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="572.90"/>
                                                        <ns2:Taxes Amount="89.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="30.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="6.25"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="662.89"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="30"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="J">BUSINESS
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="BU" RPH="TK2462"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2462">OEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="324.00"/>
                                                        <ns2:Taxes Amount="81.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="22.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="3.70"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="405.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="15"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="O">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK2462"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2462">JBU
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="44.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.44"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="44.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="J">BUSINESS
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="BU" RPH="TK2462"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2462">OEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="291.60"/>
                                                        <ns2:Taxes Amount="81.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="22.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="3.38"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="373.59"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="15"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="O">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK2462"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2462">OEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.34"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="O">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK2462"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2462">OXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="364.00"/>
                                                        <ns2:Taxes Amount="86.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="4.15"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="450.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="O">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK2462"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2462">OXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="327.60"/>
                                                        <ns2:Taxes Amount="86.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="3.79"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="414.59"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="O">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK2462"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2462">OXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.34"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="O">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK2462"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2462">OPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="414.00"/>
                                                        <ns2:Taxes Amount="91.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="4.69"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="505.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="25"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="O">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK2462"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2462">OPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="372.60"/>
                                                        <ns2:Taxes Amount="91.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="4.28"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="464.59"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="25"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="O">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK2462"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2462">OPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.34"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="O">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK2462"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2462">JBU
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="674.00"/>
                                                        <ns2:Taxes Amount="89.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="30.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="7.25"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="763.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="30"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="J">BUSINESS
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="BU" RPH="TK2462"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                            </ns2:PTC_FareBreakdowns>
                                        </bookingPriceInfoType>
                                        <extraOTASegmentInfoListType>
                                            <extraOTASegmentInfoList segmentIndex="1" isAnadoluJetSegment="false"
                                                                     isAvailable="true" isConnected="false"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                        </extraOTASegmentInfoListType>
                                        <miniRulesList>
                                            <MiniRules RPH="TK2462" PassengerType="CNN" BrandCode="BU"
                                                       BrandKey="BUTK24">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>YES</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="2" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="30"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2462" PassengerType="ADT" BrandCode="EF"
                                                       BrandKey="EFTK21">
                                                <MealSubCode>0B3</MealSubCode>
                                                <MealCommercialName>ECONOMY CLASS MEAL</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2462" PassengerType="INF" BrandCode="BU"
                                                       BrandKey="BUTK24">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>YES</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="2" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="30"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2462" PassengerType="CNN" BrandCode="EF"
                                                       BrandKey="EFTK21">
                                                <MealSubCode>0B3</MealSubCode>
                                                <MealCommercialName>ECONOMY CLASS MEAL</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2462" PassengerType="INF" BrandCode="EF"
                                                       BrandKey="EFTK21">
                                                <MealSubCode>0B3</MealSubCode>
                                                <MealCommercialName>ECONOMY CLASS MEAL</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2462" PassengerType="ADT" BrandCode="XF"
                                                       BrandKey="XFTK22">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2462" PassengerType="CNN" BrandCode="XF"
                                                       BrandKey="XFTK22">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2462" PassengerType="INF" BrandCode="XF"
                                                       BrandKey="XFTK22">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2462" PassengerType="ADT" BrandCode="PF"
                                                       BrandKey="PFTK23">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2462" PassengerType="CNN" BrandCode="PF"
                                                       BrandKey="PFTK23">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2462" PassengerType="INF" BrandCode="PF"
                                                       BrandKey="PFTK23">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2462" PassengerType="ADT" BrandCode="BU"
                                                       BrandKey="BUTK24">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>YES</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="2" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="30"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                        </miniRulesList>
                                    </extraOTAFlightInfoList>
                                    <extraOTAFlightInfoList flightNumber="TK7280" isCodeShare="false"
                                                            isFullCodeShare="false" isDomestic="true"
                                                            isFullInternational="false" isFullAvailable="true"
                                                            isElectronicTicketAvailable="true"
                                                            isPureAnadoluJetFlight="true" StandbyIndicator="false"
                                                            isMarketable="true">
                                        <bookingPriceInfoType>
                                            <ns2:PTC_FareBreakdowns>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK7280">BEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="441.99"/>
                                                        <ns2:Taxes Amount="78.00">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="26.00"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="20.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="4.83"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="519.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="15"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK7280"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK7280">BEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="397.79"/>
                                                        <ns2:Taxes Amount="78.00">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="26.00"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="20.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="4.39"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="475.79"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="15"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK7280"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK7280">BEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.34"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK7280"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK7280">BXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="474.99"/>
                                                        <ns2:Taxes Amount="83.00">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="26.00"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="25.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="5.21"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="557.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK7280"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK7280">BXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="427.49"/>
                                                        <ns2:Taxes Amount="83.00">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="26.00"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="25.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="4.74"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="510.49"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK7280"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK7280">BXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.34"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK7280"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK7280">BPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="514.99"/>
                                                        <ns2:Taxes Amount="88.00">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="26.00"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="30.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="5.65"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="602.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK7280"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK7280">BPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="463.49"/>
                                                        <ns2:Taxes Amount="88.00">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="26.00"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="30.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="5.14"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="551.49"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK7280"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK7280">BPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.34"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK7280"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                            </ns2:PTC_FareBreakdowns>
                                        </bookingPriceInfoType>
                                        <extraOTASegmentInfoListType>
                                            <extraOTASegmentInfoList segmentIndex="1" isAnadoluJetSegment="true"
                                                                     isAvailable="true" isConnected="false"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                        </extraOTASegmentInfoListType>
                                        <miniRulesList>
                                            <MiniRules RPH="TK7280" PassengerType="ADT" BrandCode="EF" BrandKey="EFAJ1">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK7280" PassengerType="CNN" BrandCode="EF" BrandKey="EFAJ1">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK7280" PassengerType="INF" BrandCode="EF" BrandKey="EFAJ1">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK7280" PassengerType="ADT" BrandCode="XF" BrandKey="XFAJ3">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK7280" PassengerType="CNN" BrandCode="XF" BrandKey="XFAJ3">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK7280" PassengerType="INF" BrandCode="XF" BrandKey="XFAJ3">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK7280" PassengerType="ADT" BrandCode="PF" BrandKey="PFAJ5">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK7280" PassengerType="CNN" BrandCode="PF" BrandKey="PFAJ5">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK7280" PassengerType="INF" BrandCode="PF" BrandKey="PFAJ5">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                        </miniRulesList>
                                    </extraOTAFlightInfoList>
                                    <extraOTAFlightInfoList flightNumber="TK2466" isCodeShare="false"
                                                            isFullCodeShare="false" isDomestic="true"
                                                            isFullInternational="false" isFullAvailable="true"
                                                            isElectronicTicketAvailable="true"
                                                            isPureAnadoluJetFlight="false" StandbyIndicator="false"
                                                            isMarketable="true">
                                        <bookingPriceInfoType>
                                            <ns2:PTC_FareBreakdowns>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2466">JBU
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="572.90"/>
                                                        <ns2:Taxes Amount="89.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="30.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="6.25"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="662.89"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="30"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="J">BUSINESS
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="BU" RPH="TK2466"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2466">BEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="400.00"/>
                                                        <ns2:Taxes Amount="89.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="30.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="4.53"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="489.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="15"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK2466"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2466">JBU
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="44.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.44"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="44.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="J">BUSINESS
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="BU" RPH="TK2466"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2466">BEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="360.00"/>
                                                        <ns2:Taxes Amount="89.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="30.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="4.14"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="449.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="15"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK2466"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2466">BEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.34"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK2466"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2466">BXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="470.00"/>
                                                        <ns2:Taxes Amount="94.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="35.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="5.28"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="564.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK2466"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2466">BXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="423.00"/>
                                                        <ns2:Taxes Amount="94.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="35.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="4.81"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="517.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK2466"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2466">BXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.34"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK2466"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2466">BPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="520.00"/>
                                                        <ns2:Taxes Amount="99.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="40.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="5.82"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="619.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="25"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK2466"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2466">BPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="468.00"/>
                                                        <ns2:Taxes Amount="99.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="40.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="5.31"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="567.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="25"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK2466"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2466">BPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.34"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK2466"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2466">JBU
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="674.00"/>
                                                        <ns2:Taxes Amount="89.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="30.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="7.25"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="763.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="30"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="J">BUSINESS
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="BU" RPH="TK2466"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                            </ns2:PTC_FareBreakdowns>
                                        </bookingPriceInfoType>
                                        <extraOTASegmentInfoListType>
                                            <extraOTASegmentInfoList segmentIndex="1" isAnadoluJetSegment="false"
                                                                     isAvailable="true" isConnected="false"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                        </extraOTASegmentInfoListType>
                                        <miniRulesList>
                                            <MiniRules RPH="TK2466" PassengerType="CNN" BrandCode="BU"
                                                       BrandKey="BUTK24">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>YES</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="2" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="30"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2466" PassengerType="ADT" BrandCode="EF"
                                                       BrandKey="EFTK21">
                                                <MealSubCode>0B3</MealSubCode>
                                                <MealCommercialName>ECONOMY CLASS MEAL</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2466" PassengerType="INF" BrandCode="BU"
                                                       BrandKey="BUTK24">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>YES</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="2" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="30"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2466" PassengerType="CNN" BrandCode="EF"
                                                       BrandKey="EFTK21">
                                                <MealSubCode>0B3</MealSubCode>
                                                <MealCommercialName>ECONOMY CLASS MEAL</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2466" PassengerType="INF" BrandCode="EF"
                                                       BrandKey="EFTK21">
                                                <MealSubCode>0B3</MealSubCode>
                                                <MealCommercialName>ECONOMY CLASS MEAL</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2466" PassengerType="ADT" BrandCode="XF"
                                                       BrandKey="XFTK22">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2466" PassengerType="CNN" BrandCode="XF"
                                                       BrandKey="XFTK22">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2466" PassengerType="INF" BrandCode="XF"
                                                       BrandKey="XFTK22">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2466" PassengerType="ADT" BrandCode="PF"
                                                       BrandKey="PFTK23">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2466" PassengerType="CNN" BrandCode="PF"
                                                       BrandKey="PFTK23">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2466" PassengerType="INF" BrandCode="PF"
                                                       BrandKey="PFTK23">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2466" PassengerType="ADT" BrandCode="BU"
                                                       BrandKey="BUTK24">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>YES</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="2" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="30"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                        </miniRulesList>
                                    </extraOTAFlightInfoList>
                                    <extraOTAFlightInfoList flightNumber="TK2468" isCodeShare="false"
                                                            isFullCodeShare="false" isDomestic="true"
                                                            isFullInternational="false" isFullAvailable="true"
                                                            isElectronicTicketAvailable="true"
                                                            isPureAnadoluJetFlight="false" StandbyIndicator="false"
                                                            isMarketable="true">
                                        <bookingPriceInfoType>
                                            <ns2:PTC_FareBreakdowns>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2468">JBU
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="572.90"/>
                                                        <ns2:Taxes Amount="89.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="30.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="6.25"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="662.89"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="30"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="J">BUSINESS
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="BU" RPH="TK2468"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2468">MEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="416.00"/>
                                                        <ns2:Taxes Amount="89.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="30.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="4.69"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="505.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="15"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="M">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK2468"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2468">JBU
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="44.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.44"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="44.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="J">BUSINESS
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="BU" RPH="TK2468"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2468">MEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="374.40"/>
                                                        <ns2:Taxes Amount="89.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="30.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="4.28"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="464.39"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="15"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="M">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK2468"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2468">MEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.34"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="M">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK2468"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2468">MXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="456.00"/>
                                                        <ns2:Taxes Amount="94.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="35.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="5.14"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="550.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="M">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK2468"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2468">MXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="410.40"/>
                                                        <ns2:Taxes Amount="94.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="35.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="4.69"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="505.39"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="M">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK2468"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2468">MXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.34"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="M">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK2468"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2468">MPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="506.00"/>
                                                        <ns2:Taxes Amount="99.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="40.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="5.68"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="605.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="25"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="M">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK2468"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2468">MPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="455.40"/>
                                                        <ns2:Taxes Amount="99.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="40.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="5.18"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="555.39"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="25"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="M">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK2468"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2468">MPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.34"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="M">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK2468"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2468">JBU
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="674.00"/>
                                                        <ns2:Taxes Amount="89.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="30.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="7.25"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="763.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="30"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="J">BUSINESS
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="BU" RPH="TK2468"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                            </ns2:PTC_FareBreakdowns>
                                        </bookingPriceInfoType>
                                        <extraOTASegmentInfoListType>
                                            <extraOTASegmentInfoList segmentIndex="1" isAnadoluJetSegment="false"
                                                                     isAvailable="true" isConnected="false"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                        </extraOTASegmentInfoListType>
                                        <miniRulesList>
                                            <MiniRules RPH="TK2468" PassengerType="CNN" BrandCode="BU"
                                                       BrandKey="BUTK24">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>YES</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="2" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="30"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2468" PassengerType="ADT" BrandCode="EF"
                                                       BrandKey="EFTK21">
                                                <MealSubCode>0B3</MealSubCode>
                                                <MealCommercialName>ECONOMY CLASS MEAL</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2468" PassengerType="INF" BrandCode="BU"
                                                       BrandKey="BUTK24">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>YES</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="2" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="30"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2468" PassengerType="CNN" BrandCode="EF"
                                                       BrandKey="EFTK21">
                                                <MealSubCode>0B3</MealSubCode>
                                                <MealCommercialName>ECONOMY CLASS MEAL</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2468" PassengerType="INF" BrandCode="EF"
                                                       BrandKey="EFTK21">
                                                <MealSubCode>0B3</MealSubCode>
                                                <MealCommercialName>ECONOMY CLASS MEAL</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2468" PassengerType="ADT" BrandCode="XF"
                                                       BrandKey="XFTK22">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2468" PassengerType="CNN" BrandCode="XF"
                                                       BrandKey="XFTK22">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2468" PassengerType="INF" BrandCode="XF"
                                                       BrandKey="XFTK22">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2468" PassengerType="ADT" BrandCode="PF"
                                                       BrandKey="PFTK23">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2468" PassengerType="CNN" BrandCode="PF"
                                                       BrandKey="PFTK23">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2468" PassengerType="INF" BrandCode="PF"
                                                       BrandKey="PFTK23">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2468" PassengerType="ADT" BrandCode="BU"
                                                       BrandKey="BUTK24">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>YES</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="2" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="30"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                        </miniRulesList>
                                    </extraOTAFlightInfoList>
                                    <extraOTAFlightInfoList flightNumber="TK2470" isCodeShare="false"
                                                            isFullCodeShare="false" isDomestic="true"
                                                            isFullInternational="false" isFullAvailable="true"
                                                            isElectronicTicketAvailable="true"
                                                            isPureAnadoluJetFlight="false" StandbyIndicator="false"
                                                            isMarketable="true">
                                        <bookingPriceInfoType>
                                            <ns2:PTC_FareBreakdowns>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2470">JBU
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="572.90"/>
                                                        <ns2:Taxes Amount="89.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="30.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="6.25"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="662.89"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="30"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="J">BUSINESS
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="BU" RPH="TK2470"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2470">MEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="416.00"/>
                                                        <ns2:Taxes Amount="89.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="30.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="4.69"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="505.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="15"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="M">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK2470"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2470">JBU
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="44.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.44"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="44.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="J">BUSINESS
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="BU" RPH="TK2470"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2470">MEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="374.40"/>
                                                        <ns2:Taxes Amount="89.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="30.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="4.28"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="464.39"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="15"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="M">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK2470"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2470">MEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.34"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="M">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK2470"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2470">MXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="456.00"/>
                                                        <ns2:Taxes Amount="94.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="35.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="5.14"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="550.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="M">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK2470"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2470">MXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="410.40"/>
                                                        <ns2:Taxes Amount="94.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="35.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="4.69"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="505.39"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="M">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK2470"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2470">MXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.34"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="M">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK2470"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2470">MPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="506.00"/>
                                                        <ns2:Taxes Amount="99.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="40.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="5.68"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="605.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="25"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="M">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK2470"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2470">MPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="455.40"/>
                                                        <ns2:Taxes Amount="99.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="40.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="5.18"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="555.39"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="25"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="M">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK2470"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2470">MPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.34"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="M">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK2470"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2470">JBU
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="674.00"/>
                                                        <ns2:Taxes Amount="89.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="30.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="7.25"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="763.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="30"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="J">BUSINESS
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="BU" RPH="TK2470"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                            </ns2:PTC_FareBreakdowns>
                                        </bookingPriceInfoType>
                                        <extraOTASegmentInfoListType>
                                            <extraOTASegmentInfoList segmentIndex="1" isAnadoluJetSegment="false"
                                                                     isAvailable="true" isConnected="false"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                        </extraOTASegmentInfoListType>
                                        <miniRulesList>
                                            <MiniRules RPH="TK2470" PassengerType="CNN" BrandCode="BU"
                                                       BrandKey="BUTK24">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>YES</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="2" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="30"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2470" PassengerType="ADT" BrandCode="EF"
                                                       BrandKey="EFTK21">
                                                <MealSubCode>0B3</MealSubCode>
                                                <MealCommercialName>ECONOMY CLASS MEAL</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2470" PassengerType="INF" BrandCode="BU"
                                                       BrandKey="BUTK24">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>YES</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="2" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="30"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2470" PassengerType="CNN" BrandCode="EF"
                                                       BrandKey="EFTK21">
                                                <MealSubCode>0B3</MealSubCode>
                                                <MealCommercialName>ECONOMY CLASS MEAL</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2470" PassengerType="INF" BrandCode="EF"
                                                       BrandKey="EFTK21">
                                                <MealSubCode>0B3</MealSubCode>
                                                <MealCommercialName>ECONOMY CLASS MEAL</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2470" PassengerType="ADT" BrandCode="XF"
                                                       BrandKey="XFTK22">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2470" PassengerType="CNN" BrandCode="XF"
                                                       BrandKey="XFTK22">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2470" PassengerType="INF" BrandCode="XF"
                                                       BrandKey="XFTK22">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2470" PassengerType="ADT" BrandCode="PF"
                                                       BrandKey="PFTK23">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2470" PassengerType="CNN" BrandCode="PF"
                                                       BrandKey="PFTK23">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2470" PassengerType="INF" BrandCode="PF"
                                                       BrandKey="PFTK23">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2470" PassengerType="ADT" BrandCode="BU"
                                                       BrandKey="BUTK24">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>YES</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="2" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="30"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                        </miniRulesList>
                                    </extraOTAFlightInfoList>
                                    <extraOTAFlightInfoList flightNumber="TK7248TK7100" isCodeShare="false"
                                                            isFullCodeShare="false" isDomestic="true"
                                                            isFullInternational="false" isFullAvailable="true"
                                                            isElectronicTicketAvailable="true"
                                                            isPureAnadoluJetFlight="true" StandbyIndicator="false"
                                                            isMarketable="true">
                                        <bookingPriceInfoType>
                                            <ns2:PTC_FareBreakdowns>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK7248TK7100">BEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="662.00"/>
                                                        <ns2:Taxes Amount="130.00">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="43.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="52.00"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="35.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="7.42"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="792.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="15"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="2"
                                                                                  UnitOfMeasureQuantity="15"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="E">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK7248TK7100"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK7248TK7100">BEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="596.00"/>
                                                        <ns2:Taxes Amount="130.00">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="43.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="52.00"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="35.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="6.76"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="726.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="15"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="2"
                                                                                  UnitOfMeasureQuantity="15"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="E">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK7248TK7100"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK7248TK7100">BEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="39.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.39"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="39.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="2"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="E">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK7248TK7100"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK7248TK7100">BXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="707.00"/>
                                                        <ns2:Taxes Amount="140.00">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="43.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="52.00"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="45.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="7.96"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="847.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="2"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="E">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK7248TK7100"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK7248TK7100">BXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="636.00"/>
                                                        <ns2:Taxes Amount="140.00">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="43.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="52.00"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="45.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="7.26"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="776.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="2"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="E">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK7248TK7100"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK7248TK7100">BXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="39.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.39"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="39.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="2"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="E">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK7248TK7100"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK7248TK7100">BPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="737.00"/>
                                                        <ns2:Taxes Amount="150.00">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="43.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="52.00"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="55.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="8.36"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="887.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="2"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="E">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK7248TK7100"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK7248TK7100">BPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="663.00"/>
                                                        <ns2:Taxes Amount="150.00">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="43.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="52.00"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="55.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="7.62"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="813.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="2"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="E">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK7248TK7100"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK7248TK7100">BPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="39.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.39"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="39.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="2"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="E">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareReference ResBookDesigCode="B">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK7248TK7100"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                            </ns2:PTC_FareBreakdowns>
                                        </bookingPriceInfoType>
                                        <extraOTASegmentInfoListType>
                                            <extraOTASegmentInfoList segmentIndex="1" isAnadoluJetSegment="true"
                                                                     isAvailable="true" isConnected="true"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                            <extraOTASegmentInfoList segmentIndex="2" isAnadoluJetSegment="true"
                                                                     isAvailable="true" isConnected="false"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                        </extraOTASegmentInfoListType>
                                        <miniRulesList>
                                            <MiniRules RPH="TK7248TK7100" PassengerType="ADT" BrandCode="EF"
                                                       BrandKey="EFAJ1">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK7248TK7100" PassengerType="CNN" BrandCode="EF"
                                                       BrandKey="EFAJ1">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK7248TK7100" PassengerType="INF" BrandCode="EF"
                                                       BrandKey="EFAJ1">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK7248TK7100" PassengerType="ADT" BrandCode="XF"
                                                       BrandKey="XFAJ3">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK7248TK7100" PassengerType="CNN" BrandCode="XF"
                                                       BrandKey="XFAJ3">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK7248TK7100" PassengerType="INF" BrandCode="XF"
                                                       BrandKey="XFAJ3">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK7248TK7100" PassengerType="ADT" BrandCode="PF"
                                                       BrandKey="PFAJ5">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK7248TK7100" PassengerType="CNN" BrandCode="PF"
                                                       BrandKey="PFAJ5">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK7248TK7100" PassengerType="INF" BrandCode="PF"
                                                       BrandKey="PFAJ5">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                        </miniRulesList>
                                    </extraOTAFlightInfoList>
                                    <extraOTAFlightInfoList flightNumber="TK2474" isCodeShare="false"
                                                            isFullCodeShare="false" isDomestic="true"
                                                            isFullInternational="false" isFullAvailable="true"
                                                            isElectronicTicketAvailable="true"
                                                            isPureAnadoluJetFlight="false" StandbyIndicator="false"
                                                            isMarketable="true">
                                        <bookingPriceInfoType>
                                            <ns2:PTC_FareBreakdowns>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2474">JBU
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="572.90"/>
                                                        <ns2:Taxes Amount="89.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="30.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="6.25"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="662.89"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="30"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="J">BUSINESS
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="BU" RPH="TK2474"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2474">OEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="324.00"/>
                                                        <ns2:Taxes Amount="81.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="22.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="3.70"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="405.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="15"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="O">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK2474"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2474">JBU
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="44.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.44"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="44.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="J">BUSINESS
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="BU" RPH="TK2474"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2474">OEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="291.60"/>
                                                        <ns2:Taxes Amount="81.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="22.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="3.38"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="373.59"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="15"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="O">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK2474"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2474">OEF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.34"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="O">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="EF" RPH="TK2474"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2474">OXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="364.00"/>
                                                        <ns2:Taxes Amount="86.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="4.15"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="450.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="O">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK2474"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2474">OXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="327.60"/>
                                                        <ns2:Taxes Amount="86.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="3.79"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="414.59"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="20"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="O">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK2474"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2474">OXF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.34"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="O">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="XF" RPH="TK2474"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2474">OPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="414.00"/>
                                                        <ns2:Taxes Amount="91.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="4.69"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="505.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="25"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="O">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK2474"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="CNN" CodeContext="CNN"
                                                                               Quantity="1"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2474">OPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="372.60"/>
                                                        <ns2:Taxes Amount="91.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="4.28"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="464.59"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="25"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="O">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK2474"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="INF" CodeContext="INF"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2474">OPF
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:Taxes Amount="0.00">
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="0.34"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="34.00"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="10"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="O">ECONOMY
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="PF" RPH="TK2474"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                                <ns2:PTC_FareBreakdown>
                                                    <ns2:PassengerTypeQuantity Code="ADT" CodeContext="ADT"
                                                                               Quantity="2"/>
                                                    <ns2:FareBasisCodes>
                                                        <ns2:FareBasisCode FlightSegmentRPH="TK2474">JBU
                                                        </ns2:FareBasisCode>
                                                    </ns2:FareBasisCodes>
                                                    <ns2:PassengerFare>
                                                        <ns2:BaseFare CurrencyCode="TRY" Amount="674.00"/>
                                                        <ns2:Taxes Amount="89.99">
                                                            <ns2:Tax TaxCode="VQ" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="32.00"/>
                                                            <ns2:Tax TaxCode="YR" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="27.99"/>
                                                            <ns2:Tax TaxCode="DU" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="30.00"/>
                                                            <ns2:Tax TaxCode="KD" RefundableInd="false"
                                                                     CurrencyCode="TRY" Amount="7.25"/>
                                                        </ns2:Taxes>
                                                        <ns2:TotalFare CurrencyCode="TRY" Amount="763.99"/>
                                                        <ns2:FareBaggageAllowance FlightSegmentRPH="1"
                                                                                  UnitOfMeasureQuantity="30"
                                                                                  UnitOfMeasure="KILO"
                                                                                  UnitOfMeasureCode="1 X PIECE"/>
                                                    </ns2:PassengerFare>
                                                    <ns2:FareInfo>
                                                        <ns2:FareReference ResBookDesigCode="J">BUSINESS
                                                        </ns2:FareReference>
                                                        <ns2:FareInfo FareType="BU" RPH="TK2474"/>
                                                        <ns2:PassengerFare/>
                                                    </ns2:FareInfo>
                                                </ns2:PTC_FareBreakdown>
                                            </ns2:PTC_FareBreakdowns>
                                        </bookingPriceInfoType>
                                        <extraOTASegmentInfoListType>
                                            <extraOTASegmentInfoList segmentIndex="1" isAnadoluJetSegment="false"
                                                                     isAvailable="true" isConnected="false"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                        </extraOTASegmentInfoListType>
                                        <miniRulesList>
                                            <MiniRules RPH="TK2474" PassengerType="CNN" BrandCode="BU"
                                                       BrandKey="BUTK24">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>YES</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="2" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="30"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2474" PassengerType="ADT" BrandCode="EF"
                                                       BrandKey="EFTK21">
                                                <MealSubCode>0B3</MealSubCode>
                                                <MealCommercialName>ECONOMY CLASS MEAL</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2474" PassengerType="INF" BrandCode="BU"
                                                       BrandKey="BUTK24">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>YES</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="2" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="30"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2474" PassengerType="CNN" BrandCode="EF"
                                                       BrandKey="EFTK21">
                                                <MealSubCode>0B3</MealSubCode>
                                                <MealCommercialName>ECONOMY CLASS MEAL</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2474" PassengerType="INF" BrandCode="EF"
                                                       BrandKey="EFTK21">
                                                <MealSubCode>0B3</MealSubCode>
                                                <MealCommercialName>ECONOMY CLASS MEAL</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="15"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2474" PassengerType="ADT" BrandCode="XF"
                                                       BrandKey="XFTK22">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2474" PassengerType="CNN" BrandCode="XF"
                                                       BrandKey="XFTK22">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2474" PassengerType="INF" BrandCode="XF"
                                                       BrandKey="XFTK22">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="20"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2474" PassengerType="ADT" BrandCode="PF"
                                                       BrandKey="PFTK23">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2474" PassengerType="CNN" BrandCode="PF"
                                                       BrandKey="PFTK23">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2474" PassengerType="INF" BrandCode="PF"
                                                       BrandKey="PFTK23">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>NO</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="25"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>PENALTY_CHARGE</ReissueRule>
                                                    <RefundRule>PENALTY_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                            <MiniRules RPH="TK2474" PassengerType="ADT" BrandCode="BU"
                                                       BrandKey="BUTK24">
                                                <MealSubCode>000</MealSubCode>
                                                <MealCommercialName>NO MEAL SERVICE</MealCommercialName>
                                                <BusinessLounge>YES</BusinessLounge>
                                                <CarryOnBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="2" kilos="8"/>
                                                </CarryOnBaggageAllowance>
                                                <CheckedBaggageAllowance>
                                                    <FreeBaggageAllowance pieces="1" kilos="30"/>
                                                </CheckedBaggageAllowance>
                                                <PenaltyMiniRule>
                                                    <ReissueRule>FREE_CHARGE</ReissueRule>
                                                    <RefundRule>FREE_CHARGE</RefundRule>
                                                </PenaltyMiniRule>
                                            </MiniRules>
                                        </miniRulesList>
                                    </extraOTAFlightInfoList>
                                    <extraOTAFlightInfoList flightNumber="TK2476" isCodeShare="false"
                                                            isFullCodeShare="false" isDomestic="true"
                                                            isFullInternational="false" isFullAvailable="false"
                                                            isElectronicTicketAvailable="true"
                                                            isPureAnadoluJetFlight="false" StandbyIndicator="false"
                                                            isMarketable="true">
                                        <bookingPriceInfoType>
                                            <ns2:PTC_FareBreakdowns/>
                                        </bookingPriceInfoType>
                                        <extraOTASegmentInfoListType>
                                            <extraOTASegmentInfoList segmentIndex="1" isAnadoluJetSegment="false"
                                                                     isAvailable="true" isConnected="false"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                        </extraOTASegmentInfoListType>
                                        <miniRulesList/>
                                    </extraOTAFlightInfoList>
                                    <extraOTAFlightInfoList flightNumber="TK2454" isCodeShare="false"
                                                            isFullCodeShare="false" isDomestic="true"
                                                            isFullInternational="false" isFullAvailable="false"
                                                            isElectronicTicketAvailable="true"
                                                            isPureAnadoluJetFlight="false" StandbyIndicator="false"
                                                            isMarketable="true">
                                        <bookingPriceInfoType>
                                            <ns2:PTC_FareBreakdowns/>
                                        </bookingPriceInfoType>
                                        <extraOTASegmentInfoListType>
                                            <extraOTASegmentInfoList segmentIndex="1" isAnadoluJetSegment="false"
                                                                     isAvailable="true" isConnected="false"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                        </extraOTASegmentInfoListType>
                                        <miniRulesList/>
                                    </extraOTAFlightInfoList>
                                    <extraOTAFlightInfoList flightNumber="TK2458" isCodeShare="false"
                                                            isFullCodeShare="false" isDomestic="true"
                                                            isFullInternational="false" isFullAvailable="false"
                                                            isElectronicTicketAvailable="true"
                                                            isPureAnadoluJetFlight="false" StandbyIndicator="false"
                                                            isMarketable="true">
                                        <bookingPriceInfoType>
                                            <ns2:PTC_FareBreakdowns/>
                                        </bookingPriceInfoType>
                                        <extraOTASegmentInfoListType>
                                            <extraOTASegmentInfoList segmentIndex="1" isAnadoluJetSegment="false"
                                                                     isAvailable="true" isConnected="false"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                        </extraOTASegmentInfoListType>
                                        <miniRulesList/>
                                    </extraOTAFlightInfoList>
                                    <extraOTAFlightInfoList flightNumber="TK2472" isCodeShare="false"
                                                            isFullCodeShare="false" isDomestic="true"
                                                            isFullInternational="false" isFullAvailable="false"
                                                            isElectronicTicketAvailable="true"
                                                            isPureAnadoluJetFlight="false" StandbyIndicator="false"
                                                            isMarketable="true">
                                        <bookingPriceInfoType>
                                            <ns2:PTC_FareBreakdowns/>
                                        </bookingPriceInfoType>
                                        <extraOTASegmentInfoListType>
                                            <extraOTASegmentInfoList segmentIndex="1" isAnadoluJetSegment="false"
                                                                     isAvailable="true" isConnected="false"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                        </extraOTASegmentInfoListType>
                                        <miniRulesList/>
                                    </extraOTAFlightInfoList>
                                    <extraOTAFlightInfoList flightNumber="TK2480" isCodeShare="false"
                                                            isFullCodeShare="false" isDomestic="true"
                                                            isFullInternational="false" isFullAvailable="false"
                                                            isElectronicTicketAvailable="true"
                                                            isPureAnadoluJetFlight="false" StandbyIndicator="false"
                                                            isMarketable="true">
                                        <bookingPriceInfoType>
                                            <ns2:PTC_FareBreakdowns/>
                                        </bookingPriceInfoType>
                                        <extraOTASegmentInfoListType>
                                            <extraOTASegmentInfoList segmentIndex="1" isAnadoluJetSegment="false"
                                                                     isAvailable="true" isConnected="false"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                        </extraOTASegmentInfoListType>
                                        <miniRulesList/>
                                    </extraOTAFlightInfoList>
                                    <extraOTAFlightInfoList flightNumber="TK2460" isCodeShare="false"
                                                            isFullCodeShare="false" isDomestic="true"
                                                            isFullInternational="false" isFullAvailable="false"
                                                            isElectronicTicketAvailable="true"
                                                            isPureAnadoluJetFlight="false" StandbyIndicator="false"
                                                            isMarketable="true">
                                        <bookingPriceInfoType>
                                            <ns2:PTC_FareBreakdowns/>
                                        </bookingPriceInfoType>
                                        <extraOTASegmentInfoListType>
                                            <extraOTASegmentInfoList segmentIndex="1" isAnadoluJetSegment="false"
                                                                     isAvailable="true" isConnected="false"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                        </extraOTASegmentInfoListType>
                                        <miniRulesList/>
                                    </extraOTAFlightInfoList>
                                    <extraOTAFlightInfoList flightNumber="TK7244TK7102" isCodeShare="false"
                                                            isFullCodeShare="false" isDomestic="true"
                                                            isFullInternational="false" isFullAvailable="false"
                                                            isElectronicTicketAvailable="true"
                                                            isPureAnadoluJetFlight="true" StandbyIndicator="false"
                                                            isMarketable="true">
                                        <bookingPriceInfoType>
                                            <ns2:PTC_FareBreakdowns/>
                                        </bookingPriceInfoType>
                                        <extraOTASegmentInfoListType>
                                            <extraOTASegmentInfoList segmentIndex="1" isAnadoluJetSegment="true"
                                                                     isAvailable="true" isConnected="true"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                            <extraOTASegmentInfoList segmentIndex="2" isAnadoluJetSegment="true"
                                                                     isAvailable="true" isConnected="false"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                        </extraOTASegmentInfoListType>
                                        <miniRulesList/>
                                    </extraOTAFlightInfoList>
                                    <extraOTAFlightInfoList flightNumber="TK2108TK7102" isCodeShare="false"
                                                            isFullCodeShare="false" isDomestic="true"
                                                            isFullInternational="false" isFullAvailable="false"
                                                            isElectronicTicketAvailable="true"
                                                            isPureAnadoluJetFlight="false" StandbyIndicator="false"
                                                            isMarketable="true">
                                        <bookingPriceInfoType>
                                            <ns2:PTC_FareBreakdowns/>
                                        </bookingPriceInfoType>
                                        <extraOTASegmentInfoListType>
                                            <extraOTASegmentInfoList segmentIndex="1" isAnadoluJetSegment="false"
                                                                     isAvailable="true" isConnected="true"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                            <extraOTASegmentInfoList segmentIndex="2" isAnadoluJetSegment="true"
                                                                     isAvailable="true" isConnected="false"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                        </extraOTASegmentInfoListType>
                                        <miniRulesList/>
                                    </extraOTAFlightInfoList>
                                    <extraOTAFlightInfoList flightNumber="TK2118TK7102" isCodeShare="false"
                                                            isFullCodeShare="false" isDomestic="true"
                                                            isFullInternational="false" isFullAvailable="false"
                                                            isElectronicTicketAvailable="true"
                                                            isPureAnadoluJetFlight="false" StandbyIndicator="false"
                                                            isMarketable="true">
                                        <bookingPriceInfoType>
                                            <ns2:PTC_FareBreakdowns/>
                                        </bookingPriceInfoType>
                                        <extraOTASegmentInfoListType>
                                            <extraOTASegmentInfoList segmentIndex="1" isAnadoluJetSegment="false"
                                                                     isAvailable="true" isConnected="true"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                            <extraOTASegmentInfoList segmentIndex="2" isAnadoluJetSegment="true"
                                                                     isAvailable="true" isConnected="false"
                                                                     isDomestic="true" isStandBySeat="false"/>
                                        </extraOTASegmentInfoListType>
                                        <miniRulesList/>
                                    </extraOTAFlightInfoList>
                                </extraOTAFlightInfoListType>
                            </extraOTAAvailabilityInfoList>
                        </extraOTAAvailabilityInfoListType>
                    </createOTAAirRoute>
                    <isMixCabin>false</isMixCabin>
                    <extraOTABrandInfoList>
                        <fareBrandOtaResponseItems>
                            <BrandIndex>1</BrandIndex>
                            <BrandCode>EF</BrandCode>
                            <BrandName>ECO FLY</BrandName>
                            <BrandKey>EFAJ1</BrandKey>
                            <CarrierCode>AJ</CarrierCode>
                            <SeatSelection>false</SeatSelection>
                            <BonusMile>false</BonusMile>
                            <BonusMileAmount>0</BonusMileAmount>
                        </fareBrandOtaResponseItems>
                        <fareBrandOtaResponseItems>
                            <BrandIndex>1</BrandIndex>
                            <BrandCode>EF</BrandCode>
                            <BrandName>ECOFLY</BrandName>
                            <BrandKey>EFTK42</BrandKey>
                            <CarrierCode>TK</CarrierCode>
                            <SeatSelection>false</SeatSelection>
                            <BonusMile>false</BonusMile>
                            <BonusMileAmount>0</BonusMileAmount>
                        </fareBrandOtaResponseItems>
                        <fareBrandOtaResponseItems>
                            <BrandIndex>1</BrandIndex>
                            <BrandCode>EF</BrandCode>
                            <BrandName>ECOFLY</BrandName>
                            <BrandKey>EFTK21</BrandKey>
                            <CarrierCode>TK</CarrierCode>
                            <SeatSelection>false</SeatSelection>
                            <BonusMile>false</BonusMile>
                            <BonusMileAmount>0</BonusMileAmount>
                        </fareBrandOtaResponseItems>
                        <fareBrandOtaResponseItems>
                            <BrandIndex>2</BrandIndex>
                            <BrandCode>XF</BrandCode>
                            <BrandName>EXTRA FLY</BrandName>
                            <BrandKey>XFAJ3</BrandKey>
                            <CarrierCode>AJ</CarrierCode>
                            <SeatSelection>true</SeatSelection>
                            <BonusMile>false</BonusMile>
                            <BonusMileAmount>0</BonusMileAmount>
                        </fareBrandOtaResponseItems>
                        <fareBrandOtaResponseItems>
                            <BrandIndex>2</BrandIndex>
                            <BrandCode>XF</BrandCode>
                            <BrandName>EXTRAFLY</BrandName>
                            <BrandKey>XFTK43</BrandKey>
                            <CarrierCode>TK</CarrierCode>
                            <SeatSelection>false</SeatSelection>
                            <BonusMile>true</BonusMile>
                            <BonusMileAmount>250</BonusMileAmount>
                        </fareBrandOtaResponseItems>
                        <fareBrandOtaResponseItems>
                            <BrandIndex>2</BrandIndex>
                            <BrandCode>XF</BrandCode>
                            <BrandName>EXTRAFLY</BrandName>
                            <BrandKey>XFTK22</BrandKey>
                            <CarrierCode>TK</CarrierCode>
                            <SeatSelection>true</SeatSelection>
                            <BonusMile>true</BonusMile>
                            <BonusMileAmount>250</BonusMileAmount>
                        </fareBrandOtaResponseItems>
                        <fareBrandOtaResponseItems>
                            <BrandIndex>3</BrandIndex>
                            <BrandCode>PF</BrandCode>
                            <BrandName>PRIME FLY</BrandName>
                            <BrandKey>PFAJ5</BrandKey>
                            <CarrierCode>AJ</CarrierCode>
                            <SeatSelection>true</SeatSelection>
                            <BonusMile>false</BonusMile>
                            <BonusMileAmount>0</BonusMileAmount>
                        </fareBrandOtaResponseItems>
                        <fareBrandOtaResponseItems>
                            <BrandIndex>3</BrandIndex>
                            <BrandCode>PF</BrandCode>
                            <BrandName>PRIMEFLY</BrandName>
                            <BrandKey>PFTK44</BrandKey>
                            <CarrierCode>TK</CarrierCode>
                            <SeatSelection>false</SeatSelection>
                            <BonusMile>true</BonusMile>
                            <BonusMileAmount>500</BonusMileAmount>
                        </fareBrandOtaResponseItems>
                        <fareBrandOtaResponseItems>
                            <BrandIndex>3</BrandIndex>
                            <BrandCode>PF</BrandCode>
                            <BrandName>PRIMEFLY</BrandName>
                            <BrandKey>PFTK23</BrandKey>
                            <CarrierCode>TK</CarrierCode>
                            <SeatSelection>true</SeatSelection>
                            <BonusMile>true</BonusMile>
                            <BonusMileAmount>500</BonusMileAmount>
                        </fareBrandOtaResponseItems>
                        <fareBrandOtaResponseItems>
                            <BrandIndex>4</BrandIndex>
                            <BrandCode>BU</BrandCode>
                            <BrandName>BUSINESS</BrandName>
                            <BrandKey>BUTK24</BrandKey>
                            <CarrierCode>TK</CarrierCode>
                            <SeatSelection>true</SeatSelection>
                            <BonusMile>false</BonusMile>
                            <BonusMileAmount>0</BonusMileAmount>
                        </fareBrandOtaResponseItems>
                    </extraOTABrandInfoList>
                </availabilityOTAResponse>
                <availabilityFlexPricerResponse/>
                <requestInfos>
                    <requestInfo key="targetSource" value="ALLFARES"/>
                    <requestInfo key="origin" value="IST"/>
                    <requestInfo key="destination" value="ADA"/>
                    <requestInfo key="routingType" value=""/>
                    <requestInfo key="originCountry" value="TR"/>
                    <requestInfo key="destinationCountry" value="TR"/>
                    <requestInfo key="travelMonth" value="MAY"/>
                    <requestInfo key="travelDay" value="30.05.2022"/>
                    <requestInfo key="calendarPeriod" value="P0D"/>
                    <requestInfo key="passengerIp" value=""/>
                    <requestInfo key="travelDuration" value=""/>
                    <requestInfo key="preferredCabin" value="ECONOMY"/>
                    <requestInfo key="occupiedSeatNumber" value="3"/>
                    <requestInfo key="infantCount" value="2"/>
                    <requestInfo key="priorToFlight" value="0"/>
                    <requestInfo key="totalPassengerCount" value="5"/>
                    <requestInfo key="accountCode" value=""/>
                    <requestInfo key="adultCount" value="2"/>
                    <requestInfo key="childCount" value="1"/>
                    <requestInfo key="studentCount" value="0"/>
                </requestInfos>
            </ns0:createAvailabilityOTAResponse>
            <responseHeader>
                <ns3:statusCode>SUCCESS</ns3:statusCode>
                <ns3:clientTransactionId>KBS_20bd-683c-467e-5f67-6045e4f52095</ns3:clientTransactionId>
                <ns3:serverTransactionId>baf2c309-f1b8-4cbd-a261-2e30225432aa</ns3:serverTransactionId>
            </responseHeader>
        </ns0:getAvailabilityResponse>
    </S:Body>
</S:Envelope>
