<S:Envelope xmlns:env="http://schemas.xmlsoap.org/soap/envelope/" xmlns:S="http://schemas.xmlsoap.org/soap/envelope/">
    <env:Header/>
    <S:Body>
        <ns0:getAvailabilityResponse xmlns:ns0="http://service.thy.com/"
                                     xmlns:ns2="http://www.opentravel.org/OTA/2003/05"
                                     xmlns:ns3="http://www.thy.com/ws/responseHeader"
                                     xmlns:ns1="http://www.thy.com/ws/requestHeader">
            <ns0:createAvailabilityOTAResponse>
                <availabilityOTAResponse/>
                <availabilityFlexPricerResponse>
                    <flexPricerAvailabilityOutput
                            jSessionId="ubrehp+b4a533e43ebea7567cf9b61cf4589341~KCf0o5slpR7pFp4Z8qtS7_RilYfq68jLAobPh00o!1653348411225.aere-xml-controller-84-q2nc7">
                        <PAGE_TICKET>0</PAGE_TICKET>
                        <FLOW_SIGNATURE>
                            <TRANSACTION>0</TRANSACTION>
                            <INITIAL_PRODUCT>1</INITIAL_PRODUCT>
                            <BOOKING_TYPE>0</BOOKING_TYPE>
                        </FLOW_SIGNATURE>
                        <LIST_PANEL>
                            <LIST_TAB>
                                <LIST_DATE>
                                    <DATE code="2022-05-24T00:00:00">Tuesday, May 24, 2022</DATE>
                                </LIST_DATE>
                                <LIST_RECOMMENDATION>
                                    <RECOMMENDATION_ID>0</RECOMMENDATION_ID>
                                    <LIST_BOUND>
                                        <LIST_FLIGHT>
                                            <FLIGHT_ID>0</FLIGHT_ID>
                                            <DISPLAY_LAST_SEATS>NO_NO_RULE</DISPLAY_LAST_SEATS>
                                            <NUMBER_OF_LAST_SEATS>9</NUMBER_OF_LAST_SEATS>
                                            <LSA_DEBUG_INFO>
                                                <FIRST_FLIGHT_NUMBER
                                                        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">4
                                                </FIRST_FLIGHT_NUMBER>
                                                <RBD xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">M</RBD>
                                            </LSA_DEBUG_INFO>
                                        </LIST_FLIGHT>
                                    </LIST_BOUND>
                                    <FARE_FAMILY>
                                        <SHORT_NAME>FX</SHORT_NAME>
                                        <BRAND_NAME>Brand name not yet defined</BRAND_NAME>
                                        <COLOR>#E0EBEC</COLOR>
                                        <HIERARCHY>6500</HIERARCHY>
                                    </FARE_FAMILY>
                                    <LIST_TRIP_PRICE>
                                        <TOTAL_AMOUNT>1252.80</TOTAL_AMOUNT>
                                        <AMOUNT>1252.80</AMOUNT>
                                        <AMOUNT_WITHOUT_TAX>1077.00</AMOUNT_WITHOUT_TAX>
                                        <TAX>175.80</TAX>
                                        <EXCHANGE_RATE>1.00</EXCHANGE_RATE>
                                        <CURRENCY>
                                            <CODE>USD</CODE>
                                            <NAME>US Dollar</NAME>
                                        </CURRENCY>
                                        <LIST_DISPLAY_TAX>
                                            <CODE>YQ</CODE>
                                            <VALUE>146.0</VALUE>
                                        </LIST_DISPLAY_TAX>
                                        <LIST_DISPLAY_TAX>
                                            <CODE>US</CODE>
                                            <VALUE>19.7</VALUE>
                                        </LIST_DISPLAY_TAX>
                                    </LIST_TRIP_PRICE>
                                    <LIST_PNR>
                                        <LIST_TRAVELLER_TYPE>
                                            <LIST_TRAVELLER>
                                                <HAS_INFANT>false</HAS_INFANT>
                                                <IS_PRIMARY_TRAVELLER>true</IS_PRIMARY_TRAVELLER>
                                                <REQUESTED_TRAVELLER_TYPE>
                                                    <CODE>ADT</CODE>
                                                    <NAME>Adult</NAME>
                                                </REQUESTED_TRAVELLER_TYPE>
                                            </LIST_TRAVELLER>
                                            <TRAVELLER_TYPE>
                                                <CODE>ADT</CODE>
                                                <NAME>Adult</NAME>
                                            </TRAVELLER_TYPE>
                                            <NUMBER>1</NUMBER>
                                            <LIST_BOUND>
                                                <LIST_SEGMENT>
                                                    <SEGMENT_ID>0</SEGMENT_ID>
                                                    <FARE_CLASS>MKY3XOX</FARE_CLASS>
                                                    <RBD>M</RBD>
                                                    <CABIN>M</CABIN>
                                                    <FARE_FAMILY>
                                                        <SHORT_NAME>FX</SHORT_NAME>
                                                        <BRAND_NAME>Brand name not yet defined</BRAND_NAME>
                                                        <COLOR>#E0EBEC</COLOR>
                                                        <HIERARCHY>6500</HIERARCHY>
                                                    </FARE_FAMILY>
                                                    <LIST_PTC_APPLIED>
                                                        <CODE>ADT</CODE>
                                                    </LIST_PTC_APPLIED>
                                                    <LIST_FARE_TYPES>
                                                        <CODE>RP</CODE>
                                                        <NAME>Public</NAME>
                                                    </LIST_FARE_TYPES>
                                                </LIST_SEGMENT>
                                            </LIST_BOUND>
                                            <LIST_TRAVELLER_PRICE>
                                                <TOTAL_AMOUNT>1252.80</TOTAL_AMOUNT>
                                                <AMOUNT>1252.80</AMOUNT>
                                                <AMOUNT_WITHOUT_TAX>1077.00</AMOUNT_WITHOUT_TAX>
                                                <TAX>175.80</TAX>
                                                <EXCHANGE_RATE>1.00</EXCHANGE_RATE>
                                                <CURRENCY>
                                                    <CODE>USD</CODE>
                                                    <NAME>US Dollar</NAME>
                                                </CURRENCY>
                                                <LIST_DISPLAY_TAX>
                                                    <CODE>YQ</CODE>
                                                    <VALUE>146.0</VALUE>
                                                </LIST_DISPLAY_TAX>
                                                <LIST_DISPLAY_TAX>
                                                    <CODE>US</CODE>
                                                    <VALUE>19.7</VALUE>
                                                </LIST_DISPLAY_TAX>
                                            </LIST_TRAVELLER_PRICE>
                                            <LIST_TRAVELLER_TYPE_PRICE>
                                                <TOTAL_AMOUNT>1252.80</TOTAL_AMOUNT>
                                                <AMOUNT>1252.80</AMOUNT>
                                                <AMOUNT_WITHOUT_TAX>1077.00</AMOUNT_WITHOUT_TAX>
                                                <TAX>175.80</TAX>
                                                <EXCHANGE_RATE>1.00</EXCHANGE_RATE>
                                                <CURRENCY>
                                                    <CODE>USD</CODE>
                                                    <NAME>US Dollar</NAME>
                                                </CURRENCY>
                                                <LIST_DISPLAY_TAX>
                                                    <CODE>YQ</CODE>
                                                    <VALUE>146.0</VALUE>
                                                </LIST_DISPLAY_TAX>
                                                <LIST_DISPLAY_TAX>
                                                    <CODE>US</CODE>
                                                    <VALUE>19.7</VALUE>
                                                </LIST_DISPLAY_TAX>
                                            </LIST_TRAVELLER_TYPE_PRICE>
                                        </LIST_TRAVELLER_TYPE>
                                        <LIST_PNR_PRICE>
                                            <TOTAL_AMOUNT>1252.80</TOTAL_AMOUNT>
                                            <AMOUNT>1252.80</AMOUNT>
                                            <AMOUNT_WITHOUT_TAX>1077.00</AMOUNT_WITHOUT_TAX>
                                            <TAX>175.80</TAX>
                                            <EXCHANGE_RATE>1.00</EXCHANGE_RATE>
                                            <CURRENCY>
                                                <CODE>USD</CODE>
                                                <NAME>US Dollar</NAME>
                                            </CURRENCY>
                                            <LIST_DISPLAY_TAX>
                                                <CODE>YQ</CODE>
                                                <VALUE>146.0</VALUE>
                                            </LIST_DISPLAY_TAX>
                                            <LIST_DISPLAY_TAX>
                                                <CODE>US</CODE>
                                                <VALUE>19.7</VALUE>
                                            </LIST_DISPLAY_TAX>
                                        </LIST_PNR_PRICE>
                                    </LIST_PNR>
                                    <RECOMMENDATION_DESCRIPTION>
                                        <LIST_COMBINATION_DESCRIPTION>
                                            <LIST_BOUND>
                                                <FLIGHT_ID>0</FLIGHT_ID>
                                            </LIST_BOUND>
                                            <LIST_TRAVELLER_DESCRIPTION>
                                                <TRAVELLER_TYPE>
                                                    <CODE>ADT</CODE>
                                                    <NAME>Adult</NAME>
                                                </TRAVELLER_TYPE>
                                                <BAGGAGE_ALLOWANCE>
                                                    <LIST_BOUND>
                                                        <LIST_SEGMENT>
                                                            <TYPED_BAGGAGE_ALLOWANCE>
                                                                <UNIT>PC</UNIT>
                                                                <VALUE>2.0</VALUE>
                                                            </TYPED_BAGGAGE_ALLOWANCE>
                                                            <LIST_FBD_INFO>
                                                                <FREE_TEXT>PIECE UP TO 23 KG</FREE_TEXT>
                                                                <NUMBER_OF_PIECES>2</NUMBER_OF_PIECES>
                                                                <LIST_ALLOWANCE_DESCRIPTOR>
                                                                    <TYPE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        WEIGHT
                                                                    </TYPE>
                                                                    <QUALIFIER
                                                                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        UP_TO
                                                                    </QUALIFIER>
                                                                    <VALUE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        23
                                                                    </VALUE>
                                                                    <UNIT xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        kg
                                                                    </UNIT>
                                                                </LIST_ALLOWANCE_DESCRIPTOR>
                                                                <LIST_ALLOWANCE_DESCRIPTOR>
                                                                    <TYPE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        WEIGHT
                                                                    </TYPE>
                                                                    <QUALIFIER
                                                                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        UP_TO
                                                                    </QUALIFIER>
                                                                    <VALUE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        50
                                                                    </VALUE>
                                                                    <UNIT xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        lb
                                                                    </UNIT>
                                                                </LIST_ALLOWANCE_DESCRIPTOR>
                                                                <LIST_ALLOWANCE_DESCRIPTOR>
                                                                    <TYPE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        LENGTH
                                                                    </TYPE>
                                                                    <QUALIFIER
                                                                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        UP_TO
                                                                    </QUALIFIER>
                                                                    <VALUE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        158
                                                                    </VALUE>
                                                                    <UNIT xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        lcm
                                                                    </UNIT>
                                                                </LIST_ALLOWANCE_DESCRIPTOR>
                                                                <LIST_ALLOWANCE_DESCRIPTOR>
                                                                    <TYPE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        LENGTH
                                                                    </TYPE>
                                                                    <QUALIFIER
                                                                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        UP_TO
                                                                    </QUALIFIER>
                                                                    <VALUE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        62
                                                                    </VALUE>
                                                                    <UNIT xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        li
                                                                    </UNIT>
                                                                </LIST_ALLOWANCE_DESCRIPTOR>
                                                            </LIST_FBD_INFO>
                                                        </LIST_SEGMENT>
                                                    </LIST_BOUND>
                                                </BAGGAGE_ALLOWANCE>
                                            </LIST_TRAVELLER_DESCRIPTION>
                                            <MINI_RULES_ID>1</MINI_RULES_ID>
                                        </LIST_COMBINATION_DESCRIPTION>
                                    </RECOMMENDATION_DESCRIPTION>
                                </LIST_RECOMMENDATION>
                                <LIST_RECOMMENDATION>
                                    <RECOMMENDATION_ID>7</RECOMMENDATION_ID>
                                    <LIST_BOUND>
                                        <LIST_FLIGHT>
                                            <FLIGHT_ID>11</FLIGHT_ID>
                                            <DISPLAY_LAST_SEATS>NO_NO_RULE</DISPLAY_LAST_SEATS>
                                            <NUMBER_OF_LAST_SEATS>1</NUMBER_OF_LAST_SEATS>
                                            <LSA_DEBUG_INFO>
                                                <FIRST_FLIGHT_NUMBER
                                                        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">8881
                                                </FIRST_FLIGHT_NUMBER>
                                                <RBD xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">T</RBD>
                                            </LSA_DEBUG_INFO>
                                        </LIST_FLIGHT>
                                    </LIST_BOUND>
                                    <FARE_FAMILY>
                                        <SHORT_NAME>FX</SHORT_NAME>
                                        <BRAND_NAME>Brand name not yet defined</BRAND_NAME>
                                        <COLOR>#E0EBEC</COLOR>
                                        <HIERARCHY>6500</HIERARCHY>
                                    </FARE_FAMILY>
                                    <LIST_TRIP_PRICE>
                                        <TOTAL_AMOUNT>2259.22</TOTAL_AMOUNT>
                                        <AMOUNT>2259.22</AMOUNT>
                                        <AMOUNT_WITHOUT_TAX>2078.00</AMOUNT_WITHOUT_TAX>
                                        <TAX>181.22</TAX>
                                        <EXCHANGE_RATE>1.00</EXCHANGE_RATE>
                                        <CURRENCY>
                                            <CODE>USD</CODE>
                                            <NAME>US Dollar</NAME>
                                        </CURRENCY>
                                        <LIST_DISPLAY_TAX>
                                            <CODE>YQ</CODE>
                                            <VALUE>146.0</VALUE>
                                        </LIST_DISPLAY_TAX>
                                        <LIST_DISPLAY_TAX>
                                            <CODE>US</CODE>
                                            <VALUE>19.7</VALUE>
                                        </LIST_DISPLAY_TAX>
                                    </LIST_TRIP_PRICE>
                                    <LIST_PNR>
                                        <LIST_TRAVELLER_TYPE>
                                            <LIST_TRAVELLER>
                                                <HAS_INFANT>false</HAS_INFANT>
                                                <IS_PRIMARY_TRAVELLER>true</IS_PRIMARY_TRAVELLER>
                                                <REQUESTED_TRAVELLER_TYPE>
                                                    <CODE>ADT</CODE>
                                                    <NAME>Adult</NAME>
                                                </REQUESTED_TRAVELLER_TYPE>
                                            </LIST_TRAVELLER>
                                            <TRAVELLER_TYPE>
                                                <CODE>ADT</CODE>
                                                <NAME>Adult</NAME>
                                            </TRAVELLER_TYPE>
                                            <NUMBER>1</NUMBER>
                                            <LIST_BOUND>
                                                <LIST_SEGMENT>
                                                    <SEGMENT_ID>0</SEGMENT_ID>
                                                    <FARE_CLASS>YIFTK</FARE_CLASS>
                                                    <RBD>T</RBD>
                                                    <CABIN>M</CABIN>
                                                    <FARE_FAMILY>
                                                        <SHORT_NAME>FX</SHORT_NAME>
                                                        <BRAND_NAME>Brand name not yet defined</BRAND_NAME>
                                                        <COLOR>#E0EBEC</COLOR>
                                                        <HIERARCHY>6500</HIERARCHY>
                                                    </FARE_FAMILY>
                                                    <LIST_PTC_APPLIED>
                                                        <CODE>ADT</CODE>
                                                    </LIST_PTC_APPLIED>
                                                    <LIST_FARE_TYPES>
                                                        <CODE>RV</CODE>
                                                        <NAME>Private</NAME>
                                                    </LIST_FARE_TYPES>
                                                </LIST_SEGMENT>
                                                <LIST_SEGMENT>
                                                    <SEGMENT_ID>1</SEGMENT_ID>
                                                    <FARE_CLASS>YIFTK</FARE_CLASS>
                                                    <RBD>Y</RBD>
                                                    <CABIN>M</CABIN>
                                                    <FARE_FAMILY>
                                                        <SHORT_NAME>RS</SHORT_NAME>
                                                        <BRAND_NAME>Brand name not yet defined</BRAND_NAME>
                                                        <COLOR>#E0EBEC</COLOR>
                                                        <HIERARCHY>6500</HIERARCHY>
                                                    </FARE_FAMILY>
                                                    <LIST_PTC_APPLIED>
                                                        <CODE>ADT</CODE>
                                                    </LIST_PTC_APPLIED>
                                                    <LIST_FARE_TYPES>
                                                        <CODE>RV</CODE>
                                                        <NAME>Private</NAME>
                                                    </LIST_FARE_TYPES>
                                                </LIST_SEGMENT>
                                            </LIST_BOUND>
                                            <LIST_TRAVELLER_PRICE>
                                                <TOTAL_AMOUNT>2259.22</TOTAL_AMOUNT>
                                                <AMOUNT>2259.22</AMOUNT>
                                                <AMOUNT_WITHOUT_TAX>2078.00</AMOUNT_WITHOUT_TAX>
                                                <TAX>181.22</TAX>
                                                <EXCHANGE_RATE>1.00</EXCHANGE_RATE>
                                                <CURRENCY>
                                                    <CODE>USD</CODE>
                                                    <NAME>US Dollar</NAME>
                                                </CURRENCY>
                                                <LIST_DISPLAY_TAX>
                                                    <CODE>YQ</CODE>
                                                    <VALUE>146.0</VALUE>
                                                </LIST_DISPLAY_TAX>
                                                <LIST_DISPLAY_TAX>
                                                    <CODE>US</CODE>
                                                    <VALUE>19.7</VALUE>
                                                </LIST_DISPLAY_TAX>
                                            </LIST_TRAVELLER_PRICE>
                                            <LIST_TRAVELLER_TYPE_PRICE>
                                                <TOTAL_AMOUNT>2259.22</TOTAL_AMOUNT>
                                                <AMOUNT>2259.22</AMOUNT>
                                                <AMOUNT_WITHOUT_TAX>2078.00</AMOUNT_WITHOUT_TAX>
                                                <TAX>181.22</TAX>
                                                <EXCHANGE_RATE>1.00</EXCHANGE_RATE>
                                                <CURRENCY>
                                                    <CODE>USD</CODE>
                                                    <NAME>US Dollar</NAME>
                                                </CURRENCY>
                                                <LIST_DISPLAY_TAX>
                                                    <CODE>YQ</CODE>
                                                    <VALUE>146.0</VALUE>
                                                </LIST_DISPLAY_TAX>
                                                <LIST_DISPLAY_TAX>
                                                    <CODE>US</CODE>
                                                    <VALUE>19.7</VALUE>
                                                </LIST_DISPLAY_TAX>
                                            </LIST_TRAVELLER_TYPE_PRICE>
                                        </LIST_TRAVELLER_TYPE>
                                        <LIST_PNR_PRICE>
                                            <TOTAL_AMOUNT>2259.22</TOTAL_AMOUNT>
                                            <AMOUNT>2259.22</AMOUNT>
                                            <AMOUNT_WITHOUT_TAX>2078.00</AMOUNT_WITHOUT_TAX>
                                            <TAX>181.22</TAX>
                                            <EXCHANGE_RATE>1.00</EXCHANGE_RATE>
                                            <CURRENCY>
                                                <CODE>USD</CODE>
                                                <NAME>US Dollar</NAME>
                                            </CURRENCY>
                                            <LIST_DISPLAY_TAX>
                                                <CODE>YQ</CODE>
                                                <VALUE>146.0</VALUE>
                                            </LIST_DISPLAY_TAX>
                                            <LIST_DISPLAY_TAX>
                                                <CODE>US</CODE>
                                                <VALUE>19.7</VALUE>
                                            </LIST_DISPLAY_TAX>
                                        </LIST_PNR_PRICE>
                                    </LIST_PNR>
                                    <RECOMMENDATION_DESCRIPTION>
                                        <LIST_COMBINATION_DESCRIPTION>
                                            <LIST_BOUND>
                                                <FLIGHT_ID>11</FLIGHT_ID>
                                            </LIST_BOUND>
                                            <LIST_TRAVELLER_DESCRIPTION>
                                                <TRAVELLER_TYPE>
                                                    <CODE>ADT</CODE>
                                                    <NAME>Adult</NAME>
                                                </TRAVELLER_TYPE>
                                                <BAGGAGE_ALLOWANCE>
                                                    <LIST_BOUND>
                                                        <LIST_SEGMENT>
                                                            <TYPED_BAGGAGE_ALLOWANCE>
                                                                <UNIT>PC</UNIT>
                                                                <VALUE>1.0</VALUE>
                                                            </TYPED_BAGGAGE_ALLOWANCE>
                                                            <LIST_FBD_INFO>
                                                                <FREE_TEXT>UPTO50LB 23KG AND62LI 158LCM</FREE_TEXT>
                                                                <NUMBER_OF_PIECES>1</NUMBER_OF_PIECES>
                                                                <LIST_ALLOWANCE_DESCRIPTOR>
                                                                    <TYPE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        WEIGHT
                                                                    </TYPE>
                                                                    <QUALIFIER
                                                                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        UP_TO
                                                                    </QUALIFIER>
                                                                    <VALUE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        23
                                                                    </VALUE>
                                                                    <UNIT xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        kg
                                                                    </UNIT>
                                                                </LIST_ALLOWANCE_DESCRIPTOR>
                                                                <LIST_ALLOWANCE_DESCRIPTOR>
                                                                    <TYPE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        WEIGHT
                                                                    </TYPE>
                                                                    <QUALIFIER
                                                                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        UP_TO
                                                                    </QUALIFIER>
                                                                    <VALUE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        50
                                                                    </VALUE>
                                                                    <UNIT xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        lb
                                                                    </UNIT>
                                                                </LIST_ALLOWANCE_DESCRIPTOR>
                                                                <LIST_ALLOWANCE_DESCRIPTOR>
                                                                    <TYPE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        LENGTH
                                                                    </TYPE>
                                                                    <QUALIFIER
                                                                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        UP_TO
                                                                    </QUALIFIER>
                                                                    <VALUE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        158
                                                                    </VALUE>
                                                                    <UNIT xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        lcm
                                                                    </UNIT>
                                                                </LIST_ALLOWANCE_DESCRIPTOR>
                                                                <LIST_ALLOWANCE_DESCRIPTOR>
                                                                    <TYPE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        LENGTH
                                                                    </TYPE>
                                                                    <QUALIFIER
                                                                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        UP_TO
                                                                    </QUALIFIER>
                                                                    <VALUE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        62
                                                                    </VALUE>
                                                                    <UNIT xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        li
                                                                    </UNIT>
                                                                </LIST_ALLOWANCE_DESCRIPTOR>
                                                            </LIST_FBD_INFO>
                                                        </LIST_SEGMENT>
                                                        <LIST_SEGMENT>
                                                            <TYPED_BAGGAGE_ALLOWANCE>
                                                                <UNIT>PC</UNIT>
                                                                <VALUE>1.0</VALUE>
                                                            </TYPED_BAGGAGE_ALLOWANCE>
                                                            <LIST_FBD_INFO>
                                                                <FREE_TEXT>UPTO50LB 23KG AND62LI 158LCM</FREE_TEXT>
                                                                <NUMBER_OF_PIECES>1</NUMBER_OF_PIECES>
                                                                <LIST_ALLOWANCE_DESCRIPTOR>
                                                                    <TYPE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        WEIGHT
                                                                    </TYPE>
                                                                    <QUALIFIER
                                                                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        UP_TO
                                                                    </QUALIFIER>
                                                                    <VALUE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        23
                                                                    </VALUE>
                                                                    <UNIT xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        kg
                                                                    </UNIT>
                                                                </LIST_ALLOWANCE_DESCRIPTOR>
                                                                <LIST_ALLOWANCE_DESCRIPTOR>
                                                                    <TYPE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        WEIGHT
                                                                    </TYPE>
                                                                    <QUALIFIER
                                                                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        UP_TO
                                                                    </QUALIFIER>
                                                                    <VALUE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        50
                                                                    </VALUE>
                                                                    <UNIT xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        lb
                                                                    </UNIT>
                                                                </LIST_ALLOWANCE_DESCRIPTOR>
                                                                <LIST_ALLOWANCE_DESCRIPTOR>
                                                                    <TYPE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        LENGTH
                                                                    </TYPE>
                                                                    <QUALIFIER
                                                                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        UP_TO
                                                                    </QUALIFIER>
                                                                    <VALUE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        158
                                                                    </VALUE>
                                                                    <UNIT xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        lcm
                                                                    </UNIT>
                                                                </LIST_ALLOWANCE_DESCRIPTOR>
                                                                <LIST_ALLOWANCE_DESCRIPTOR>
                                                                    <TYPE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        LENGTH
                                                                    </TYPE>
                                                                    <QUALIFIER
                                                                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        UP_TO
                                                                    </QUALIFIER>
                                                                    <VALUE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        62
                                                                    </VALUE>
                                                                    <UNIT xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                                        li
                                                                    </UNIT>
                                                                </LIST_ALLOWANCE_DESCRIPTOR>
                                                            </LIST_FBD_INFO>
                                                        </LIST_SEGMENT>
                                                    </LIST_BOUND>
                                                </BAGGAGE_ALLOWANCE>
                                            </LIST_TRAVELLER_DESCRIPTION>
                                            <MINI_RULES_ID>2</MINI_RULES_ID>
                                        </LIST_COMBINATION_DESCRIPTION>
                                    </RECOMMENDATION_DESCRIPTION>
                                </LIST_RECOMMENDATION>
                                <LIST_PROPOSED_BOUND>
                                    <LIST_FLIGHT>
                                        <FLIGHT_ID>0</FLIGHT_ID>
                                        <LIST_SEGMENT>
                                            <SEGMENT_ID>0</SEGMENT_ID>
                                            <B_LOCATION>
                                                <CITY_CODE>NYC</CITY_CODE>
                                                <CITY_NAME>New York</CITY_NAME>
                                                <STATE_CODE>NY</STATE_CODE>
                                                <STATE_NAME>New York</STATE_NAME>
                                                <COUNTRY_CODE>US</COUNTRY_CODE>
                                                <COUNTRY_NAME>United States Of America</COUNTRY_NAME>
                                                <LOCATION_CODE>JFK</LOCATION_CODE>
                                                <LOCATION_NAME>John F. Kennedy International</LOCATION_NAME>
                                            </B_LOCATION>
                                            <E_LOCATION>
                                                <CITY_CODE>IST</CITY_CODE>
                                                <CITY_NAME>Istanbul</CITY_NAME>
                                                <COUNTRY_CODE>TR</COUNTRY_CODE>
                                                <COUNTRY_NAME>Turkey</COUNTRY_NAME>
                                                <LOCATION_CODE>IST</LOCATION_CODE>
                                                <LOCATION_NAME>Istanbul Airport</LOCATION_NAME>
                                            </E_LOCATION>
                                            <AIRLINE>
                                                <CODE>TK</CODE>
                                                <NAME>Turkish Airlines</NAME>
                                            </AIRLINE>
                                            <FLIGHT_NUMBER>4</FLIGHT_NUMBER>
                                            <B_DATE code="2022-05-24T12:45:00">Tuesday, May 24, 2022</B_DATE>
                                            <E_DATE code="2022-05-25T05:20:00">Wednesday, May 25, 2022</E_DATE>
                                            <B_TERMINAL>1</B_TERMINAL>
                                            <E_DAY_INDICATOR>1</E_DAY_INDICATOR>
                                            <E_TICKETING>true</E_TICKETING>
                                            <NUMBER_OF_STOPS>0</NUMBER_OF_STOPS>
                                            <SEGMENT_FLIGHT_TIME>34500000</SEGMENT_FLIGHT_TIME>
                                            <EQUIPMENT>
                                                <CODE>77W</CODE>
                                                <NAME>Boeing 777-300ER</NAME>
                                            </EQUIPMENT>
                                            <ELAPSED_FLYING_TIME>34500000</ELAPSED_FLYING_TIME>
                                        </LIST_SEGMENT>
                                    </LIST_FLIGHT>
                                    <LIST_FLIGHT>
                                        <FLIGHT_ID>11</FLIGHT_ID>
                                        <LIST_SEGMENT>
                                            <SEGMENT_ID>18</SEGMENT_ID>
                                            <B_LOCATION>
                                                <CITY_CODE>NYC</CITY_CODE>
                                                <CITY_NAME>New York</CITY_NAME>
                                                <STATE_CODE>NJ</STATE_CODE>
                                                <STATE_NAME>New Jersey</STATE_NAME>
                                                <COUNTRY_CODE>US</COUNTRY_CODE>
                                                <COUNTRY_NAME>United States Of America</COUNTRY_NAME>
                                                <LOCATION_CODE>EWR</LOCATION_CODE>
                                                <LOCATION_NAME>Newark Liberty International</LOCATION_NAME>
                                            </B_LOCATION>
                                            <E_LOCATION>
                                                <CITY_CODE>YTO</CITY_CODE>
                                                <CITY_NAME>Toronto</CITY_NAME>
                                                <STATE_CODE>ON</STATE_CODE>
                                                <STATE_NAME>Ontario</STATE_NAME>
                                                <COUNTRY_CODE>CA</COUNTRY_CODE>
                                                <COUNTRY_NAME>Canada</COUNTRY_NAME>
                                                <LOCATION_CODE>YYZ</LOCATION_CODE>
                                                <LOCATION_NAME>Lester B. Pearson International</LOCATION_NAME>
                                            </E_LOCATION>
                                            <AIRLINE>
                                                <CODE>AC</CODE>
                                                <NAME>Air Canada</NAME>
                                            </AIRLINE>
                                            <OTHER_AIRLINE>
                                                <NAME>AIR CANADA EXPRESS - JAZZ</NAME>
                                            </OTHER_AIRLINE>
                                            <FLIGHT_NUMBER>8881</FLIGHT_NUMBER>
                                            <B_DATE code="2022-05-24T14:50:00">Tuesday, May 24, 2022</B_DATE>
                                            <E_DATE code="2022-05-24T16:25:00">Tuesday, May 24, 2022</E_DATE>
                                            <B_TERMINAL>A</B_TERMINAL>
                                            <E_TERMINAL>1</E_TERMINAL>
                                            <OP_FLAG>true</OP_FLAG>
                                            <E_TICKETING>true</E_TICKETING>
                                            <NUMBER_OF_STOPS>0</NUMBER_OF_STOPS>
                                            <SEGMENT_FLIGHT_TIME>5700000</SEGMENT_FLIGHT_TIME>
                                            <EQUIPMENT>
                                                <CODE>E75</CODE>
                                                <NAME>Embraer 175</NAME>
                                            </EQUIPMENT>
                                        </LIST_SEGMENT>
                                        <LIST_SEGMENT>
                                            <SEGMENT_ID>19</SEGMENT_ID>
                                            <B_LOCATION>
                                                <CITY_CODE>YTO</CITY_CODE>
                                                <CITY_NAME>Toronto</CITY_NAME>
                                                <STATE_CODE>ON</STATE_CODE>
                                                <STATE_NAME>Ontario</STATE_NAME>
                                                <COUNTRY_CODE>CA</COUNTRY_CODE>
                                                <COUNTRY_NAME>Canada</COUNTRY_NAME>
                                                <LOCATION_CODE>YYZ</LOCATION_CODE>
                                                <LOCATION_NAME>Lester B. Pearson International</LOCATION_NAME>
                                            </B_LOCATION>
                                            <E_LOCATION>
                                                <CITY_CODE>IST</CITY_CODE>
                                                <CITY_NAME>Istanbul</CITY_NAME>
                                                <COUNTRY_CODE>TR</COUNTRY_CODE>
                                                <COUNTRY_NAME>Turkey</COUNTRY_NAME>
                                                <LOCATION_CODE>IST</LOCATION_CODE>
                                                <LOCATION_NAME>Istanbul Airport</LOCATION_NAME>
                                            </E_LOCATION>
                                            <AIRLINE>
                                                <CODE>TK</CODE>
                                                <NAME>Turkish Airlines</NAME>
                                            </AIRLINE>
                                            <FLIGHT_NUMBER>18</FLIGHT_NUMBER>
                                            <B_DATE code="2022-05-24T22:20:00">Tuesday, May 24, 2022</B_DATE>
                                            <E_DATE code="2022-05-25T15:10:00">Wednesday, May 25, 2022</E_DATE>
                                            <B_TERMINAL>1</B_TERMINAL>
                                            <E_DAY_INDICATOR>1</E_DAY_INDICATOR>
                                            <E_TICKETING>true</E_TICKETING>
                                            <NUMBER_OF_STOPS>0</NUMBER_OF_STOPS>
                                            <SEGMENT_FLIGHT_TIME>35400000</SEGMENT_FLIGHT_TIME>
                                            <EQUIPMENT>
                                                <CODE>77W</CODE>
                                                <NAME>Boeing 777-300ER</NAME>
                                            </EQUIPMENT>
                                            <ELAPSED_FLYING_TIME>62400000</ELAPSED_FLYING_TIME>
                                        </LIST_SEGMENT>
                                    </LIST_FLIGHT>
                                </LIST_PROPOSED_BOUND>
                            </LIST_TAB>
                            <SEARCH_DATA>
                                <LIST_DESTINATION>
                                    <B_LOCATION>
                                        <CITY_CODE>NYC</CITY_CODE>
                                        <CITY_NAME>New York</CITY_NAME>
                                        <STATE_CODE>NY</STATE_CODE>
                                        <STATE_NAME>New York</STATE_NAME>
                                        <COUNTRY_CODE>US</COUNTRY_CODE>
                                        <COUNTRY_NAME>United States Of America</COUNTRY_NAME>
                                        <LOCATION_CODE>NYC</LOCATION_CODE>
                                        <LOCATION_NAME>New York</LOCATION_NAME>
                                    </B_LOCATION>
                                    <E_LOCATION>
                                        <CITY_CODE>IST</CITY_CODE>
                                        <CITY_NAME>Istanbul</CITY_NAME>
                                        <COUNTRY_CODE>TR</COUNTRY_CODE>
                                        <COUNTRY_NAME>Turkey</COUNTRY_NAME>
                                        <LOCATION_CODE>IST</LOCATION_CODE>
                                        <LOCATION_NAME>Istanbul Airport</LOCATION_NAME>
                                    </E_LOCATION>
                                    <B_DATE code="2022-05-24T00:00:00">Tuesday, May 24, 2022</B_DATE>
                                    <B_TIME_WINDOW>0</B_TIME_WINDOW>
                                </LIST_DESTINATION>
                                <TRIP_TYPE>O</TRIP_TYPE>
                                <DISPLAY_TYPE>2</DISPLAY_TYPE>
                                <PRICING_TYPE>O</PRICING_TYPE>
                                <LIST_COMMERCIAL_FARE_FAMILY>
                                    <CODE>ECOB</CODE>
                                    <PANEL_TYPE>A</PANEL_TYPE>
                                    <SET_INDEX>0</SET_INDEX>
                                </LIST_COMMERCIAL_FARE_FAMILY>
                            </SEARCH_DATA>
                            <TYPE>FP</TYPE>
                            <IS_LOADED_FROM_CACHE>false</IS_LOADED_FROM_CACHE>
                            <MINI_RULES_DICTIONARY>
                                <LIST_MNR_DETAILS>
                                    <MNR_REF>
                                        <NUMBER>1</NUMBER>
                                    </MNR_REF>
                                    <CAT_GRP>
                                        <LIST_CAT_GROUP>
                                            <CATINFO>
                                                <NUMBER>31</NUMBER>
                                            </CATINFO>
                                            <MONETARY_INFO>
                                                <MONETARY_DETAILS>
                                                    <TYPE_QUALIFIER>BDC</TYPE_QUALIFIER>
                                                    <AMOUNT>0.00</AMOUNT>
                                                </MONETARY_DETAILS>
                                                <OTHER_MONETARY_DETAILS>
                                                    <TYPE_QUALIFIER>BDT</TYPE_QUALIFIER>
                                                    <AMOUNT>0.00</AMOUNT>
                                                </OTHER_MONETARY_DETAILS>
                                            </MONETARY_INFO>
                                            <STATUS_INFO>
                                                <LIST_STATUS_INFORMATION>
                                                    <INDICATOR>FFT</INDICATOR>
                                                    <ACTION>1</ACTION>
                                                </LIST_STATUS_INFORMATION>
                                                <LIST_STATUS_INFORMATION>
                                                    <INDICATOR>RVJ</INDICATOR>
                                                    <ACTION>1</ACTION>
                                                </LIST_STATUS_INFORMATION>
                                                <LIST_STATUS_INFORMATION>
                                                    <INDICATOR>BDJ</INDICATOR>
                                                    <ACTION>1</ACTION>
                                                </LIST_STATUS_INFORMATION>
                                                <LIST_STATUS_INFORMATION>
                                                    <INDICATOR>GUA</INDICATOR>
                                                    <ACTION>0</ACTION>
                                                </LIST_STATUS_INFORMATION>
                                            </STATUS_INFO>
                                        </LIST_CAT_GROUP>
                                        <LIST_CAT_GROUP>
                                            <CATINFO>
                                                <NUMBER>33</NUMBER>
                                            </CATINFO>
                                            <MONETARY_INFO>
                                                <MONETARY_DETAILS>
                                                    <TYPE_QUALIFIER>BDT</TYPE_QUALIFIER>
                                                    <AMOUNT>0.00</AMOUNT>
                                                </MONETARY_DETAILS>
                                            </MONETARY_INFO>
                                            <STATUS_INFO>
                                                <LIST_STATUS_INFORMATION>
                                                    <INDICATOR>FFT</INDICATOR>
                                                    <ACTION>1</ACTION>
                                                </LIST_STATUS_INFORMATION>
                                                <LIST_STATUS_INFORMATION>
                                                    <INDICATOR>BDJ</INDICATOR>
                                                    <ACTION>1</ACTION>
                                                </LIST_STATUS_INFORMATION>
                                                <LIST_STATUS_INFORMATION>
                                                    <INDICATOR>GUA</INDICATOR>
                                                    <ACTION>0</ACTION>
                                                </LIST_STATUS_INFORMATION>
                                            </STATUS_INFO>
                                        </LIST_CAT_GROUP>
                                    </CAT_GRP>
                                </LIST_MNR_DETAILS>
                                <LIST_MNR_DETAILS>
                                    <MNR_REF>
                                        <NUMBER>2</NUMBER>
                                    </MNR_REF>
                                    <CAT_GRP>
                                        <LIST_CAT_GROUP>
                                            <CATINFO>
                                                <NUMBER>31</NUMBER>
                                            </CATINFO>
                                            <MONETARY_INFO>
                                                <MONETARY_DETAILS>
                                                    <TYPE_QUALIFIER>BDC</TYPE_QUALIFIER>
                                                    <AMOUNT>0.00</AMOUNT>
                                                </MONETARY_DETAILS>
                                                <OTHER_MONETARY_DETAILS>
                                                    <TYPE_QUALIFIER>BDT</TYPE_QUALIFIER>
                                                    <AMOUNT>0.00</AMOUNT>
                                                </OTHER_MONETARY_DETAILS>
                                            </MONETARY_INFO>
                                            <STATUS_INFO>
                                                <LIST_STATUS_INFORMATION>
                                                    <INDICATOR>RVJ</INDICATOR>
                                                    <ACTION>1</ACTION>
                                                </LIST_STATUS_INFORMATION>
                                                <LIST_STATUS_INFORMATION>
                                                    <INDICATOR>BDJ</INDICATOR>
                                                    <ACTION>1</ACTION>
                                                </LIST_STATUS_INFORMATION>
                                                <LIST_STATUS_INFORMATION>
                                                    <INDICATOR>GUA</INDICATOR>
                                                    <ACTION>0</ACTION>
                                                </LIST_STATUS_INFORMATION>
                                            </STATUS_INFO>
                                        </LIST_CAT_GROUP>
                                        <LIST_CAT_GROUP>
                                            <CATINFO>
                                                <NUMBER>33</NUMBER>
                                            </CATINFO>
                                            <MONETARY_INFO>
                                                <MONETARY_DETAILS>
                                                    <TYPE_QUALIFIER>BDT</TYPE_QUALIFIER>
                                                    <AMOUNT>0.00</AMOUNT>
                                                </MONETARY_DETAILS>
                                            </MONETARY_INFO>
                                            <STATUS_INFO>
                                                <LIST_STATUS_INFORMATION>
                                                    <INDICATOR>BDJ</INDICATOR>
                                                    <ACTION>1</ACTION>
                                                </LIST_STATUS_INFORMATION>
                                                <LIST_STATUS_INFORMATION>
                                                    <INDICATOR>GUA</INDICATOR>
                                                    <ACTION>0</ACTION>
                                                </LIST_STATUS_INFORMATION>
                                            </STATUS_INFO>
                                        </LIST_CAT_GROUP>
                                    </CAT_GRP>
                                </LIST_MNR_DETAILS>
                            </MINI_RULES_DICTIONARY>
                            <MINIRULES_TYPE_QUALIFIER_DICTIONARY>
                                <LIST_MONETARY_DETAILS>
                                    <NAME>FFT</NAME>
                                    <DESCRIPTION>Check full fare conditions for additional information</DESCRIPTION>
                                </LIST_MONETARY_DETAILS>
                                <LIST_MONETARY_DETAILS>
                                    <NAME>BDC</NAME>
                                    <DESCRIPTION>Maximum Revalidation penalty fee for entire ticket ^DATA(BDC)
                                    </DESCRIPTION>
                                </LIST_MONETARY_DETAILS>
                                <LIST_MONETARY_DETAILS>
                                    <NAME>BDT</NAME>
                                    <DESCRIPTION>Maximum Refund penalty fee for entire ticket: ^DATA(BDT)</DESCRIPTION>
                                </LIST_MONETARY_DETAILS>
                            </MINIRULES_TYPE_QUALIFIER_DICTIONARY>
                        </LIST_PANEL>
                        <TEMPLATE xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">SITE_JSP_FLEX_PRICER_AVL
                        </TEMPLATE>
                        <LIST_AVAILABILITY_RANGE>
                            <MIN_AVAIL_DATE code="2022-05-24T01:26:00">Tuesday, May 24, 2022</MIN_AVAIL_DATE>
                            <MAX_AVAIL_DATE code="2023-05-13T23:26:00">Saturday, May 13, 2023</MAX_AVAIL_DATE>
                            <LOCATION>NYC</LOCATION>
                        </LIST_AVAILABILITY_RANGE>
                        <FARE_FAMILY_DICTIONARY>
                            <LIST_FARE_FAMILY>
                                <FARE_FAMILY>FX</FARE_FAMILY>
                                <COMMERCIAL_FARE_FAMILY>ECOB</COMMERCIAL_FARE_FAMILY>
                                <LIST_SERVICE>
                                    <SERVICE_STATUS>INC</SERVICE_STATUS>
                                    <SERVICE_REFERENCE>3</SERVICE_REFERENCE>
                                </LIST_SERVICE>
                                <LIST_SERVICE>
                                    <SERVICE_STATUS>INC</SERVICE_STATUS>
                                    <SERVICE_REFERENCE>4</SERVICE_REFERENCE>
                                </LIST_SERVICE>
                                <LIST_SERVICE>
                                    <SERVICE_STATUS>INC</SERVICE_STATUS>
                                    <SERVICE_REFERENCE>5</SERVICE_REFERENCE>
                                </LIST_SERVICE>
                                <LIST_SERVICE>
                                    <SERVICE_STATUS>INC</SERVICE_STATUS>
                                    <SERVICE_REFERENCE>6</SERVICE_REFERENCE>
                                </LIST_SERVICE>
                                <LIST_SERVICE>
                                    <SERVICE_STATUS>INC</SERVICE_STATUS>
                                    <SERVICE_REFERENCE>7</SERVICE_REFERENCE>
                                </LIST_SERVICE>
                            </LIST_FARE_FAMILY>
                        </FARE_FAMILY_DICTIONARY>
                        <LIST_SERVICE_FEES_GROUP>
                            <LIST_SERVICE_DETAILS_GROUP>
                                <SERVICE_OPTION_INFO>
                                    <DATA_TYPE_INFORMATION>
                                        <SUBTYPE>0GO</SUBTYPE>
                                    </DATA_TYPE_INFORMATION>
                                </SERVICE_OPTION_INFO>
                                <FEE_DESCRIPTION_GROUP>
                                    <SERVICE_ATTRIBUTES_INFO>
                                        <ATTRIBUTE_DETAILS>
                                            <ATTRIBUTE_TYPE>BKM</ATTRIBUTE_TYPE>
                                            <ATTRIBUTE_DESCRIPTION>01</ATTRIBUTE_DESCRIPTION>
                                        </ATTRIBUTE_DETAILS>
                                        <ATTRIBUTE_DETAILS>
                                            <ATTRIBUTE_TYPE>RFIC</ATTRIBUTE_TYPE>
                                            <ATTRIBUTE_DESCRIPTION>C</ATTRIBUTE_DESCRIPTION>
                                        </ATTRIBUTE_DETAILS>
                                        <ATTRIBUTE_DETAILS>
                                            <ATTRIBUTE_TYPE>SSR</ATTRIBUTE_TYPE>
                                            <ATTRIBUTE_DESCRIPTION>RFIC</ATTRIBUTE_DESCRIPTION>
                                        </ATTRIBUTE_DETAILS>
                                    </SERVICE_ATTRIBUTES_INFO>
                                    <COMMERCIAL_NAME>
                                        <FREE_TEXT_QUALIFICATION>
                                            <TEXT_SUBJECT_QUALIFIER>3</TEXT_SUBJECT_QUALIFIER>
                                        </FREE_TEXT_QUALIFICATION>
                                        <FREE_TEXT>PIECE UP TO 23 KG</FREE_TEXT>
                                    </COMMERCIAL_NAME>
                                    <SERVICE_DESCRIPTION_INFO>
                                        <SERVICE_REQUIREMENTS_INFO>
                                            <SERVICE_FREE_TEXT>23</SERVICE_FREE_TEXT>
                                            <SERVICE_GROUP>BG</SERVICE_GROUP>
                                            <SERVICE_MARKETING_CARRIER>TK</SERVICE_MARKETING_CARRIER>
                                            <SERVICE_NUMBER_OF_INSTANCES>1</SERVICE_NUMBER_OF_INSTANCES>
                                            <SERVICE_CLASSIFICATION>C</SERVICE_CLASSIFICATION>
                                        </SERVICE_REQUIREMENTS_INFO>
                                    </SERVICE_DESCRIPTION_INFO>
                                    <ITEM_NUMBER_INFO>
                                        <ITEM_NUMBER_DETAILS>
                                            <TYPE>SD</TYPE>
                                            <NUMBER>1</NUMBER>
                                        </ITEM_NUMBER_DETAILS>
                                    </ITEM_NUMBER_INFO>
                                </FEE_DESCRIPTION_GROUP>
                            </LIST_SERVICE_DETAILS_GROUP>
                            <LIST_SERVICE_DETAILS_GROUP>
                                <SERVICE_OPTION_INFO>
                                    <DATA_TYPE_INFORMATION>
                                        <SUBTYPE>0GO</SUBTYPE>
                                    </DATA_TYPE_INFORMATION>
                                </SERVICE_OPTION_INFO>
                                <FEE_DESCRIPTION_GROUP>
                                    <SERVICE_ATTRIBUTES_INFO>
                                        <ATTRIBUTE_DETAILS>
                                            <ATTRIBUTE_TYPE>RFIC</ATTRIBUTE_TYPE>
                                            <ATTRIBUTE_DESCRIPTION>C</ATTRIBUTE_DESCRIPTION>
                                        </ATTRIBUTE_DETAILS>
                                        <ATTRIBUTE_DETAILS>
                                            <ATTRIBUTE_TYPE>SSR</ATTRIBUTE_TYPE>
                                            <ATTRIBUTE_DESCRIPTION>XBAG</ATTRIBUTE_DESCRIPTION>
                                        </ATTRIBUTE_DETAILS>
                                    </SERVICE_ATTRIBUTES_INFO>
                                    <COMMERCIAL_NAME>
                                        <FREE_TEXT_QUALIFICATION>
                                            <TEXT_SUBJECT_QUALIFIER>3</TEXT_SUBJECT_QUALIFIER>
                                        </FREE_TEXT_QUALIFICATION>
                                        <FREE_TEXT>UPTO50LB 23KG AND62LI 158LCM</FREE_TEXT>
                                    </COMMERCIAL_NAME>
                                    <SERVICE_DESCRIPTION_INFO>
                                        <SERVICE_REQUIREMENTS_INFO>
                                            <SERVICE_FREE_TEXT>23</SERVICE_FREE_TEXT>
                                            <SERVICE_GROUP>BG</SERVICE_GROUP>
                                            <SERVICE_MARKETING_CARRIER>AC</SERVICE_MARKETING_CARRIER>
                                            <SERVICE_NUMBER_OF_INSTANCES>1</SERVICE_NUMBER_OF_INSTANCES>
                                            <SERVICE_CLASSIFICATION>C</SERVICE_CLASSIFICATION>
                                        </SERVICE_REQUIREMENTS_INFO>
                                    </SERVICE_DESCRIPTION_INFO>
                                    <ITEM_NUMBER_INFO>
                                        <ITEM_NUMBER_DETAILS>
                                            <TYPE>SD</TYPE>
                                            <NUMBER>2</NUMBER>
                                        </ITEM_NUMBER_DETAILS>
                                    </ITEM_NUMBER_INFO>
                                </FEE_DESCRIPTION_GROUP>
                            </LIST_SERVICE_DETAILS_GROUP>
                            <LIST_SERVICE_DETAILS_GROUP>
                                <SERVICE_OPTION_INFO>
                                    <DATA_TYPE_INFORMATION>
                                        <SUBTYPE>09Q</SUBTYPE>
                                    </DATA_TYPE_INFORMATION>
                                </SERVICE_OPTION_INFO>
                                <FEE_DESCRIPTION_GROUP>
                                    <SERVICE_ATTRIBUTES_INFO>
                                        <ATTRIBUTE_DETAILS>
                                            <ATTRIBUTE_TYPE>RFIC</ATTRIBUTE_TYPE>
                                            <ATTRIBUTE_DESCRIPTION>C</ATTRIBUTE_DESCRIPTION>
                                        </ATTRIBUTE_DETAILS>
                                        <ATTRIBUTE_DETAILS>
                                            <ATTRIBUTE_TYPE>SSR</ATTRIBUTE_TYPE>
                                            <ATTRIBUTE_DESCRIPTION>RFIC</ATTRIBUTE_DESCRIPTION>
                                        </ATTRIBUTE_DETAILS>
                                    </SERVICE_ATTRIBUTES_INFO>
                                    <COMMERCIAL_NAME>
                                        <FREE_TEXT_QUALIFICATION>
                                            <TEXT_SUBJECT_QUALIFIER>3</TEXT_SUBJECT_QUALIFIER>
                                        </FREE_TEXT_QUALIFICATION>
                                        <FREE_TEXT>1 PIECE X 8 KG CABIN BAGGAGE</FREE_TEXT>
                                    </COMMERCIAL_NAME>
                                    <SERVICE_DESCRIPTION_INFO>
                                        <SERVICE_REQUIREMENTS_INFO>
                                            <SERVICE_FREE_TEXT>4Y</SERVICE_FREE_TEXT>
                                            <SERVICE_SUB_GROUP>CY</SERVICE_SUB_GROUP>
                                            <SERVICE_GROUP>BG</SERVICE_GROUP>
                                            <SERVICE_MARKETING_CARRIER>TK</SERVICE_MARKETING_CARRIER>
                                            <SERVICE_CLASSIFICATION>C</SERVICE_CLASSIFICATION>
                                        </SERVICE_REQUIREMENTS_INFO>
                                    </SERVICE_DESCRIPTION_INFO>
                                    <ITEM_NUMBER_INFO>
                                        <ITEM_NUMBER_DETAILS>
                                            <TYPE>SD</TYPE>
                                            <NUMBER>3</NUMBER>
                                        </ITEM_NUMBER_DETAILS>
                                    </ITEM_NUMBER_INFO>
                                </FEE_DESCRIPTION_GROUP>
                            </LIST_SERVICE_DETAILS_GROUP>
                            <LIST_SERVICE_DETAILS_GROUP>
                                <SERVICE_OPTION_INFO>
                                    <DATA_TYPE_INFORMATION>
                                        <SUBTYPE>0AK</SUBTYPE>
                                    </DATA_TYPE_INFORMATION>
                                </SERVICE_OPTION_INFO>
                                <FEE_DESCRIPTION_GROUP>
                                    <COMMERCIAL_NAME>
                                        <FREE_TEXT_QUALIFICATION>
                                            <TEXT_SUBJECT_QUALIFIER>3</TEXT_SUBJECT_QUALIFIER>
                                        </FREE_TEXT_QUALIFICATION>
                                        <FREE_TEXT>MEAL SERVICE</FREE_TEXT>
                                    </COMMERCIAL_NAME>
                                    <SERVICE_DESCRIPTION_INFO>
                                        <SERVICE_REQUIREMENTS_INFO>
                                            <SERVICE_SUB_GROUP>DI</SERVICE_SUB_GROUP>
                                            <SERVICE_GROUP>ML</SERVICE_GROUP>
                                            <SERVICE_MARKETING_CARRIER>TK</SERVICE_MARKETING_CARRIER>
                                            <SERVICE_CLASSIFICATION>F</SERVICE_CLASSIFICATION>
                                        </SERVICE_REQUIREMENTS_INFO>
                                    </SERVICE_DESCRIPTION_INFO>
                                    <ITEM_NUMBER_INFO>
                                        <ITEM_NUMBER_DETAILS>
                                            <TYPE>SD</TYPE>
                                            <NUMBER>4</NUMBER>
                                        </ITEM_NUMBER_DETAILS>
                                    </ITEM_NUMBER_INFO>
                                </FEE_DESCRIPTION_GROUP>
                            </LIST_SERVICE_DETAILS_GROUP>
                            <LIST_SERVICE_DETAILS_GROUP>
                                <SERVICE_OPTION_INFO>
                                    <DATA_TYPE_INFORMATION>
                                        <SUBTYPE>BAG</SUBTYPE>
                                    </DATA_TYPE_INFORMATION>
                                </SERVICE_OPTION_INFO>
                                <FEE_DESCRIPTION_GROUP>
                                    <SERVICE_ATTRIBUTES_INFO>
                                        <ATTRIBUTE_DETAILS>
                                            <ATTRIBUTE_TYPE>BKM</ATTRIBUTE_TYPE>
                                            <ATTRIBUTE_DESCRIPTION>01</ATTRIBUTE_DESCRIPTION>
                                        </ATTRIBUTE_DETAILS>
                                        <ATTRIBUTE_DETAILS>
                                            <ATTRIBUTE_TYPE>RFIC</ATTRIBUTE_TYPE>
                                            <ATTRIBUTE_DESCRIPTION>C</ATTRIBUTE_DESCRIPTION>
                                        </ATTRIBUTE_DETAILS>
                                        <ATTRIBUTE_DETAILS>
                                            <ATTRIBUTE_TYPE>SSR</ATTRIBUTE_TYPE>
                                            <ATTRIBUTE_DESCRIPTION>RFIC</ATTRIBUTE_DESCRIPTION>
                                        </ATTRIBUTE_DETAILS>
                                    </SERVICE_ATTRIBUTES_INFO>
                                    <COMMERCIAL_NAME>
                                        <FREE_TEXT_QUALIFICATION>
                                            <TEXT_SUBJECT_QUALIFIER>3</TEXT_SUBJECT_QUALIFIER>
                                        </FREE_TEXT_QUALIFICATION>
                                        <FREE_TEXT>BAG INCLUDED</FREE_TEXT>
                                    </COMMERCIAL_NAME>
                                    <SERVICE_DESCRIPTION_INFO>
                                        <SERVICE_REQUIREMENTS_INFO>
                                            <SERVICE_GROUP>BG</SERVICE_GROUP>
                                            <SERVICE_MARKETING_CARRIER>TK</SERVICE_MARKETING_CARRIER>
                                            <SERVICE_CLASSIFICATION>C</SERVICE_CLASSIFICATION>
                                        </SERVICE_REQUIREMENTS_INFO>
                                    </SERVICE_DESCRIPTION_INFO>
                                    <ITEM_NUMBER_INFO>
                                        <ITEM_NUMBER_DETAILS>
                                            <TYPE>SD</TYPE>
                                            <NUMBER>5</NUMBER>
                                        </ITEM_NUMBER_DETAILS>
                                    </ITEM_NUMBER_INFO>
                                </FEE_DESCRIPTION_GROUP>
                            </LIST_SERVICE_DETAILS_GROUP>
                            <LIST_SERVICE_DETAILS_GROUP>
                                <SERVICE_OPTION_INFO>
                                    <DATA_TYPE_INFORMATION>
                                        <SUBTYPE>059</SUBTYPE>
                                    </DATA_TYPE_INFORMATION>
                                </SERVICE_OPTION_INFO>
                                <FEE_DESCRIPTION_GROUP>
                                    <COMMERCIAL_NAME>
                                        <FREE_TEXT_QUALIFICATION>
                                            <TEXT_SUBJECT_QUALIFIER>3</TEXT_SUBJECT_QUALIFIER>
                                        </FREE_TEXT_QUALIFICATION>
                                        <FREE_TEXT>CHANGEABLE TICKET</FREE_TEXT>
                                    </COMMERCIAL_NAME>
                                    <SERVICE_DESCRIPTION_INFO>
                                        <SERVICE_REQUIREMENTS_INFO>
                                            <SERVICE_FREE_TEXT>CD</SERVICE_FREE_TEXT>
                                            <SERVICE_SUB_GROUP>VC</SERVICE_SUB_GROUP>
                                            <SERVICE_GROUP>BF</SERVICE_GROUP>
                                            <SERVICE_MARKETING_CARRIER>TK</SERVICE_MARKETING_CARRIER>
                                            <SERVICE_CLASSIFICATION>Z</SERVICE_CLASSIFICATION>
                                        </SERVICE_REQUIREMENTS_INFO>
                                    </SERVICE_DESCRIPTION_INFO>
                                    <ITEM_NUMBER_INFO>
                                        <ITEM_NUMBER_DETAILS>
                                            <TYPE>SD</TYPE>
                                            <NUMBER>6</NUMBER>
                                        </ITEM_NUMBER_DETAILS>
                                    </ITEM_NUMBER_INFO>
                                </FEE_DESCRIPTION_GROUP>
                            </LIST_SERVICE_DETAILS_GROUP>
                            <LIST_SERVICE_DETAILS_GROUP>
                                <SERVICE_OPTION_INFO>
                                    <DATA_TYPE_INFORMATION>
                                        <SUBTYPE>056</SUBTYPE>
                                    </DATA_TYPE_INFORMATION>
                                </SERVICE_OPTION_INFO>
                                <FEE_DESCRIPTION_GROUP>
                                    <COMMERCIAL_NAME>
                                        <FREE_TEXT_QUALIFICATION>
                                            <TEXT_SUBJECT_QUALIFIER>3</TEXT_SUBJECT_QUALIFIER>
                                        </FREE_TEXT_QUALIFICATION>
                                        <FREE_TEXT>REFUNDABLE TICKET</FREE_TEXT>
                                    </COMMERCIAL_NAME>
                                    <SERVICE_DESCRIPTION_INFO>
                                        <SERVICE_REQUIREMENTS_INFO>
                                            <SERVICE_SUB_GROUP>RF</SERVICE_SUB_GROUP>
                                            <SERVICE_GROUP>BF</SERVICE_GROUP>
                                            <SERVICE_MARKETING_CARRIER>TK</SERVICE_MARKETING_CARRIER>
                                            <SERVICE_CLASSIFICATION>Z</SERVICE_CLASSIFICATION>
                                        </SERVICE_REQUIREMENTS_INFO>
                                    </SERVICE_DESCRIPTION_INFO>
                                    <ITEM_NUMBER_INFO>
                                        <ITEM_NUMBER_DETAILS>
                                            <TYPE>SD</TYPE>
                                            <NUMBER>7</NUMBER>
                                        </ITEM_NUMBER_DETAILS>
                                    </ITEM_NUMBER_INFO>
                                </FEE_DESCRIPTION_GROUP>
                            </LIST_SERVICE_DETAILS_GROUP>
                            <TYPE>OC</TYPE>
                        </LIST_SERVICE_FEES_GROUP>
                    </flexPricerAvailabilityOutput>
                </availabilityFlexPricerResponse>
                <requestInfos>
                    <requestInfo key="targetSource" value="PREMIUM"/>
                    <requestInfo key="origin" value="JFK"/>
                    <requestInfo key="destination" value="IST"/>
                    <requestInfo key="routingType" value="O"/>
                    <requestInfo key="originCountry" value="US"/>
                    <requestInfo key="destinationCountry" value="TR"/>
                    <requestInfo key="travelMonth" value="MAY"/>
                    <requestInfo key="travelDay" value="24.05.2022"/>
                    <requestInfo key="calendarPeriod" value="P0D"/>
                    <requestInfo key="passengerIp" value="************"/>
                    <requestInfo key="travelDuration" value=""/>
                    <requestInfo key="preferredCabin" value="ECONOMY"/>
                    <requestInfo key="occupiedSeatNumber" value="1"/>
                    <requestInfo key="infantCount" value="0"/>
                    <requestInfo key="priorToFlight" value="0"/>
                    <requestInfo key="totalPassengerCount" value="1"/>
                    <requestInfo key="accountCode" value=""/>
                    <requestInfo key="adultCount" value="1"/>
                    <requestInfo key="childCount" value="0"/>
                    <requestInfo key="studentCount" value="0"/>
                    <requestInfo key="officeId" value="ISTTK18BF"/>
                </requestInfos>
            </ns0:createAvailabilityOTAResponse>
            <responseHeader>
                <ns3:statusCode>SUCCESS</ns3:statusCode>
                <ns3:clientTransactionId>CTID-TD4P-240522-*********</ns3:clientTransactionId>
                <ns3:serverTransactionId>712d9dbf-dcc1-4f6e-a9aa-4f6b590c35ae</ns3:serverTransactionId>
            </responseHeader>
        </ns0:getAvailabilityResponse>
    </S:Body>
</S:Envelope>
