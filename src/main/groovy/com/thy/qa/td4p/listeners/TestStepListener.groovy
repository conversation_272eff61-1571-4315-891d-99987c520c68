package com.thy.qa.td4p.listeners

import com.eviware.soapui.impl.wsdl.teststeps.WsdlTestRequestStep
import com.eviware.soapui.model.testsuite.*
import com.thy.qa.td4p.teststep.TestStepFactory
import com.thy.qa.td4p.teststep.WsdlRequestStep
import groovy.transform.AutoImplement

@AutoImplement
class TestStepListener implements TestRunListener {

    TestStepFactory testStepFactory = new TestStepFactory()

    @Override
    void beforeStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStep testStep) {
        if (testStep instanceof WsdlTestRequestStep && context.currentStep.getName().startsWith("TD4P")) {
            WsdlRequestStep step = testStepFactory.getTestStep(context.currentStep.getOperation().name)
            if (step.beforeStepEnabled) step.beforeStep(testRunner, context, testStep)
        }
    }

    @Override
    void afterStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStepResult testStepResult) {
        if (testStepResult.getTestStep() instanceof WsdlTestRequestStep && context.currentStep.getName().startsWith("TD4P")) {
            WsdlRequestStep step = testStepFactory.getTestStep(context.currentStep.getOperation().name)
            if (step.afterStepEnabled) step.afterStep(testRunner, context, testStepResult)
        }
    }

}
