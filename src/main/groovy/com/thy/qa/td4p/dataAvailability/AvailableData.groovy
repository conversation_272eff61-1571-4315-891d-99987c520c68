package com.thy.qa.td4p.dataAvailability

import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCase
import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCaseRunner
import com.eviware.soapui.model.testsuite.TestRunListener
import com.eviware.soapui.support.XmlHolder
import com.eviware.soapui.support.types.StringToObjectMap
import com.thy.qa.td4p.Pnr
import com.thy.qa.td4p.ProjectManager.ProjectType
import com.thy.qa.td4p.TestCaseImpl
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.baseclasses.OriginDestinationOption
import com.thy.qa.td4p.configuration.Configurable
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.listeners.TestStepListener
import com.thy.qa.td4p.merchandising.request.FreeReservationOption
import com.thy.qa.td4p.merchandising.request.PricedReservationOption
import com.thy.qa.td4p.pnr.Flow

class AvailableData implements TestCaseImpl, Configurable<AvailableData> {

    private static ProjectType getProjectType() { ProjectType.TD4P }

    private final Map propertiesMap = [:]

    /** the runner object to retrieve test results */
    private WsdlTestCaseRunner runner = null

    /** the testCase object to be run */
    private WsdlTestCase testCase = null

    /** the testCaseName object to set test case name */
    private String testCaseName = ''

    private TestRunListener testStepListener = new TestStepListener()

    Pnr getAvailableOnlineTicketData(TestCaseContext testCaseContext) {
        setTestCaseMap(testCaseContext)
        setTestCaseName()
        propertiesMap.flow = Flow.CASH_TICKETING
        testCase = project.getTestSuiteAt(0).cloneTestCase(project.getTestSuiteAt(9).getTestCaseAt(0), testCaseName)
        getAvailabilityData(testCase)
    }

    Pnr getAvailableAwardTicketData(String firstPassengerMsNo, TestCaseContext testCaseContext) {
        setTestCaseMap(testCaseContext)
        propertiesMap['msNos'][0] = firstPassengerMsNo
        propertiesMap.flow = Flow.VPO_AWARD_TICKETING
        propertiesMap['routingType'] = RoutingType.ONEWAY
        setTestCaseName()
        testCase = project.getTestSuiteAt(0).cloneTestCase(project.getTestSuiteAt(9).getTestCaseAt(1), testCaseName)
        getAvailabilityData(testCase)
    }

    Pnr getAvailableFreeReservationData(TestCaseContext testCaseContext) {
        setTestCaseMap(testCaseContext)
        setTestCaseName()
        propertiesMap.serviceRequests = [new FreeReservationOption()]
        propertiesMap.flow = Flow.FREE_RESERVATION
        testCase = project.getTestSuiteAt(0).cloneTestCase(project.getTestSuiteAt(9).getTestCaseAt(2), testCaseName)
        getAvailabilityData(testCase)
    }

    Pnr getAvailablePaidReservationData(TestCaseContext testCaseContext) {
        setTestCaseMap(testCaseContext)
        setTestCaseName()
        propertiesMap.serviceRequests = [new PricedReservationOption()]
        propertiesMap.flow = Flow.PAID_RESERVATION
        testCase = project.getTestSuiteAt(0).cloneTestCase(project.getTestSuiteAt(9).getTestCaseAt(2), testCaseName)
        getAvailabilityData(testCase)
    }

    private Pnr getAvailabilityData(WsdlTestCase testCase) {
        runTestCase(testCase)
        if (runner.getStatus().toString().equalsIgnoreCase('PASS')) {
            XmlHolder responseHolder = new XmlHolder(testCase.getTestStepByName("TD4P-retrieveReservationDetail").getPropertyValue('Response'))
            propertiesMap.pnr.pnrNumber = responseHolder.getNodeValue("//*:BookingReferenceID[@Type='PNR']/@ID")
            propertiesMap.pnr.totalAmount = new BigDecimal(responseHolder.getNodeValue("//*:PTC_FareBreakdowns//*:GrandTotal/@Amount"))
            propertiesMap.pnr.currencyCode = responseHolder.getNodeValue("//*:PTC_FareBreakdowns//*:GrandTotal/@CurrencyCode")
            return propertiesMap.pnr
        } else {
            return null
        }
    }

    private void runTestCase(WsdlTestCase testCase) {
        testCase.addAsFirstTestRunListener(testStepListener)
        propertiesMap << toMap()
        log.info("propertiesMap is set as $propertiesMap")
        log.info("sessionid is $propertiesMap.sessionId for reporting any issue or reviewing service call logs.")
        runner = testCase.run(new StringToObjectMap(propertiesMap), false)
        sendLogs()
    }

    private void setTestCaseName() {
        StringBuilder testCaseNameBuilder = new StringBuilder()
        testCaseNameBuilder.append([propertiesMap.seats, propertiesMap.passengerCodes.unique(false)*.toString()].transpose()*.sum().join('-'))
        testCaseNameBuilder.append('-' + propertiesMap.portCodes.join('-'))
        testCaseNameBuilder.append('-' + propertiesMap.stopovers.collect { (it >= 1) ? 'Connecting' : 'Direct' }.join('-'))
        testCaseNameBuilder.append('-' + propertiesMap.fareTypes*.name().join('-'))
        if (propertiesMap['ticketType'] == 'award') {
            testCaseNameBuilder.append('-MILESPAYMENT')
        } else {
            testCaseNameBuilder.append('-' + propertiesMap.payment.getPaymentInfo().get('paymentType').toString().toUpperCase())
        }
        testCaseName = testCaseNameBuilder.toString()
    }

    private void setTestCaseMap(def testCaseContext) {
        testCaseContext.setTestCaseContextMap()
        this.propertiesMap << testCaseContext.getPropertiesMap()
    }

    /**
     * Used for getting surname info after generating pnr.
     * @return surname
     */
    String getSurname() { runner.getTestCase().getPropertyValue('surname') }

    /**
     * Used for getting test step's response after generating pnr.
     * @param name the name of the test step
     * @return response
     */
    String getTestStepResponse(String name) {
        runner.getRunContext().expand('${' + name + '#Response}')
    }

    String getTestStepResponseAt(int index) {
        return runner.getTestCase().getTestStepList()[index].getPropertyValue('Response')
    }

    /**
     * Used for getting test step's raw request after generating pnr.
     * @param name the name of the test step
     * @return raw request
     */
    String getTestStepRequest(String name) {
        runner.getRunContext().expand('${' + name + '#RawRequest}')
    }

    /**
     * Used for getting runner object for test run results.
     * @return runner
     */
    WsdlTestCaseRunner getRunner() { runner }

    /**
     * Used for getting all passengers info after any successful method call to generate pnr.
     * @return a list of transposed ticketNumbers and surnames. Each item consists of ticket no and surname in turn.
     */
    List<List<String>> getPassengerInfo() {
        XmlHolder responseHolder = new XmlHolder(getTestStepResponseAt(-1))
        List ticketNumbers = responseHolder.getNodeValues('//*:Ticketing/@TicketDocumentNbr').reverse()
        List surnames = responseHolder.getNodeValues('//*:AirTraveler//*:PersonName//*:Surname')
        return [ticketNumbers, surnames].transpose()
    }

    List<OriginDestinationOption> getFlights() {
        propertiesMap['pnr'].originDestinationOptions
    }

    /**
     * Used for getting the reason why the test case has failed.
     * @return failure reason
     */
    String getFailureReason() { runner.getReason() }

    /**
     * Used for setting soapui project's auth profile's username.*/
    void setAuthUsername(String username) {
        project.getAuthRepository().getEntry('ykmwstestuser').setUsername(username)
    }

    /**
     * Used for setting soapui project's auth profile's password.*/
    void setAuthPassword(String password) {
        project.getAuthRepository().getEntry('ykmwstestuser').setPassword(password)
    }
}
