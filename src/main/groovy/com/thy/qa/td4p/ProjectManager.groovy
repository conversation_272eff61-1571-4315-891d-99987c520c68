package com.thy.qa.td4p

import com.eviware.soapui.impl.wsdl.WsdlProject
import com.thy.qa.td4p.configuration.TestRunAppender
import groovy.transform.CompileStatic
import groovy.transform.Synchronized
import org.apache.logging.log4j.Level
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.core.LoggerContext
import org.apache.logging.log4j.core.appender.SocketAppender
import org.apache.logging.log4j.core.config.Configuration
import org.apache.logging.log4j.core.config.ConfigurationScheduler
import org.apache.logging.log4j.core.config.LoggerConfig
import org.apache.logging.log4j.core.net.Protocol
import org.apache.logging.log4j.core.util.CronExpression
import org.apache.logging.log4j.layout.template.json.JsonTemplateLayout

import java.time.LocalDateTime

import static com.thy.qa.td4p.ProjectManager.ProjectType.*

@CompileStatic
@Singleton
class ProjectManager {

    private WsdlProject td4pProject
    private WsdlProject milesAndSmilesProject
    private WsdlProject paresProject

    private boolean isTestRunLoggerConfigured = false

    private static final String GRAYLOG_HOST = System.getProperty("GRAYLOG_HOST", "graylogdev.thy.com")

    private static final String GRAYLOG_LOGGER_NAME = "GrayLogSender"

    static enum ProjectType {
        TD4P, MILES_AND_SMILES, PARES
    }

    @Synchronized('instance')
    WsdlProject getProjectByType(ProjectType projectType) {
        WsdlProject project = switch (projectType) {
            case TD4P -> td4pProject ?: (td4pProject = createProject('/soapuiprojects/TD4PProject.xml'))
            case MILES_AND_SMILES -> milesAndSmilesProject ?: (milesAndSmilesProject = createProject('/soapuiprojects/MilesAndSmilesProject.xml'))
            case PARES -> paresProject ?: (paresProject = createProject('/soapuiprojects/ParesProject.xml'))
        }
        if (!isTestRunLoggerConfigured) {
            configureTestRunAppender()
            isTestRunLoggerConfigured = true
        }
        return project
    }

    private WsdlProject createProject(String projectPath) {
        return new WsdlProject(ProjectManager.getResourceAsStream(projectPath), null)
    }

    private void configureTestRunAppender() {
        LoggerContext context = (LoggerContext) LogManager.getContext(false)
        Configuration config = context.getConfiguration()
        LoggerConfig loggerConfig = createLoggerConfig(config)
        config.addLogger(GRAYLOG_LOGGER_NAME, loggerConfig)
        scheduleTestRunLogClear(config)
        TestRunAppender appender = createTestRunAppender()
        SocketAppender socketAppender = createSocketAppender(config)
        addAppendersToLoggers(config, appender, socketAppender)
        context.updateLoggers()
    }

    private LoggerConfig createLoggerConfig(Configuration config) {
        return ((LoggerConfig.Builder) LoggerConfig.newBuilder())
                .withConfig(config)
                .withLoggerName(GRAYLOG_LOGGER_NAME)
                .withLevel(Level.INFO)
                .build()
    }

    private void scheduleTestRunLogClear(Configuration config) {
        ConfigurationScheduler scheduler = config.getScheduler()
        scheduler.incrementScheduledItems()
        scheduler.scheduleWithCron(new CronExpression("0 */5 * * * ?"), LocalDateTime.now().plusMinutes(10).toDate(), {
            TestRunAppender.clearTestRunLogs()
        })
    }

    private TestRunAppender createTestRunAppender() {
        TestRunAppender.createAppender("TestRunAppender", null).with {
            start()
            it
        }
    }

    private SocketAppender createSocketAppender(Configuration config) {
        SocketAppender.newBuilder().setName("GRAYLOG")
                .setHost(GRAYLOG_HOST)
                .setPort(12212)
                .setProtocol(Protocol.UDP)
                .setImmediateFail(true)
                .setLayout(JsonTemplateLayout.newBuilder()
                        .setEventTemplateUri("classpath:GelfLayout.json")
                        .setConfiguration(config)
                        .setEventTemplateAdditionalFields(JsonTemplateLayout.EventTemplateAdditionalField.newBuilder()
                                .setKey("application").setValue("TD4P").build()).build())
                .build().with {
            start()
            it
        }
    }

    private void addAppendersToLoggers(Configuration config, TestRunAppender appender, SocketAppender socketAppender) {
        Map<String, LoggerConfig> loggers = config.getLoggers()
        List<LoggerConfig> loggerConfigs = loggers.findAll { entry -> entry.key in ['com.eviware.soapui', 'groovy.log'] }*.value
        loggerConfigs.each { it.addAppender(appender, Level.INFO, null) }
        LoggerConfig graylogSenderConfig = loggers.find { it.key == 'GrayLogSender' }?.value
        graylogSenderConfig.addAppender(socketAppender, Level.INFO, null)
    }
}