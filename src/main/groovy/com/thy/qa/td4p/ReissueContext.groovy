package com.thy.qa.td4p

import com.eviware.soapui.model.testsuite.TestRunContext
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.environment.TroyaEnvironment
import com.thy.qa.td4p.payments.CreditCardPayment
import com.thy.qa.td4p.payments.Payment
import com.thy.qa.td4p.request.RequestHeader

import java.time.Duration
import java.time.LocalDateTime
import java.time.YearMonth
import java.time.ZoneId
import java.time.format.DateTimeFormatter

class ReissueContext {
    Enum<Channel> channel = Channel.WEB3
    Payment payment = new CreditCardPayment(YearMonth.of(2030, 02), "123", "****************")
    List<AirlineCode> airlineCodes = []

    private Duration reissueDelay = Duration.ofSeconds(5)

    private Map propertiesMap = [pnr: new Pnr()]

    Map getPropertiesMap() { propertiesMap }

    private RoutingType routingType
    private List<Flight> flights
    private List<Integer> removedFlights

    /**
     * @param routingType -> It should be determined by taking {@code addedFlights} parameter's flights into account. So it has nothing to do with the previous method call's routingType parameter or pnr's routing type.
     * <p>
     * @param addedFlights -> Set it however you do for {@link TestCaseContext#TestCaseContext(com.thy.qa.td4p.enums.RoutingType, java.util.List, com.thy.qa.td4p.passenger.PassengerCombination)}.
     *
     * @since *******
     *
     */
    ReissueContext(RoutingType routingType, List<Flight> addedFlights) {
        this.routingType = routingType
        this.flights = addedFlights*.validate().sort()
    }

    /**
     * @param routingType -> It should be determined by taking {@code addedFlights} parameter's flights into account. So it has nothing to do with the previous method call's routingType parameter or pnr's routing type.
     * <p>
     * @param addedFlights -> Set it however you do for {@link TestCaseContext#TestCaseContext(com.thy.qa.td4p.enums.RoutingType, java.util.List, com.thy.qa.td4p.passenger.PassengerCombination)}.
     * <p>
     * @param removedFlights -> if you wish to learn about indexing flights to be removed, see {@link ReissueContext#ReissueContext(java.util.List)}.
     *
     * @since *******
     *
     */
    ReissueContext(RoutingType routingType, List<Flight> addedFlights, List<Integer> removedFlights) {
        this.routingType = routingType
        this.flights = addedFlights*.validate().sort()
        this.removedFlights = removedFlights
    }

    /**
     * @param removedFlights -> list of flight indexes starting from 1.
     * <p>
     * Only flights could be cancelled. Any leg of flight (For flight ADB-JFK with stopover 1 below -> IST-JFK flight leg) can not be cancelled separately.
     * <p>
     * Every flight has only one index even if it has multiple legs. So this means that any flight leg can not be indexed.
     * <p>
     * ARNK segment (which is inserted between two segments that are not connected) can not be indexed.
     * <p>
     * For the example below, the indexes must be [1, 2].
     * <p>
     * [new Flight(origin: "ADB",destination: "JFK", stopover: 1, dayToFlight: 110),<br>
     *  new Flight(origin: "CDG",destination: "IST", stopover: 0, dayToFlight: 122)]<br>
     *
     * @since *******
     *
     */
    ReissueContext(List<Integer> removedFlights) { this.removedFlights = removedFlights }

    /**
     * It is evaluated as time millis. If you set this to a value more than 60000 millis, it is set to 60000. Because any delay more than 60 seconds makes no sense.<br>
     * Feel free to set any type of {@link Duration} -> second, minute, time millis..<br>
     * Because it will be converted to time millis anyway.
     * @since *******
     */
    void setReissueDelay(Duration reissueDelay) { this.reissueDelay = reissueDelay }

    void setTestCaseContextMap() {
        setReissueParameters()
        setMap()
        propertiesMap['channel'] = channel
    }

    void setContextParameters(TestRunContext context) {
        setTestCaseContextMap()
        propertiesMap.each { String key, def value -> context[key] = value }
    }

    private void setReissueParameters() {
        if (flights) {
            propertiesMap['addReissueFlights'] = true
            propertiesMap['flights'] = flights
            propertiesMap['portCodes'] = [flights*.origin,
                                          flights*.destination].transpose().flatten()*.toUpperCase()
            propertiesMap['isOriginMultiAirport'] = flights*.isOriginMultiAirport
            propertiesMap['isDestinationMultiAirport'] = flights*.isDestinationMultiAirport
            propertiesMap['stopovers'] = flights*.stopover
            propertiesMap['daysToFlights'] = flights*.dayToFlight
            propertiesMap['fareTypes'] = flights*.fareType
        } else {
            propertiesMap['addReissueFlights'] = false
        }

        if (routingType) {
            Map channelInfo = channel.value()
            boolean foreignSalesOffice = (channelInfo.get('requestHeaderInfo').get('clientUsername') == 'QRES' && channelInfo.get('currencyCode') != 'TRY')
            RoutingType tripType = (domestic && !foreignSalesOffice && routingType == RoutingType.ROUNDTRIP) ? RoutingType.ONEWAY : routingType
            propertiesMap['routingType'] = tripType
        }

        if (removedFlights) {
            propertiesMap['cancelFlights'] = true
            propertiesMap['removedFlightIndexes'] = removedFlights
        } else {
            propertiesMap['cancelFlights'] = false
        }
    }

    private void setMap() {
        propertiesMap['payment'] = payment

        if (airlineCodes != []) {
            propertiesMap['airlineCodes'] = airlineCodes*.toString()
        }

        LocalDateTime currentLocalDateTime = LocalDateTime.now(ZoneId.systemDefault())
        propertiesMap['currentLocalDateTime'] = currentLocalDateTime
        propertiesMap['sessionId'] = RequestHeader.getSID_PREFIX() + '-' + currentLocalDateTime.format(DateTimeFormatter.ofPattern('ddMMyy-HHmmssSSS'))
        propertiesMap['reissueDelay'] = (reissueDelay.compareTo(Duration.ofMinutes(1)) > 0) ? 60000 : reissueDelay.toMillis()
    }

    private boolean isDomestic() {
        propertiesMap['portCodes'].every { it in TestCaseContext.DOMESTIC_PORT_LIST }
    }

}