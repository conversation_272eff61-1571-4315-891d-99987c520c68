package com.thy.qa.td4p

import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCase
import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCaseRunner
import com.eviware.soapui.model.testsuite.TestRunListener
import com.eviware.soapui.support.XmlHolder
import com.eviware.soapui.support.types.StringToObjectMap
import com.thy.qa.td4p.ProjectManager.ProjectType
import com.thy.qa.td4p.baseclasses.OriginDestinationOption
import com.thy.qa.td4p.configuration.Configurable
import com.thy.qa.td4p.enums.FlightHour
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.listeners.CheckinTestStepListener
import com.thy.qa.td4p.listeners.TestStepListener
import com.thy.qa.td4p.merchandising.request.FreeReservationOption
import com.thy.qa.td4p.merchandising.request.PricedReservationOption
import com.thy.qa.td4p.merchandising.request.ServiceRequestsContext
import com.thy.qa.td4p.payandfly.PayAndFlyContext
import com.thy.qa.td4p.payments.CreditCardPayment
import com.thy.qa.td4p.payments.MilesPayment
import com.thy.qa.td4p.payments.Payment
import com.thy.qa.td4p.pnr.Flow

import java.time.YearMonth

/**
 * The GetPnr class represents the entry to the application. Once it's initialized, Soapui project is loaded and any test case of which gets ready to be run.
 * <p>
 * The aim of this application is basically to generate pnr. Moreover, it also provides the mechanism for running functional tests on Soapui application
 * when you have its test run listener {@link TestStepListener}, required test steps in your test case
 * and set required context parameters by calling {@link TestCaseContext} -> {@link com.thy.qa.td4p.TestCaseContext#setContextParameters(context) setContextParameters} method or {@link ReissueContext} -> {@link com.thy.qa.td4p.ReissueContext#setContextParameters(context) setContextParameters} before test steps.
 * <p>
 * The class GetPnr includes methods for generating pnr of types which are ticket, award ticket, checkin, pay and fly, free reservation, paid reservation and reissue.
 * <p>
 * Its structure heavily depends on creating a properties map, running the test case of the loaded project and extracting the needed info (for example pnr, surname, ticket number...) if the test case has passed.
 *
 * @param runner -> use this parameter for logging test step results.
 *
 * <AUTHOR> BALCILAR
 */
class GetPnr implements TestCaseImpl, Configurable<GetPnr> {

    private static ProjectType getProjectType() { ProjectType.TD4P }

    private Map propertiesMap = [:]

    /** the runner object to retrieve test results */
    private WsdlTestCaseRunner runner = null

    /** the testCase object to be run */
    private WsdlTestCase testCase = null

    /** the testCaseName object to set test case name */
    private String testCaseName = ''

    private TestRunListener testStepListener = new TestStepListener()

    /**
     * Checkin VPOawardPnr ->
     * @param firstPassengerMsNo -> Miles&Smiles number value of the first passenger on the pnr.
     * @param TestCaseContext testCaseContext -> Day value for FlightHour.NONE flight hour must be set properly if used.
     *             For example, [0,0] days value list for [FlightHour.WITHIN24HOURS, FlightHour.NONE] may be invalid.
     *             Because if day value for FlightHour.WITHIN24HOURS is incremented by 1 at runtime, then no pnr can not be produced due to invalid [1, 0] day value list.
     * @param flightHours
     *
     * @return checkin VPOawardPnr
     *
     * @since 1.3.7.0
     */
    String getVPOAwardTicketPnrForCheckin(String firstPassengerMsNo, TestCaseContext testCaseContext, List<FlightHour> flightHours) {
        setTestCaseMap(testCaseContext)
        propertiesMap['msNos'][0] = firstPassengerMsNo
        propertiesMap.flow = Flow.VPO_AWARD_TICKETING
        propertiesMap['routingType'] = RoutingType.ONEWAY
        setCheckinFlightDays(flightHours)
        propertiesMap['flightHours'] = flightHours
        testStepListener = new CheckinTestStepListener()
        setTestCaseName()
        testCase = project.getTestSuiteAt(2).cloneTestCase(project.getTestSuiteAt(2).getTestCaseAt(1), testCaseName)
        createPnrNumber(testCase)
    }

    /**
     * Checkin awardPnr ->
     * @param firstPassengerMsNo -> Miles&Smiles number value of the first passenger on the pnr.
     * @param TestCaseContext testCaseContext -> Day value for FlightHour.NONE flight hour must be set properly if used.
     *             For example, [0,0] days value list for [FlightHour.WITHIN24HOURS, FlightHour.NONE] may be invalid.
     *             Because if day value for FlightHour.WITHIN24HOURS is incremented by 1 at runtime, then no pnr can not be produced due to invalid [1, 0] day value list.
     * @param flightHours
     *
     * @return checkin awardPnr
     *
     * @since 1.3.7.0
     */
    String getAwardTicketPnrForCheckin(String firstPassengerMsNo, TestCaseContext testCaseContext, List<FlightHour> flightHours) {
        setTestCaseMap(testCaseContext)
        propertiesMap['msNos'][0] = firstPassengerMsNo
        propertiesMap.flow = Flow.AWARD_TICKETING
        propertiesMap['payment'] = new MilesPayment()
        propertiesMap['routingType'] = RoutingType.ONEWAY
        setCheckinFlightDays(flightHours)
        propertiesMap['flightHours'] = flightHours
        testStepListener = new CheckinTestStepListener()
        setTestCaseName()
        testCase = project.getTestSuiteAt(2).cloneTestCase(project.getTestSuiteAt(2).getTestCaseAt(1), testCaseName)
        createPnrNumber(testCase)
    }

    /**
     * Checkin pnr ->
     * @param TestCaseContext testCaseContext -> Day value for FlightHour.NONE flight hour must be set properly if used.
     *             For example, [0,0] days value list for [FlightHour.WITHIN24HOURS, FlightHour.NONE] may be invalid.
     *             Because if day value for FlightHour.WITHIN24HOURS is incremented by 1 at runtime, then no pnr can not be produced due to invalid [1, 0] day value list.
     * @param flightHours
     *
     * @return checkin pnr
     *
     * @since 1.3.0.0
     */
    String getTicketPnrForCheckin(TestCaseContext testCaseContext, List<FlightHour> flightHours) {
        setTestCaseMap(testCaseContext)
        setCheckinFlightDays(flightHours)
        propertiesMap['flightHours'] = flightHours
        propertiesMap.flow = Flow.CASH_TICKETING
        testStepListener = new CheckinTestStepListener()
        setTestCaseName()
        testCase = project.getTestSuiteAt(2).cloneTestCase(project.getTestSuiteAt(2).getTestCaseAt(0), testCaseName)
        createPnrNumber(testCase)
    }

    /**
     * Creates a getting method for ticket pnr.
     * @param TestCaseContext testCaseContext
     * @return ticket pnr
     */
    String getTicketPnr(TestCaseContext testCaseContext) {
        setTestCaseMap(testCaseContext)
        propertiesMap.flow = Flow.CASH_TICKETING
        setTestCaseName()
        testCase = project.getTestSuiteAt(0).cloneTestCase(project.getTestSuiteAt(0).getTestCaseAt(0), testCaseName)
        createPnrNumber(testCase)
    }

    void purchaseAncillaryServices(String pnrNumber, String surname, ServiceRequestsContext ancillaryServicesContext) {
        setTestCaseMap(ancillaryServicesContext)
        propertiesMap['pnr'].pnrNumber = pnrNumber
        propertiesMap['surname'] = surname
        propertiesMap.flow = Flow.STANDALONE
        testCase = project.getTestSuiteAt(6).cloneTestCase(project.getTestSuiteAt(6).getTestCaseAt(0), "PurchaseAncillaryServices-" + pnrNumber)
        runTestCase(testCase)
    }

    /**
     * Creates a getting method for reissued pnr.
     *
     * @param pnr
     * @param surname
     * @param ReissueContext reissueContext
     *
     * @return reissued pnr
     */
    String getReissuedPnr(String pnrNumber, String surname, ReissueContext reissueContext) {
        setTestCaseMap(reissueContext)
        propertiesMap['pnr'].pnrNumber = pnrNumber
        propertiesMap['surname'] = surname
        propertiesMap.flow = Flow.REISSUE
        testCaseName = (testCaseName) ? testCaseName + '-Reissue' : pnrNumber + '-Reissue'
        testCase = project.getTestSuiteAt(3).cloneTestCase(project.getTestSuiteAt(3).getTestCaseAt(0), testCaseName)
        createPnrNumber(testCase)
    }

    /**
     * Adds infant passenger(s) to the PNR
     *
     * @param pnr
     * @param surname
     * @param InfantAdditionContext infantAdditionContext
     *
     * @return infant added pnr
     *
     * @since 1.3.7.0
     */
    String getInfantAddedPnr(String pnrNumber, String surname, InfantAdditionContext infantAdditionContext) {
        setTestCaseMap(infantAdditionContext)
        propertiesMap['pnr'].pnrNumber = pnrNumber
        propertiesMap['surname'] = surname
        propertiesMap.flow = Flow.INFANT_ADDITION
        testCaseName = (testCaseName) ? testCaseName + '-InfantAddition' : pnrNumber + '-InfantAddition'
        testCase = project.getTestSuiteAt(4).cloneTestCase(project.getTestSuiteAt(4).getTestCaseAt(0), testCaseName)
        createPnrNumber(testCase)
    }

    /**
     * Used for getting reservation pnr.
     * @param TestCaseContext testCaseContext
     * @return reservation pnr
     */
    String getPaidReservationPnr(TestCaseContext testCaseContext) {
        setTestCaseMap(testCaseContext)
        setTestCaseName()
        propertiesMap.serviceRequests = [new PricedReservationOption()]
        propertiesMap.flow = Flow.PAID_RESERVATION
        testCase = project.getTestSuiteAt(1).cloneTestCase(project.getTestSuiteAt(1).getTestCaseAt(1), testCaseName)
        createPnrNumber(testCase)
    }

    String getFreeReservationPnr(TestCaseContext testCaseContext) {
        setTestCaseMap(testCaseContext)
        setTestCaseName()
        propertiesMap.serviceRequests = [new FreeReservationOption()]
        propertiesMap.flow = Flow.FREE_RESERVATION
        testCase = project.getTestSuiteAt(1).cloneTestCase(project.getTestSuiteAt(1).getTestCaseAt(0), testCaseName)
        createPnrNumber(testCase)
    }

    String getAwardTicketPnr(String firstPassengerMsNo, TestCaseContext testCaseContext) {
        setTestCaseMap(testCaseContext)
        propertiesMap['msNos'][0] = firstPassengerMsNo
        propertiesMap.flow = Flow.AWARD_TICKETING
        propertiesMap['payment'] = new MilesPayment()
        propertiesMap['routingType'] = RoutingType.ONEWAY
        setTestCaseName()
        testCase = project.getTestSuiteAt(0).cloneTestCase(project.getTestSuiteAt(0).getTestCaseAt(1), testCaseName)
        createPnrNumber(testCase)
    }

    String getVPOAwardTicketPnr(String firstPassengerMsNo, TestCaseContext testCaseContext) {
        setTestCaseMap(testCaseContext)
        propertiesMap['msNos'][0] = firstPassengerMsNo
        propertiesMap.flow = Flow.VPO_AWARD_TICKETING
        propertiesMap['routingType'] = RoutingType.ONEWAY
        setTestCaseName()
        testCase = project.getTestSuiteAt(0).cloneTestCase(project.getTestSuiteAt(0).getTestCaseAt(2), testCaseName)
        createPnrNumber(testCase)
    }

    String getPaidReservationPayAndFlyTicketPnr(TestCaseContext testCaseContext) {
        Payment payment = testCaseContext.getPayment()
        testCaseContext.setPayment(new CreditCardPayment(YearMonth.of(2030, 02), '123', '****************'))
        String pnrNumber = getPaidReservationPnr(testCaseContext)
        if (pnrNumber.isEmpty()) return ''
        testCaseContext.setPayment(payment)
        payAndFly(payAndFlyContext(pnrNumber, testCaseContext)) ? pnrNumber : ''
    }

    String getFreeReservationPayAndFlyTicketPnr(TestCaseContext testCaseContext) {
        String pnrNumber = getFreeReservationPnr(testCaseContext)
        if (pnrNumber.isEmpty()) return ''
        payAndFly(payAndFlyContext(pnrNumber, testCaseContext)) ? pnrNumber : ''
    }

    boolean payAndFly(PayAndFlyContext payAndFlyContext) {
        payAndFlyContext.setTestCaseContextMap()
        propertiesMap = payAndFlyContext.getPropertiesMap()
        testCaseName = payAndFlyContext.getPropertiesMap().get('pnr').getPnrNumber() + '-PayAndFly'
        testCase = project.getTestSuiteAt(0).cloneTestCase(project.getTestSuiteAt(0).getTestCaseAt(5), testCaseName)
        createPnrNumber(testCase)
    }

    private PayAndFlyContext payAndFlyContext(String pnrNumber, TestCaseContext testCaseContext) {
        PayAndFlyContext payAndFlyContext = new PayAndFlyContext(pnrNumber, getSurname())
        payAndFlyContext.setChannel(testCaseContext.getChannel())
        payAndFlyContext.setPayment(testCaseContext.getPayment())
        payAndFlyContext
    }

    private String createPnrNumber(WsdlTestCase testCase) {
        runTestCase(testCase)
        if (runner.getStatus().toString().equalsIgnoreCase('PASS')) {
            XmlHolder responseHolder = new XmlHolder(testCase.testStepList.last().getPropertyValue('Response'))
            String pnrNumber = responseHolder.getNodeValue("//*:BookingReferenceID[@Type='PNR']/@ID")
            String surname = responseHolder.getNodeValue('//*:AirTraveler[1]//*:PersonName//*:Surname')
            log.info(testCaseName + ' : ' + pnrNumber)
            log.info('Surname' + ' : ' + surname)
            return pnrNumber
        } else {
            return ''
        }
    }

    private void runTestCase(WsdlTestCase testCase) {
        testCase.addAsFirstTestRunListener(testStepListener)
        propertiesMap << toMap()
        log.info("propertiesMap is set as $propertiesMap")
        log.info("sessionid is $propertiesMap.sessionId for reporting any issue or reviewing service call logs.")
        runner = testCase.run(new StringToObjectMap(propertiesMap), false)
        sendLogs()
    }

    private void setTestCaseName() {
        StringBuilder testCaseNameBuilder = new StringBuilder()
        testCaseNameBuilder.append([propertiesMap.seats, propertiesMap.passengerCodes.unique(false)*.toString()].transpose()*.sum().join('-'))
        testCaseNameBuilder.append('-' + propertiesMap.portCodes.join('-'))
        testCaseNameBuilder.append('-' + propertiesMap.stopovers.collect { (it >= 1) ? 'Connecting' : 'Direct' }.join('-'))
        testCaseNameBuilder.append('-' + propertiesMap.fareTypes*.name().join('-'))
        if (propertiesMap['ticketType'] == 'award') {
            testCaseNameBuilder.append('-MILESPAYMENT')
        } else {
            testCaseNameBuilder.append('-' + propertiesMap.payment.getPaymentInfo().get('paymentType').toString().toUpperCase())
        }
        testCaseName = testCaseNameBuilder.toString()
    }

    private void setTestCaseMap(def testCaseContext) {
        testCaseContext.setTestCaseContextMap()
        this.propertiesMap << testCaseContext.getPropertiesMap()
    }

    /**
     * This method changes all dayToFlight values to 0 except for ones with flight hour FlightHour.NONE.
     * @param flightHours
     */
    private void setCheckinFlightDays(List flightHours) {
        flightHours.eachWithIndex { it, i ->
            if (it != FlightHour.NONE) {
                propertiesMap['daysToFlights'][i] = 0
            }
        }
    }

    /**
     * Used for getting surname info after generating pnr.
     * @return surname
     */
    String getSurname() { runner.getTestCase().getPropertyValue('surname') }

    /**
     * Used for getting test step's response after generating pnr.
     * @param name the name of the test step
     * @return response
     */
    String getTestStepResponse(String name) {
        runner.getRunContext().expand('${' + name + '#Response}')
    }

    String getTestStepResponseAt(int index) {
        return runner.getTestCase().getTestStepList()[index].getPropertyValue('Response')
    }

    /**
     * Used for getting test step's raw request after generating pnr.
     * @param name the name of the test step
     * @return raw request
     */
    String getTestStepRequest(String name) {
        runner.getRunContext().expand('${' + name + '#RawRequest}')
    }

    /**
     * Used for getting runner object for test run results.
     * @return runner
     */
    WsdlTestCaseRunner getRunner() { runner }

    /**
     * Used for getting pnr object after any successful method call to generate pnr.
     * @return pnr
     * @see {@link com.thy.qa.td4p.Pnr}
     */
    Pnr getPnr() { propertiesMap['pnr'] }

    /**
     * Used for getting all passengers info after any successful method call to generate pnr.
     * @return a list of transposed ticketNumbers and surnames. Each item consists of ticket no and surname in turn.
     */
    List<List<String>> getPassengerInfo() {
        XmlHolder responseHolder = new XmlHolder(getTestStepResponseAt(-1))
        List ticketNumbers = responseHolder.getNodeValues('//*:Ticketing/@TicketDocumentNbr').reverse()
        List surnames = responseHolder.getNodeValues('//*:AirTraveler//*:PersonName//*:Surname')
        return [ticketNumbers, surnames].transpose()
    }

    List<OriginDestinationOption> getFlights() { pnr.originDestinationOptions }

    /**
     * Used for getting the reason why the test case has failed.
     * @return failure reason
     */
    String getFailureReason() { runner.getReason() }

    /**
     * Used for setting soapui project's auth profile's username.*/
    static void setAuthUsername(String username) {
        getProject().getAuthRepository().getEntry('ykmwstestuser').setUsername(username)
    }

    /**
     * Used for setting soapui project's auth profile's password.*/
    static void setAuthPassword(String password) {
        getProject().getAuthRepository().getEntry('ykmwstestuser').setPassword(password)
    }
}