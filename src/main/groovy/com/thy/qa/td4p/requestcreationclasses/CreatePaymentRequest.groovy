package com.thy.qa.td4p.requestcreationclasses

import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCaseRunner
import com.eviware.soapui.impl.wsdl.testcase.WsdlTestRunContext
import com.eviware.soapui.support.XmlHolder
import com.thy.qa.td4p.Pnr
import com.thy.qa.td4p.baseclasses.TestStepFinder
import com.thy.qa.td4p.enums.PaymentType
import com.thy.qa.td4p.payments.Payment
import com.thy.qa.td4p.request.soap.payment.CreditCardInfo
import org.apache.logging.log4j.core.Logger
import org.w3c.dom.Element
import org.w3c.dom.Node

import java.time.format.DateTimeFormatter

class CreatePaymentRequest {

    WsdlTestRunContext context
    WsdlTestCaseRunner testRunner
    Logger log

    private def ticketType = context.getProperty('ticketType')

    private String paymentType = context.getProperty('payment').getPaymentInfo().get('paymentType').toString()

    CreatePaymentRequest(context, testRunner, log) {
        this.context = context
        this.testRunner = testRunner
        this.log = log
    }

    void setPaymentRequest(XmlHolder requestHolder, String amount, String currencyCode, String parentNodeName) {
        if (paymentType != PaymentType.EFT.toString()) {
            setPaymentTrackingIdNodeValue(requestHolder)
        } else {
            requestHolder.removeDomNodes("//*:requestHeader//*[@key='PAYMENT_TRACKING_ID']")
            requestHolder.removeDomNodes("//*:requestHeader//*[@key='PG_APPLICATION_MODE']")
        }

        setPaymentOTARequestNode(requestHolder, amount, currencyCode, parentNodeName)
    }

    void setPaymentOTARequestNode(XmlHolder requestHolder, String amount, String currencyCode, String parentNodeName) {
        setBookingReferenceIDNodeValue(requestHolder, parentNodeName)
        setPaymentInfoNode(requestHolder, amount, currencyCode)
        if (ticketType in ["award", "VPOAward"]) {
            setApplicationNodeValue(requestHolder, 'AWT')
            addMilesInfoNodeToPaymentOTARequest(requestHolder, parentNodeName)
            setFrequentFlyerIdNodeValue(requestHolder, context['msNos'].unique(false).findAll().first())
        }
        if (paymentType != 'creditCard') {
            requestHolder.removeDomNodes("//*:threeDSecureInfo")
            requestHolder.removeDomNodes("//*:clientBrowserDetails")
            requestHolder.removeDomNodes("//*:paymentFlowInfo")
        }
        setSurnameNodeValue(requestHolder)
    }

    void setPaymentInfoNode(XmlHolder requestHolder, String amount, String currencyCode) {
        setPaymentTypeValue(requestHolder, paymentType)
        setPaymentAmountValue(requestHolder, amount, currencyCode)
        setPaymentCurrencyCodeValue(requestHolder, currencyCode)
        if (paymentType == 'creditCard') {
            setCreditCardInfoNode(requestHolder)
        } else {
            requestHolder.removeDomNodes("//*:PaymentInfo//*:CreditCardInfo")
        }
    }

    void setCreditCardInfoNode(XmlHolder requestHolder) {
        Payment payment = context.getProperty('payment')
        Map paymentInfo = payment.getPaymentInfo()
        CreditCardInfo creditCardInfo = paymentInfo.get('CreditCardInfo')
        requestHolder['//*:PaymentInfo//*:CreditCardInfo/@Remark'] = (paymentInfo.get('ccCheckRequired') == true) ? 'ccCheckRequired' : ''
        requestHolder['//*:PaymentInfo//*:CreditCardInfo/@ExpireDate'] = creditCardInfo.getExpireDate().format(DateTimeFormatter.ofPattern("MM/yy"))
        requestHolder['//*:PaymentInfo//*:CreditCardInfo//*:CardNumber/*:PlainText'] = creditCardInfo.cardNumber
        requestHolder['//*:PaymentInfo//*:CreditCardInfo//*:SeriesCode/*:PlainText'] = creditCardInfo.seriesCode
    }

    void setBookingReferenceIDNodeValue(XmlHolder requestHolder, String parentNodeName) {
        requestHolder["//*:" + parentNodeName + "//*:BookingReferenceID/@ID"] = ((Pnr) context.getProperty('pnr')).pnrNumber
    }

    String getAmountXpath() {
        (ticketType != "VPOAward") ? "//*:PTC_FareBreakdowns//*:GrandTotal/@Amount" : "//*:PTC_FareBreakdowns//*:GrandTaxTotal/@Amount"
    }

    String getCurrencyCodeXpath() {
        (ticketType != "VPOAward") ? "//*:PTC_FareBreakdowns//*:GrandTotal/@CurrencyCode" : "//*:PTC_FareBreakdowns//*:GrandTaxTotal/@CurrencyCode"
    }

    void setSurnameNodeValue(XmlHolder requestHolder) {
        requestHolder["//*:DemandTicketDetail//*:PassengerName//*:Surname"] = context.getProperty('surname')
    }

    void setPaymentTypeValue(XmlHolder requestHolder, String paymentType) {
        requestHolder["//*:PaymentInfo/@PaymentType"] = paymentType
    }

    void setPaymentAmountValue(XmlHolder requestHolder, String amount, String currencyCode) {
        int decimal = currencyCode != 'PTS' ? 2 : 0
        requestHolder["//*:PaymentInfo/@Amount"] = new BigDecimal(amount).setScale(decimal).toString()
    }

    void setPaymentCurrencyCodeValue(XmlHolder requestHolder, String currencyCode) {
        requestHolder["//*:PaymentInfo/@CurrencyCode"] = currencyCode
    }

    void setFunctionAttributeValue(XmlHolder requestHolder, String function) {
        requestHolder["//*:MessageFunction/@Function"] = function
    }

    void addCoverageNode(XmlHolder requestHolder) {
        Node node = requestHolder.getDomNode("//*:TK_RQIntentionType")
        Element element = node.getOwnerDocument().createElementNS(node.getNamespaceURI(), "" + node.getPrefix() + ":Coverage")
        node.appendChild(element)
    }

    void setCoverageNodeValue(XmlHolder requestHolder, String coverageType) {
        requestHolder["//*:TK_RQIntentionType//*:Coverage"] = coverageType
    }

    void addEmdFareItems(XmlHolder requestHolder) {
        Node processPaymentOTARequest = requestHolder.getDomNode("//*:processPaymentOTARequest")
        Element emdFareItemsElement = processPaymentOTARequest.getOwnerDocument().createElement("emdFareItems")
        processPaymentOTARequest.appendChild(emdFareItemsElement)
    }

    void addEmdFareItemNodeForPrePayment(XmlHolder requestHolder) {
        Node emdFareItems = requestHolder.getDomNode("//*:processPaymentOTARequest/*:emdFareItems")
        Element emdFareItemElement = emdFareItems.getOwnerDocument().createElement("emdFareItem")
        emdFareItems.appendChild(emdFareItemElement)
    }

    void addEmdCurrency(XmlHolder requestHolder) {
        Node emdFareItems = requestHolder.getDomNode("//*:purchaseBasketOTARequest")
        Element emdCurrencyElement = emdFareItems.getOwnerDocument().createElement("emdCurrency")
        emdFareItems.appendChild(emdCurrencyElement)
    }

    void setEmdCurrency(XmlHolder requestHolder, String currencyCode) {
        requestHolder.setNodeValue("//*:purchaseBasketOTARequest/*:emdCurrency", currencyCode)
    }

    void addEmdFareItemNodeForPurchaseBasket(XmlHolder requestHolder) {
        Node purchaseBasketOTARequest = requestHolder.getDomNode("//*:purchaseBasketOTARequest")
        Element emdFareItemElement = purchaseBasketOTARequest.getOwnerDocument().createElement("emdFareItem")
        purchaseBasketOTARequest.appendChild(emdFareItemElement)
    }

    void addEmdFareItemChildNodesForPrePayment(XmlHolder requestHolder, int i) {
        Node emdFareItem = requestHolder.getDomNode("//*:processPaymentOTARequest/*:emdFareItems/*:emdFareItem[" + (i + 1) + "]")
        Element passengerIndexElement = emdFareItem.getOwnerDocument().createElement("passengerIndex")
        emdFareItem.appendChild(passengerIndexElement)
        Element segmentIndexElement = emdFareItem.getOwnerDocument().createElement("segmentIndexList")
        emdFareItem.appendChild(segmentIndexElement)
        Element ssrCodeElement = emdFareItem.getOwnerDocument().createElement("ssrCode")
        emdFareItem.appendChild(ssrCodeElement)
        Element ancillaryItemTypeElement = emdFareItem.getOwnerDocument().createElement("ancillaryItemType")
        emdFareItem.appendChild(ancillaryItemTypeElement)
        Element paymentAmountElement = emdFareItem.getOwnerDocument().createElement("paymentAmount")
        emdFareItem.appendChild(paymentAmountElement)
        Element offerItemElement = emdFareItem.getOwnerDocument().createElement("paymentCurrency")
        emdFareItem.appendChild(offerItemElement)
    }

    void addEmdFareItemChildNodesForPurchaseBasket(XmlHolder requestHolder, int i) {
        Node emdFareItem = requestHolder.getDomNode("//*:purchaseBasketOTARequest/*:emdFareItem[" + (i + 1) + "]")
        Element passengerIndexElement = emdFareItem.getOwnerDocument().createElement("passengerIndex")
        emdFareItem.appendChild(passengerIndexElement)
        Element segmentIndexElement = emdFareItem.getOwnerDocument().createElement("segmentIndexList")
        emdFareItem.appendChild(segmentIndexElement)
        Element ssrCodeElement = emdFareItem.getOwnerDocument().createElement("ssrCode")
        emdFareItem.appendChild(ssrCodeElement)
        Element ancillaryItemTypeElement = emdFareItem.getOwnerDocument().createElement("ancillaryItemType")
        emdFareItem.appendChild(ancillaryItemTypeElement)
        Element paymentAmountElement = emdFareItem.getOwnerDocument().createElement("paymentAmount")
        emdFareItem.appendChild(paymentAmountElement)
        Element offerItemElement = emdFareItem.getOwnerDocument().createElement("paymentCurrency")
        emdFareItem.appendChild(offerItemElement)
    }

    void setEmdFareItemChildNodesForPrePayment(XmlHolder requestHolder, List emdFareItemBlockInfo, int emdFareItemIndex) {
        requestHolder["//*:processPaymentOTARequest/*:emdFareItems/*:emdFareItem[" + (emdFareItemIndex + 1) + "]/*:passengerIndex"] = emdFareItemBlockInfo[1]
        requestHolder["//*:processPaymentOTARequest/*:emdFareItems/*:emdFareItem[" + (emdFareItemIndex + 1) + "]/*:segmentIndexList"] = emdFareItemBlockInfo[0]
        requestHolder["//*:processPaymentOTARequest/*:emdFareItems/*:emdFareItem[" + (emdFareItemIndex + 1) + "]/*:ssrCode"] = "RQST"
        requestHolder["//*:processPaymentOTARequest/*:emdFareItems/*:emdFareItem[" + (emdFareItemIndex + 1) + "]/*:paymentAmount"] = emdFareItemBlockInfo[2][0..emdFareItemBlockInfo[2].size() - 4]
        requestHolder["//*:processPaymentOTARequest/*:emdFareItems/*:emdFareItem[" + (emdFareItemIndex + 1) + "]/*:paymentCurrency"] = emdFareItemBlockInfo[2][emdFareItemBlockInfo[2].size() - 3..emdFareItemBlockInfo[2].size() - 1]
        requestHolder["//*:processPaymentOTARequest/*:emdFareItems/*:emdFareItem[" + (emdFareItemIndex + 1) + "]/*:ancillaryItemType"] = emdFareItemBlockInfo[3]
    }

    void setEmdFareItemChildNodesForPurchaseBasket(XmlHolder requestHolder, List emdFareItemBlockInfo, int emdFareItemIndex) {

        requestHolder["//*:purchaseBasketOTARequest/*:emdFareItem[" + (emdFareItemIndex + 1) + "]/*:passengerIndex"] = emdFareItemBlockInfo[1]
        requestHolder["//*:purchaseBasketOTARequest/*:emdFareItem[" + (emdFareItemIndex + 1) + "]/*:segmentIndexList"] = emdFareItemBlockInfo[0]
        requestHolder["//*:purchaseBasketOTARequest/*:emdFareItem[" + (emdFareItemIndex + 1) + "]/*:ssrCode"] = "RQST"
        requestHolder["//*:purchaseBasketOTARequest/*:emdFareItem[" + (emdFareItemIndex + 1) + "]/*:paymentAmount"] = emdFareItemBlockInfo[2][0..emdFareItemBlockInfo[2].size() - 4]
        requestHolder["//*:purchaseBasketOTARequest/*:emdFareItem[" + (emdFareItemIndex + 1) + "]/*:paymentCurrency"] = emdFareItemBlockInfo[2][emdFareItemBlockInfo[2].size() - 3..emdFareItemBlockInfo[2].size() - 1]
        requestHolder["//*:purchaseBasketOTARequest/*:emdFareItem[" + (emdFareItemIndex + 1) + "]/*:ancillaryItemType"] = emdFareItemBlockInfo[3]
    }

    void setPaymentTrackingIdNodeValue(XmlHolder requestHolder) {
        XmlHolder prePaymentResponseHolder = new XmlHolder(new TestStepFinder(context).findSuccessfulStepOfType('prePayment').getPropertyValue('Response'))
        requestHolder["//*:extraParameters[@key='PAYMENT_TRACKING_ID']/@value"] = prePaymentResponseHolder.getNodeValue("//*:processPaymentOTAResponseList//*:paymentTrackingId")
    }

    void setApplicationNodeValue(XmlHolder requestHolder, String applicationType) {
        requestHolder["//*:TK_RQIntentionType//*:Application"] = applicationType
    }

    void setFrequentFlyerIdNodeValue(XmlHolder requestHolder, String msNo) {
        requestHolder["//*:milesInfo//*:frequentFlyerId"] = msNo
    }

    void addMilesInfoNodeToPaymentOTARequest(XmlHolder requestHolder, String parentNodeName) {
        Node node = requestHolder.getDomNode("//*:" + parentNodeName + "")
        Element element = node.getOwnerDocument().createElement("milesInfo")
        Element childElement = element.getOwnerDocument().createElement("frequentFlyerId")
        node.appendChild(element)
        element.appendChild(childElement)
    }
}
