package com.thy.qa.td4p.requestcreationclasses

import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCaseRunner
import com.eviware.soapui.impl.wsdl.testcase.WsdlTestRunContext
import com.eviware.soapui.support.XmlHolder
import com.thy.qa.td4p.baseclasses.TestStepFinder
import groovy.transform.CompileStatic
import org.apache.logging.log4j.core.Logger

import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter

@CompileStatic
class CreateEftPrepaymentRequest implements CreateRequest {

    TestStepFinder testStepFinder

    CreateEftPrepaymentRequest(WsdlTestRunContext context, WsdlTestCaseRunner testRunner, Logger log) {
        this.context = context
        this.testRunner = testRunner
        this.log = log
        this.testStepFinder = new TestStepFinder(context)
    }

    void updateEftPrepaymentRequest() {

        XmlHolder responseHolder = new XmlHolder(testStepFinder.findSuccessfulStepOfType('purchaseBasket').getPropertyValue('Response'))
        requestHolder = new XmlHolder(getRequestContent("eftPrepayment"))

        requestHolder["//*:clientTransactionId"] = getClientTransactionId()
        requestHolder["//*[@key='SESSION_ID']/@value"] = context.getProperty("sessionId")

        String amount = responseHolder.getNodeValue("//*:PTC_FareBreakdowns//*:GrandTotal/@Amount").replaceAll("\\.", "").padLeft(18, "0")
        String pnrReference = responseHolder.getNodeValue("//*:BookingReferenceID[@Type='REFERENCE']/@ID")
        String eftDate = LocalDate.now(ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern("yyMMdd"))

        requestHolder["//*:batchEftOTARequest//*:eftXmlAmount"] = amount
        requestHolder["//*:batchEftOTARequest//*:eftXmlSenderDescFirst"] = pnrReference
        requestHolder["//*:batchEftOTARequest//*:eftXmlDescription"] = "SOAPTEST " + pnrReference
        requestHolder["//*:batchEftOTARequest//*:eftXmlEftDate"] = eftDate

        requestHolder["//*:batchEftOTARequest//*:eftXmlIsbankQueryNo"] = (int) (Math.random() * **********)

        context.getCurrentStep().setPropertyValue("Request", requestHolder.getPrettyXml())

        log.info("eftPrepayment request serialized.")
    }

}
