package com.thy.qa.td4p.requestcreationclasses

import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCaseRunner
import com.eviware.soapui.impl.wsdl.testcase.WsdlTestRunContext
import com.eviware.soapui.support.XmlHolder
import com.thy.qa.td4p.request.RequestHeader
import org.apache.commons.io.IOUtils
import org.apache.logging.log4j.core.Logger
import org.w3c.dom.Node

import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

trait CreateRequest {

    WsdlTestRunContext context
    WsdlTestCaseRunner testRunner
    Logger log

    XmlHolder requestHolder

    String getIp() {
        return InetAddress.getLocalHost().getHostAddress()
    }

    String getClientTransactionId() {
        RequestHeader.getCTID_PREFIX() + "-" + (getContext().currentStepIndex + 1) + "-" + LocalDateTime.now(ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern("ddMMyy-HHmmssSSS"))
    }

    String getRequestContent(String name) {
        IOUtils.toString(CreateRequest.getResourceAsStream('/requests/' + name + '.xml'), 'UTF-8')
    }

    void setRequestHeader() {
        Map requestHeaderInfo = (Map) getContext().getProperty('channel').value()['requestHeaderInfo']
        requestHeaderInfo.each { String key, String value -> getRequestHolder().setNodeValue("//*:requestHeader//*:" + key + "", value) }
        getRequestHolder()["//*:clientTransactionId"] = getClientTransactionId()
        getRequestHolder()["//*[@key='SESSION_ID']/@value"] = getContext().getProperty("sessionId")
        getRequestHolder()["//*[@key='ALCS_SYSTEM']/@value"] = getContext().getProperty("troyaEnvironment")
        getRequestHolder()["//*[@key='CLIENT_IP']/@value"] = getIp()
        getRequestHolder().removeDomNodes('//*:requestHeader/*[not(text()) and local-name() != "extraParameters"]')
    }

    void cloneNodes(String nodePath, int iterationNumber) {
        iterationNumber.times {
            Node originDestinationOption = getRequestHolder().getDomNode("(" + nodePath + ")[last()]")
            originDestinationOption.getParentNode().insertBefore(originDestinationOption.cloneNode(true), originDestinationOption.getNextSibling())
        }
    }

}
