package com.thy.qa.td4p.requestcreationclasses

import com.eviware.soapui.support.XmlHolder

class CreateGetElectronicDocumentsRequest implements CreateRequest {

    CreateGetElectronicDocumentsRequest(context, testRunner, log) {
        this.context = context
        this.testRunner = testRunner
        this.log = log
    }

    void updateGetElectronicDocumentsRequest() {

        requestHolder = new XmlHolder(getRequestContent("getElectronicDocuments"))

        setRequestHeader()

        requestHolder["//*:AirReadRequest//*:Name/*:Surname"] = context["surname"]

        requestHolder["//*:AirReadRequest//*:TicketNumber/@TicketType"] = context["documentType"].value()

        requestHolder["//*:AirReadRequest//*:TicketNumber/@eTicketNumber"] = context["documentNumber"]

        context.getCurrentStep().setPropertyValue("Request", requestHolder.getPrettyXml())

        log.info("getElectronicDocuments request serialized.")
    }
}