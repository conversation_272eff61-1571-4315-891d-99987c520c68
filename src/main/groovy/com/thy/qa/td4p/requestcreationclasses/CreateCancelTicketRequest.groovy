package com.thy.qa.td4p.requestcreationclasses

import com.eviware.soapui.support.XmlHolder
import com.thy.qa.td4p.baseclasses.TestStepFinder
import com.thy.qa.td4p.enums.CorrelationID

class CreateCancelTicketRequest implements CreateRequest {

    TestStepFinder testStepFinder = new TestStepFinder(context)

    CreateCancelTicketRequest(context, testRunner, log) {
        this.context = context
        this.testRunner = testRunner
        this.log = log
    }

    void updateCancelTicketRequest(String pnrNumber, String surname, Map ticketPassengerMap) {
        requestHolder = new XmlHolder(getRequestContent('cancelTicket'))
        XmlHolder responseHolder = new XmlHolder(testStepFinder.findSuccessfulStepOfType('validateCancelTicket').getPropertyValue('Response'))

        setRequestHeader()
        setOTA_CancelRQNode(ticketPassengerMap)
        setticketBasedMoneyTaxItemsNodes(ticketPassengerMap, responseHolder)
        setCreateTicketOTARequestNode()

        context.getCurrentStep().setPropertyValue("Request", requestHolder.getPrettyXml())

        log.info("cancelTicket request serialized.")
    }

    private void setOTA_CancelRQNode(Map ticketPassengerMap) {
        requestHolder["//*:OTA_CancelRQ/@TransactionIdentifier"] = context["transactionIdentifier"]
        requestHolder["//*:OTA_CancelRQ/@CorrelationID"] = context["correlationID"]

        int totalTicketCount = ticketPassengerMap.size()

        cloneNodes("//*:OTA_CancelRQ//*:UniqueID[2]", totalTicketCount - 1)

        ticketPassengerMap.eachWithIndex { passengerRPH, ticketNumber, index ->
            requestHolder["//*:OTA_CancelRQ//*:UniqueID[" + (index + 2) + "]/@ID"] = ticketNumber
            requestHolder["//*:OTA_CancelRQ//*:UniqueID[" + (index + 2) + "]/@Type"] = "Ticket"
            requestHolder["//*:OTA_CancelRQ//*:UniqueID[" + (index + 2) + "]/@Instance"] = passengerRPH
        }

        requestHolder["//*:OTA_CancelRQ//*:UniqueID[@Type='Reservation']/@ID"] = context["pnr"]
        requestHolder["//*:OTA_CancelRQ//*:Verification//*:PersonName/*:Surname"] = context["surname"]

        setReasonAttribute()
        setCustLoyaltyNode()
    }

    private void setMoneyPenaltyNodeValues(def passengerRPH, XmlHolder responseHolder) {

        requestHolder["//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:money[1]//*:amount"] = responseHolder.getNodeValue("//*:OTA_CancelRS[" + passengerRPH + "]//*:CancelRule[@Type='Refund-Total']/@Amount")
        requestHolder["//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:money[1]//*:currency//*:code"] = responseHolder.getNodeValue("//*:OTA_CancelRS[" + passengerRPH + "]//*:CancelRule[@Type='Refund-Total']/@CurrencyCode")
        requestHolder["//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:money[2]//*:amount"] = responseHolder.getNodeValue("//*:OTA_CancelRS[" + passengerRPH + "]//*:CancelRule[@Type='Refund-Mile']/@Amount")
        requestHolder["//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:money[2]//*:currency//*:code"] = responseHolder.getNodeValue("//*:OTA_CancelRS[" + passengerRPH + "]//*:CancelRule[@Type='Refund-Mile']/@CurrencyCode")
        requestHolder["//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:penalty[1]//*:amount//*:amount"] = responseHolder.getNodeValue("//*:OTA_CancelRS[" + passengerRPH + "]//*:CancelRule[@Type='Charge-CancelPenalty']/@Amount")
        requestHolder["//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:penalty[1]//*:amount//*:currency//*:code"] = responseHolder.getNodeValue("//*:OTA_CancelRS[" + passengerRPH + "]//*:CancelRule[@Type='Charge-CancelPenalty']/@CurrencyCode")
        if (Double.parseDouble(responseHolder.getNodeValue("//*:OTA_CancelRS[" + passengerRPH + "]//*:CancelRule[@Type='Charge-Mile']/@Amount")) != 0) {
            cloneNodes("//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:penalty", 1)
            requestHolder["//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:penalty[2]//*:amount//*:amount"] = responseHolder.getNodeValue("//*:OTA_CancelRS[" + passengerRPH + "]//*:CancelRule[@Type='Charge-Mile']/@Amount")
            requestHolder["//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:penalty[2]//*:amount//*:currency//*:code"] = responseHolder.getNodeValue("//*:OTA_CancelRS[" + passengerRPH + "]//*:CancelRule[@Type='Charge-Mile']/@CurrencyCode")
        }
    }

    private void setTroyaEntryPaymentByOrderIdListNodeValues(def passengerRPH, XmlHolder responseHolder) {

        List troyaEntryNodeList = responseHolder.getNodeValues("//*:allocatePaybackOTAResponseList[" + passengerRPH + "]//*:troyaEntryList")
        List paymentTypesList = responseHolder.getNodeValues("//*:allocatePaybackOTAResponseList[" + passengerRPH + "]//*:PaymentDetail/@PaymentType")
        List orderIdList = responseHolder.getNodeValues("//*:allocatePaybackOTAResponseList[" + passengerRPH + "]//*:PaymentDetail//*:PaymentAmount/@ApprovalCode")
        List refundCalculationMethodsList = responseHolder.getNodeValues("//*:allocatePaybackOTAResponseList[" + passengerRPH + "]//*:PaymentDetail//*:PaymentAmount/@RefundCalcMethod")
        List amountsList = responseHolder.getNodeValues("//*:allocatePaybackOTAResponseList[" + passengerRPH + "]//*:PaymentDetail//*:PaymentAmount/@Amount")
        List currencyCodesList = responseHolder.getNodeValues("//*:allocatePaybackOTAResponseList[" + passengerRPH + "]//*:PaymentDetail//*:PaymentAmount/@CurrencyCode")

        cloneNodes("//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:troyaEntryList", (troyaEntryNodeList.size() - 1))
        cloneNodes("//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:paymentByOrderIdList", (troyaEntryNodeList.size() - 1))

        troyaEntryNodeList.eachWithIndex { troyaEntryList, troyaEntryListIndex ->

            requestHolder["//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:troyaEntryList[" + (troyaEntryListIndex + 1) + "]"] = troyaEntryNodeList[troyaEntryListIndex]

            requestHolder["//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:paymentByOrderIdList[" + (troyaEntryListIndex + 1) + "]//*:paymentOption"] = paymentTypesList[troyaEntryListIndex]
            requestHolder["//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:paymentByOrderIdList[" + (troyaEntryListIndex + 1) + "]//*:orderId"] = (orderIdList[troyaEntryListIndex] as String) + (refundCalculationMethodsList[troyaEntryListIndex] as String)

            if (paymentTypesList[troyaEntryListIndex] == "AWDTAX") {
                requestHolder["//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:paymentByOrderIdList[" + (troyaEntryListIndex + 1) + "]//*:amount//*:amount"] = "0"
                requestHolder["//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:paymentByOrderIdList[" + (troyaEntryListIndex + 1) + "]//*:amount//*:currency//*:code"] = currencyCodesList[troyaEntryListIndex]
                requestHolder["//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:paymentByOrderIdList[" + (troyaEntryListIndex + 1) + "]//*:milesTaxAmount//*:amount"] = amountsList[troyaEntryListIndex]
            } else {
                requestHolder["//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:paymentByOrderIdList[" + (troyaEntryListIndex + 1) + "]//*:amount//*:amount"] = amountsList[troyaEntryListIndex]
                requestHolder["//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:paymentByOrderIdList[" + (troyaEntryListIndex + 1) + "]//*:amount//*:currency//*:code"] = currencyCodesList[troyaEntryListIndex]
                requestHolder.removeDomNodes("//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:paymentByOrderIdList[" + (troyaEntryListIndex + 1) + "]//*:milesTaxAmount")
            }
        }
    }

    private void setticketBasedMoneyTaxItemsNodes(Map ticketPassengerMap, XmlHolder responseHolder) {
        int totalTicketCount = ticketPassengerMap.size()
        cloneNodes("//*:ticketBasedMoneyTaxItems", totalTicketCount - 1)

        ticketPassengerMap.eachWithIndex { passengerRPH, ticketNumber, index ->

            requestHolder["//*:ticketBasedMoneyTaxItems[" + passengerRPH + "]//*:ticketNumber"] = ticketNumber
            setMoneyPenaltyNodeValues(passengerRPH, responseHolder)

            if (context['correlationID'] == CorrelationID.OnlinePayback || context['correlationID'] == CorrelationID.RDLEMD) {
                setTroyaEntryPaymentByOrderIdListNodeValues(passengerRPH, responseHolder)
            }
        }
    }

    private void setCustLoyaltyNode() {
        if (context['isAwardPnr']) {
            List msNos = context['msNos']
            requestHolder["//*:OTA_CancelRQ//*:Verification//*:CustLoyalty/@MembershipID"] = msNos[0]
            context.setProperty('ticketType', "award")
        } else {
            requestHolder.removeDomNodes("//*:OTA_CancelRQ//*:Verification//*:CustLoyalty")
        }
    }

    private void setReasonAttribute() {
        if (context["refundReason"]) {
            requestHolder["//*:OTA_CancelRQ//*:UniqueID[@Type='Reservation']/@Reason"] = context["refundReason"]
        } else {
            requestHolder.getDomNode("//*:OTA_CancelRQ//*:UniqueID[1]").removeAttribute("Reason")
        }
    }

    private void setCreateTicketOTARequestNode() {
        CreatePaymentRequest createPaymentRequest = new CreatePaymentRequest(context, testRunner, log)
        if (!context['cancelTicketPenaltyChargeAmount']) {
            if (context['cancelTicketPenaltyChargeMileAmount']) {
                createPaymentRequest.setPaymentRequest(requestHolder, context['cancelTicketPenaltyChargeMileAmount'] as String, context['cancelTicketPenaltyChargeMileCurrencyCode'] as String, 'createTicketOTARequest')
            } else {
                requestHolder.removeDomNodes("//*:cancelTicketOTARequest//*:createTicketOTARequest")
            }
        } else {
            createPaymentRequest.setPaymentRequest(requestHolder, context['cancelTicketPenaltyChargeAmount'] as String, context['cancelTicketPenaltyChargeCurrencyCode'] as String, "createTicketOTARequest")
        }
    }
}