package com.thy.qa.td4p.requestcreationclasses

import com.eviware.soapui.support.XmlHolder

class CreateCWSSearchPassengerRequest implements CreateRequest {

    CreateCWSSearchPassengerRequest(context, testRunner, log) {
        this.context = context
        this.testRunner = testRunner
        this.log = log
    }

    void updateCWSSearchPassengerRequest() {
        requestHolder = new XmlHolder(getRequestContent("CheckinWS-searchPassenger"))
        String ctidHolder = getClientTransactionId()
        requestHolder["//*:clientTransactionId"] = ctidHolder
        requestHolder["//*:pnr"] = context["pnr"]
        requestHolder["//*:lastname"] = context["surname"]
        context.getCurrentStep().setPropertyValue("Request", requestHolder.getPrettyXml())
        log.info("searchPassenger request serialized with ClientTransactionId " + ctidHolder + " for reporting any issue or reviewing service call logs.")
    }

}
