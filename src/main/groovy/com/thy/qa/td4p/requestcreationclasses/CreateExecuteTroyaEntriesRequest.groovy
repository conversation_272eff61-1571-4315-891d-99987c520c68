package com.thy.qa.td4p.requestcreationclasses


import com.eviware.soapui.support.XmlHolder
import groovy.transform.TypeChecked

class CreateExecuteTroyaEntriesRequest implements CreateRequest {

    CreateExecuteTroyaEntriesRequest(context, testRunner, log) {
        this.context = context
        this.testRunner = testRunner
        this.log = log
    }

    void updateExecuteTroyaEntriesRequest() {
        context.getTestCase().getTestStepAt(context.getCurrentStepIndex()).setPropertyValue("Request", getExecuteTroyaEntriesRequest(context.entryList))
        log.info("executeTroyaEntries request serialized.")
    }

    @TypeChecked
    String getExecuteTroyaEntriesRequest(List<String> entryList) {

        requestHolder = new XmlHolder(getRequestContent("executeTroyaEntries"))

        setRequestHeader()

        cloneNodes("//*:ExecuteTroyaEntriesOTARequest/entryList", entryList.size() - 1)

        entryList.eachWithIndex { String entry, int index ->
            requestHolder.setNodeValue("(//*:ExecuteTroyaEntriesOTARequest/entryList)[" + (index + 1) + "]", entry)
        }

        requestHolder.getPrettyXml()
    }
}