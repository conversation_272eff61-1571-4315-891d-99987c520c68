package com.thy.qa.td4p.requestcreationclasses

import com.eviware.soapui.support.XmlHolder
import com.thy.qa.td4p.baseclasses.TestStepFinder

import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.ChronoUnit

class CreateInfantFareCalculationRequest implements CreateRequest {

    TestStepFinder testStepFinder = new TestStepFinder(context)
    LocalDate now = LocalDate.now(ZoneId.systemDefault())

    CreateInfantFareCalculationRequest(context, testRunner, log) {
        this.context = context
        this.testRunner = testRunner
        this.log = log
    }

    void updateInfantFareCalculationRequest(String pnrNumber, String surname, List passengerCodes, List seats, List nationalities) {
        requestHolder = new XmlHolder(getRequestContent("infantFareCalculation"))
        XmlHolder responseHolder = new XmlHolder(testStepFinder.findSuccessfulStepOfType('retrieveReservationDetail').getPropertyValue('Response'))

        setRequestHeader()

        CreateProfileRequest createProfileRequest = new CreateProfileRequest(context, testRunner, log)

        int OriginDestinationOptionCount = responseHolder.getNodeValue("count(//*:AirReservation//*:OriginDestinationOption)").toInteger()
        int LastFlightSegmentCount = responseHolder.getNodeValue("count(//*:AirReservation//*:OriginDestinationOption[" + OriginDestinationOptionCount + "]//*:FlightSegment)").toInteger()

        def FirstFlightDepartureDateTime = responseHolder.getNodeValue("//*:AirReservation//*:OriginDestinationOption[1]//*:FlightSegment[1]/@DepartureDateTime")
        def LastFlightDepartureDateTime = responseHolder.getNodeValue("//*:AirReservation//*:OriginDestinationOption[" + OriginDestinationOptionCount + "]//*:FlightSegment[" + LastFlightSegmentCount + "]/@DepartureDateTime")

        LocalDate FirstFlightDate = LocalDate.parse(FirstFlightDepartureDateTime[0..9].toString())
        LocalDate LastFlightDate = LocalDate.parse(LastFlightDepartureDateTime[0..9].toString())

        List daysToFlightsList = [now.until(FirstFlightDate, ChronoUnit.DAYS).toInteger(), now.until(LastFlightDate, ChronoUnit.DAYS).toInteger()]
        context.setProperty('daysToFlights', daysToFlightsList)

        int countOfPassenger = responseHolder.getNodeValue("count(//*:TravelerInfo//*:AirTraveler)").toInteger()

        int totalTravelerNumber = seats.sum()

        List passengerTypeCodes = context.getProperty('passengerCodes')*.name()
        List birthDates = context['birthDates']
        List names = context['names']
        List surnames = context['surnames']
        List tcNos = context['tcNos']

        cloneNodes("//*:TravelerInfo//*:AirTraveler", totalTravelerNumber - 1)

        Map infantAdditionRequestPropertiesMap = [:]

        for (int airTravelerIndex = 0; airTravelerIndex < totalTravelerNumber; airTravelerIndex++) {
            requestHolder['//*:AirTraveler[' + (airTravelerIndex + 1) + ']//*:UniqueID/@ID'] = (countOfPassenger + airTravelerIndex + 1)
            String passengerTypeCode = passengerTypeCodes[airTravelerIndex]
            String nationality = nationalities[airTravelerIndex]
            String birthDate = (birthDates[airTravelerIndex]) ?: createProfileRequest.getBirthDateByPassengerCode(passengerTypeCode)
            List paxInfo = createProfileRequest.getPaxInfo(passengerTypeCode)
            int InfantPassengerRequestedIndex = (countOfPassenger + airTravelerIndex + 1)
            List infantPassengerInfoList = [
                    birthDate,
                    paxInfo[1],
                    passengerTypeCode,
                    names[airTravelerIndex],
                    paxInfo[0],
                    surnames[airTravelerIndex],
                    tcNos[airTravelerIndex],
                    InfantPassengerRequestedIndex,
                    nationalities[airTravelerIndex]
            ]
            createProfileRequest.setAirTraveler(requestHolder, airTravelerIndex + 1, infantPassengerInfoList)

            infantAdditionRequestPropertiesMap.put(airTravelerIndex, infantPassengerInfoList)

            if (nationality == 'TR') {
                createProfileRequest.setIdentityNumberDocumentInfo(requestHolder, airTravelerIndex + 1, infantPassengerInfoList[6])
            } else {
                requestHolder.removeDomNodes("//*:AirTraveler[" + (airTravelerIndex + 1) + "]//*:PersonName//*:Document")
            }
        }
        context.setProperty('infantAdditionRequestPropertiesMap', infantAdditionRequestPropertiesMap)

        requestHolder['//*:OTA_AirBookRQ//*:BookingReferenceID/@ID'] = pnrNumber
        requestHolder['//*:OTA_AirBookRQ//*:Fulfillment//*:Name//*:Surname'] = surname

        testRunner.testCase.setPropertyValue('pnr', pnrNumber)
        testRunner.testCase.setPropertyValue('surname', surname)

        context.getCurrentStep().setPropertyValue("Request", requestHolder.getPrettyXml())

        log.info("infantFareCalculation request serialized.")
    }

}
