package com.thy.qa.td4p.requestcreationclasses

import com.eviware.soapui.support.XmlHolder
import com.thy.qa.td4p.TestCaseContext
import com.thy.qa.td4p.baseclasses.TestStepFinder
import com.thy.qa.td4p.passenger.contact.ContactInfo
import com.thy.qa.td4p.passenger.contact.CountryCode
import com.thy.qa.td4p.passenger.contact.MobilePhoneNumber
import org.apache.commons.lang3.StringUtils
import org.w3c.dom.Element
import org.w3c.dom.Node

import java.time.LocalDate
import java.time.format.DateTimeFormatter

class CreateCreateProfileRequest implements CreateRequest {

    TestStepFinder testStepFinder = new TestStepFinder(context)

    CreateProfileRequest createProfileRequest = new CreateProfileRequest(context, testRunner, log)

    ContactInfo contactInfo = context.contactInfo

    XmlHolder responseHolder = new XmlHolder(testStepFinder.findSuccessfulStepOfType('getFares').getPropertyValue('Response'))

    CreateCreateProfileRequest(context, testRunner, log) {
        this.context = context
        this.testRunner = testRunner
        this.log = log
    }

    void updateCreateProfileRequest(List seats, List nationalities, List msNos) {
        requestHolder = new XmlHolder(getRequestContent("createProfile"))

        setRequestHeader()

        requestHolder["//*:OTA_AirBookRQ/@TransactionIdentifier"] = responseHolder.getNodeValue("//*:OTA_AirBookRS/@TransactionIdentifier")

        int currentMsPassengerIndex = 0
        int totalTravelerNumber = seats.sum()
        List uniqueMsNos = msNos.unique(false).findAll()
        List passengerCodes = context.getProperty('passengerCodes')*.name()
        List birthDates = context['birthDates']
        List names = context['names'].collect { upperCaseWithoutAccents(it) }
        List surnames = context['surnames'].collect { upperCaseWithoutAccents(it) }
        List tcNos = context['tcNos']
        List jetGencNos = context['jetGencNos']
        List extraSeatCount = context.getProperty('pnr').passengers.collect { it.hasExtraSeat() ? 1 : 0 }
        List emails = context['emails']
        List mobilePhoneNumbers = context['mobilePhoneNumbers']

        cloneNodes("//*:TravelerInfo//*:AirTraveler", totalTravelerNumber - 1)

        for (int airTravelerIndex = 0; airTravelerIndex < totalTravelerNumber; airTravelerIndex++) {
            requestHolder['//*:AirTraveler[' + (airTravelerIndex + 1) + ']//*:UniqueID/@ID'] = (airTravelerIndex + 1)
            String passengerTypeCode = passengerCodes[airTravelerIndex]
            String nationality = nationalities[airTravelerIndex]
            String extraSeatQuantity = extraSeatCount[airTravelerIndex]
            if (msNos[airTravelerIndex] == '' && !(jetGencNos?.get(airTravelerIndex))) {
                String birthDate = (birthDates[airTravelerIndex]) ?: createProfileRequest.getBirthDateByPassengerCode(passengerTypeCode)
                List paxInfo = createProfileRequest.getPaxInfo(passengerTypeCode)
                createProfileRequest.setAirTraveler(requestHolder, airTravelerIndex + 1, [birthDate,
                                                                                          paxInfo[1],
                                                                                          passengerTypeCode,
                                                                                          names[airTravelerIndex],
                                                                                          paxInfo[0],
                                                                                          surnames[airTravelerIndex],
                                                                                          extraSeatQuantity])
                setPassengerContactInfo(airTravelerIndex + 1, emails[airTravelerIndex], mobilePhoneNumbers[airTravelerIndex])
            } else if (msNos[airTravelerIndex]) {
                XmlHolder msResponseHolder = new XmlHolder(testStepFinder.findSuccessfulStepsOfType('getMemberDetails')[currentMsPassengerIndex].getPropertyValue('Response'))
                String birthDate = LocalDate.parse(msResponseHolder.getNodeValue('//*:memberDataKVPair[*:key="D_OUT_BIRTH_DATE"]//value'), DateTimeFormatter.ofPattern('dd.MM.yyyy')).format('yyyy-MM-dd')
                String gender = msResponseHolder.getNodeValue('//*:memberDataKVPair[*:key="D_OUT_SEX"]//value')
                String namePrefix = msResponseHolder.getNodeValue('//*:memberDataKVPair[*:key="D_OUT_TITLE"]//value').take(2).toUpperCase()
                String name = msResponseHolder.getNodeValue('//*:memberDataKVPair[*:key="D_OUT_NAME"]//value')
                String surname = msResponseHolder.getNodeValue('//*:memberDataKVPair[*:key="D_OUT_SURNAME"]//value')
                nationality = msResponseHolder.getNodeValue('//*:memberDataKVPair[*:key="D_OUT_NATIONALITY"]//value')
                String email = msResponseHolder.getNodeValue('//*:memberDataKVPair[*:key="D_OUT_PRIMARY_EMAIL_ADDR"]//value')
                String countryCode = msResponseHolder.getNodeValue('//*:memberDataKVPair[*:key="D_OUT_GSM_TEL_COUNTRY_CODE"]//value')
                String phone = msResponseHolder.getNodeValue('//*:memberDataKVPair[*:key="D_OUT_GSM_TEL_PHONE"]//value')
                MobilePhoneNumber mobilePhoneNumber = phone ? new MobilePhoneNumber(CountryCode.forAccessCode(countryCode), Integer.parseInt(phone.take(3)), Long.parseLong(phone.drop(3))) : null
                String msNo = uniqueMsNos[currentMsPassengerIndex]
                String programID = msNo.take(2), membershipID = msNo.drop(2)
                createProfileRequest.setAirTraveler(requestHolder, airTravelerIndex + 1, [birthDate,
                                                                                          gender,
                                                                                          passengerTypeCode,
                                                                                          name,
                                                                                          namePrefix,
                                                                                          surname,
                                                                                          extraSeatQuantity])
                setPassengerContactInfo(airTravelerIndex + 1, email, mobilePhoneNumber)
                createProfileRequest.addCustLoyaltyNode(requestHolder, airTravelerIndex + 1, programID, membershipID)
                currentMsPassengerIndex++
            } else if (jetGencNos[airTravelerIndex]) {
                XmlHolder getJGmembershipInfoResponse = new XmlHolder(testStepFinder.findSuccessfulStepsOfType('getJGmembershipInfo')[currentMsPassengerIndex].getPropertyValue('Response'))
                String birthDate = LocalDate.parse(getJGmembershipInfoResponse.getNodeValue('//*:getJGmembershipInfoOTAResponse//*:JGmembershipInfoOTAResponseItem//*:BirthDate'), DateTimeFormatter.ofPattern('yyyy-MM-dd')).toString()
                String gender = getJGmembershipInfoResponse.getNodeValue('//*:getJGmembershipInfoOTAResponse//*:JGmembershipInfoOTAResponseItem//*:Gender')
                String namePrefix = (gender == 'M') ? 'MR' : 'MRS'
                String name = getJGmembershipInfoResponse.getNodeValue('//*:getJGmembershipInfoOTAResponse//*:JGmembershipInfoOTAResponseItem//*:PersonNameType//*:GivenName').toUpperCase()
                String surname = getJGmembershipInfoResponse.getNodeValue('//*:getJGmembershipInfoOTAResponse//*:JGmembershipInfoOTAResponseItem//*:PersonNameType//*:Surname').toUpperCase()
                String email = getJGmembershipInfoResponse.getNodeValue('//*:getJGmembershipInfoOTAResponse//*:JGmembershipInfoOTAResponseItem//*:EmailType/@Value')
                String phone = getJGmembershipInfoResponse.getNodeValue('//*:getJGmembershipInfoOTAResponse//*:JGmembershipInfoOTAResponseItem//*:Telephone/@PhoneNumber')
                MobilePhoneNumber mobilePhoneNumber = new MobilePhoneNumber(CountryCode.forCode('TR'), Integer.parseInt(phone.take(3)), Long.parseLong(phone.drop(3)))
                String membershipID = jetGencNos[airTravelerIndex]
                createProfileRequest.setAirTraveler(requestHolder, airTravelerIndex + 1, [birthDate,
                                                                                          gender,
                                                                                          passengerTypeCode,
                                                                                          name,
                                                                                          namePrefix,
                                                                                          surname,
                                                                                          extraSeatQuantity])
                setPassengerContactInfo(airTravelerIndex + 1, email, mobilePhoneNumber)
                createProfileRequest.addCustLoyaltyNode(requestHolder, airTravelerIndex + 1, 'JETGENC', membershipID)
                currentMsPassengerIndex++
            }

            if (nationality == 'TR') {
                createProfileRequest.setIdentityNumberDocumentInfo(requestHolder, airTravelerIndex + 1, tcNos[airTravelerIndex])
            } else {
                requestHolder.removeDomNodes("//*:AirTraveler[" + (airTravelerIndex + 1) + "]//*:PersonName//*:Document")
            }
        }

        if (passengerCodes.any { it == 'INFANT' }) {
            List accompaniedAirTravelers = retainAirTravelers(responseHolder.getDomNodes("//*:AirTraveler"), ['INFANT', 'CHILD', 'ATTENDANT', 'UNACCOMPANIED'])
            List accompaniedPassengersIndex = collectIDs(accompaniedAirTravelers)

            requestHolder.getDomNodes("//*:AirTraveler[@PassengerTypeCode = 'INFANT']").eachWithIndex { Node airTravelerNode, int index ->
                int accompaniedPassengerIndex = accompaniedPassengersIndex[index]
                Element element = airTravelerNode.getOwnerDocument().createElementNS(airTravelerNode.getNamespaceURI(), "" + airTravelerNode.getPrefix() + ":TravelerRefNumber")
                element.setAttribute("RPH", accompaniedPassengerIndex.toString())
                airTravelerNode.appendChild(element)
                int accompanyingPassengerIndex = context.pnr.passengers.findIndexOf { it.indexReference == accompaniedPassengerIndex }
                requestHolder["//*:AirTraveler[" + (accompanyingPassengerIndex + 1) + "]/@AccompaniedByInfantInd"] = 'true'
                context.pnr.passengers[accompanyingPassengerIndex].setAccompaniedByInfant(true)
            }
        }

        String surname = requestHolder.getNodeValue('//*:AirTraveler[1]//*:PersonName//*:Surname')
        testRunner.testCase.setPropertyValue('surname', surname)
        context.setProperty('surname', surname)

        setContactInfo(createProfileRequest)

        context.getCurrentStep().setPropertyValue("Request", requestHolder.getPrettyXml())

        log.info("createProfile request serialized.")
    }

    private void setPassengerContactInfo(int airTravelerIndex, String email, MobilePhoneNumber mobilePhoneNumber) {
        if (email) {
            createProfileRequest.setEmail(requestHolder, airTravelerIndex, email)
        }
        if (mobilePhoneNumber) {
            createProfileRequest.setTelephone(requestHolder, airTravelerIndex, mobilePhoneNumber)
        }
    }

    private void setContactInfo(CreateProfileRequest createProfileRequest) {
        if (!contactInfo) {
            String email = TestCaseContext.contactEmail()
            MobilePhoneNumber mobilePhoneNumber = TestCaseContext.contactMobilePhoneNumber()
            createProfileRequest.setContactInfoNode(requestHolder, new ContactInfo(0, 'Orçun Balcılar', email, mobilePhoneNumber))
        } else {
            int contactPersonIndex = contactInfo.passengerIndex
            if (!contactPersonIndex) {
                createProfileRequest.setContactInfoNode(requestHolder, contactInfo)
            } else {
                createProfileRequest.setContactInfoNode(requestHolder, contactInfo.withFullName(getFullName(contactPersonIndex)))
            }
        }
    }

    private List retainAirTravelers(Node[] airTravelers, List passengerTypeCodes) {
        airTravelers.findAll {
            String passengerTypeCode = it.getAttributes().getNamedItem("PassengerTypeCode").getNodeValue()
            !(passengerTypeCode in passengerTypeCodes)
        }
    }

    private List collectIDs(List airTravelers) {
        airTravelers.collect {
            Node profileRef = it.getChildNodes().find { it.getLocalName() == "ProfileRef" }
            profileRef.getChildNodes().item(1).getAttributes().getNamedItem("ID").getNodeValue().toInteger()
        }
    }

    private String getFullName(int passengerIndex) {
        String givenName = requestHolder['//*:AirTraveler[' + passengerIndex + ']//*:PersonName//*:GivenName']
        String surname = requestHolder['//*:AirTraveler[' + passengerIndex + ']//*:PersonName//*:Surname']
        return givenName + " " + surname
    }

    private String upperCaseWithoutAccents(String text) {
        return StringUtils.stripAccents(text).toUpperCase()
    }
}
