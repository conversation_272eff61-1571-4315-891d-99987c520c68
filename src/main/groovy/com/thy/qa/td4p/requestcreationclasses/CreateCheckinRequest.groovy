package com.thy.qa.td4p.requestcreationclasses

import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCaseRunner
import com.eviware.soapui.impl.wsdl.testcase.WsdlTestRunContext
import com.eviware.soapui.support.XmlHolder
import org.apache.logging.log4j.core.Logger

class CreateCheckinRequest {

    WsdlTestRunContext context
    WsdlTestCaseRunner testRunner
    Logger log

    CreateCheckinRequest(context, testRunner, log) {
        this.context = context
        this.testRunner = testRunner
        this.log = log
    }

    /** This method gets properties of a passenger as a List for given passengerIndex.
     * @param passengerIndex intended passenger index.
     * @param responseHolder should contain 'searchPassenger' services response.
     * @return [(0)AdultRPH, (1)Gender, (2)PassengerRPH, (3)TravelingWithInfant, (4)NamePrefix, (5)GivenName, (6)Surname, (7)PassengerTypeCode, (8)SeatNumber ]
     */
    List getPassengerProperties(int passengerIndex, XmlHolder responseHolder) {
        List passengerPropertiesList = []
        List passengerInfoBlockKeyList = ["/@AdultRPH", "/@Gender", "/@RPH", "/@TravelingWithInfant",
                                          "//*:PassengerName//*:NamePrefix", "//*:PassengerName//*:GivenName", "//*:PassengerName//*:Surname",
                                          "//*:PassengerTypeCode"]
        passengerInfoBlockKeyList.each { String key ->
            passengerPropertiesList.add(responseHolder.getNodeValue("//*:PassengerInfoList//*:PassengerInfo[" + passengerIndex + "]" + key + ""))
        }
        passengerPropertiesList.add(responseHolder.getNodeValue("//*:PassengerFlightInfoList//*:PassengerFlightInfo[@PassengerRPH=" + passengerIndex + "]//*:PaxIdentifier/@SeatNumber"))
        log.info("passengerIndex :" + passengerIndex + " : Name : " + passengerPropertiesList[5] + " Type : " + passengerPropertiesList[7])
        return passengerPropertiesList
    }

    /**
     * @param flightIndex
     * @param responseHolder should contain 'searchPassenger' services response.
     * @return List = [(0)IsReturnFlight, (1)FlightNumber, (2)MarketingAirlineCode, (3)flightRPH, (4)DepartureDate, (5)DepartureTime, (6)DepartureLocationCode,
     * (7)ArrivalDate, (8)ArrivalTime, (9)ArrivalLocationCode, (10)CabinCode, (11)IsStandBy, (12)DepartureLocalTime, (13)DepartureLocalDate, (14)DepartureDay ]
     */
    List getFlightProperties(int flightIndex, XmlHolder responseHolder) {
        List originDestionationAndSegmentIndexes = findOriginDestinationBlockAndSegmentIndexes(flightIndex, responseHolder)
        int originDestinationIndex = originDestionationAndSegmentIndexes[0].toInteger()
        int segmentIndex = originDestionationAndSegmentIndexes[1].toInteger()
        List flightPropertiesList = []
        String flightRPH = responseHolder.getNodeValue("//*:OriginDestinationList//*:OriginDestination[" + originDestinationIndex + "]//*:Segment[" + segmentIndex + "]/@RPH")
        flightPropertiesList.add(responseHolder.getNodeValue("//*:OriginDestinationList//*:OriginDestination[" + originDestinationIndex + "]/@IsReturnFlight"))
        flightPropertiesList.add(responseHolder.getNodeValue("//*:OriginDestinationList//*:OriginDestination[" + originDestinationIndex + "]//*:Segment[" + segmentIndex + "]/@FlightNumber"))
        flightPropertiesList.add(responseHolder.getNodeValue("//*:OriginDestinationList//*:OriginDestination[" + originDestinationIndex + "]//*:Segment[" + segmentIndex + "]/@MarketingAirlineCode"))
        flightPropertiesList.add(responseHolder.getNodeValue("//*:OriginDestinationList//*:OriginDestination[" + originDestinationIndex + "]//*:Segment[" + segmentIndex + "]/@RPH"))
        flightPropertiesList.add(responseHolder.getNodeValue("//*:OriginDestinationList//*:OriginDestination[" + originDestinationIndex + "]//*:Segment[" + segmentIndex + "]//*:DepartureInformation/@DepartureDate"))
        flightPropertiesList.add(responseHolder.getNodeValue("//*:OriginDestinationList//*:OriginDestination[" + originDestinationIndex + "]//*:Segment[" + segmentIndex + "]//*:DepartureInformation/@DepartureTime"))
        flightPropertiesList.add(responseHolder.getNodeValue("//*:OriginDestinationList//*:OriginDestination[" + originDestinationIndex + "]//*:Segment[" + segmentIndex + "]//*:DepartureInformation/@LocationCode"))
        flightPropertiesList.add(responseHolder.getNodeValue("//*:OriginDestinationList//*:OriginDestination[" + originDestinationIndex + "]//*:Segment[" + segmentIndex + "]//*:ArrivalInformation/@ArrivalDate"))
        flightPropertiesList.add(responseHolder.getNodeValue("//*:OriginDestinationList//*:OriginDestination[" + originDestinationIndex + "]//*:Segment[" + segmentIndex + "]//*:ArrivalInformation/@ArrivalTime"))
        flightPropertiesList.add(responseHolder.getNodeValue("//*:OriginDestinationList//*:OriginDestination[" + originDestinationIndex + "]//*:Segment[" + segmentIndex + "]//*:ArrivalInformation/@LocationCode"))
        flightPropertiesList.add(responseHolder.getNodeValue("//*:PassengerFlightInfoList//*:PassengerFlightInfo[@FlightRPH=" + flightRPH + "]/@CabinCode"))
        flightPropertiesList.add(responseHolder.getNodeValue("//*:PassengerFlightInfoList//*:PassengerFlightInfo[@FlightRPH=" + flightRPH + "]/@IsStandBy"))
        flightPropertiesList.add(responseHolder.getNodeValue("//*:OriginDestinationList//*:OriginDestination[" + originDestinationIndex + "]//*:Segment[" + segmentIndex + "]//*:DepartureInformation//*:LocalTime/@time"))
        flightPropertiesList.add(responseHolder.getNodeValue("//*:OriginDestinationList//*:OriginDestination[" + originDestinationIndex + "]//*:Segment[" + segmentIndex + "]//*:DepartureInformation//*:LocalTime/@date"))
        flightPropertiesList.add(responseHolder.getNodeValue("//*:OriginDestinationList//*:OriginDestination[" + originDestinationIndex + "]//*:Segment[" + segmentIndex + "]//*:DepartureInformation/@DepartureDay"))

        return flightPropertiesList
    }

    /**
     * @param flightIndex
     * @param passengerIndex
     * @param responseHolder should contain 'searchPassenger' services response.
     * @return passengerFlightPropertyList = []
     */
    List getPassengerFlightProperties(int flightIndex, int passengerIndex, XmlHolder responseHolder) {
        List passengerFlightPropertyList = []
        List bookingInfoBlockKeyList = ["/@FlightRPH", "/@PassengerRPH", "/@CabinCode", "//*:PaxIdentifier/@DCS_PassengerRefNumber", "//*:PaxIdentifier/@SeatNumber",
                                        "//*:BookingInfo/@BrandCode", "//*:BookingInfo/@ChargeableSeatType", "//*:BookingInfo/@HasChargeableExitSeat", "//*:BookingInfo/@HasChargeableSeat",
                                        "//*:BookingInfo/@HesCode", "//*:BookingInfo/@HesCodeIdLast3Digit", "//*:BookingInfo/@HesCodeExpiryDate", "//*:BookingInfo/@HesCodeStatus",
                                        "//*:BookingInfo/@ResBookDesigCode", "//*:BookingInfo/@SeatEMDSSRCode", "//*:BookingInfo/@SeatSelectionAllowed", "//*:BookingInfo/@Status",
                                        "//*:BookingInfo/@Type", "//*:Ticket/@CouponNumber", "//*:Ticket/@TicketNumber", "//*:Ticket/@TicketStatus", "//*:Ticket/@TicketType",
                                        "//*:FareBase/@BaggageAllowance", "//*:FareBase/@FareBase"]
        int passengerFlightInfoNodeIndex = [responseHolder.getNodeValues("//*:PassengerFlightInfoList//*:PassengerFlightInfo/@FlightRPH"), responseHolder.getNodeValues("//*:PassengerFlightInfoList//*:PassengerFlightInfo/@PassengerRPH")].transpose().findIndexOf { it == [flightIndex.toString(), passengerIndex.toString()] }
        bookingInfoBlockKeyList.each { key ->
            passengerFlightPropertyList.add(responseHolder.getNodeValue("//*:PassengerFlightInfoList//*:PassengerFlightInfo[" + (passengerFlightInfoNodeIndex + 1) + "]" + key + ""))
        }

        return passengerFlightPropertyList
    }

    /**
     * @param passengerIndex
     * @param responseHolder should contain 'retrieveReservationDetail' services response.
     * @return list = [(0)Name, (1)Surname, (2)NamePrefix, (3)BirthDate, (4)TCKN, (5)DocType, (6)Gender, (7)PassengerRPH, (8)PassengerType ]
     */
    List getAdditionalApisInfo(int passengerIndex, XmlHolder responseHolder) {
        List returnList = []

        returnList.add(responseHolder.getNodeValue("//*:AirTraveler[" + passengerIndex + "]//*:PersonName//*:GivenName"))
        returnList.add(responseHolder.getNodeValue("//*:AirTraveler[" + passengerIndex + "]//*:PersonName//*:Surname"))
        returnList.add(responseHolder.getNodeValue("//*:AirTraveler[" + passengerIndex + "]//*:PersonName//*:NamePrefix"))
        returnList.add(responseHolder.getNodeValue("//*:AirTraveler[" + passengerIndex + "]//*:Document[@Remark='DOCS']/@BirthDate")[0..9])
        returnList.add(responseHolder.getNodeValue("//*:AirTraveler[" + passengerIndex + "]//*:Document[@Remark='TCKN']/@DocID"))
        returnList.add(responseHolder.getNodeValue("//*:AirTraveler[" + passengerIndex + "]//*:Document/@DocType"))
        returnList.add(responseHolder.getNodeValue("//*:AirTraveler[" + passengerIndex + "]//*:Document/@Gender"))
        returnList.add(responseHolder.getNodeValue("//*:AirTraveler[" + passengerIndex + "]//*:ProfileRef//*:UniqueID/@ID"))
        returnList.add(responseHolder.getNodeValue("//*:AirTraveler[" + passengerIndex + "]//*:ProfileRef//*:UniqueID/@Type"))

        return returnList
    }

    List findOriginDestinationBlockAndSegmentIndexes(int flightIndex, XmlHolder responseHolder) {
        List originDestinationAndSegmentIndexes = [1, 1]
        List flightNumbers = responseHolder.getNodeValues("//*:OriginDestinationList//*:Segment/@FlightNumber")

        String selectedFlightNumber

        int originDestinationBlockCount = responseHolder.getNodeValue("count(//*:OriginDestinationList//*:OriginDestination)").toInteger()
        for (int originDestinatinationBlockIndex = 1; originDestinatinationBlockIndex <= originDestinationBlockCount; originDestinatinationBlockIndex++) {

            int segmentBlockCount = responseHolder.getNodeValue("count(//*:OriginDestinationList//*:OriginDestination[" + originDestinatinationBlockIndex + "]//*:Segment)").toInteger()
            for (int segmentIndex = 1; segmentIndex <= segmentBlockCount; segmentIndex++) {
                selectedFlightNumber = responseHolder.getNodeValue("//*:OriginDestinationList//*:OriginDestination[" + originDestinatinationBlockIndex + "]//*:Segment[" + segmentIndex + "]/@FlightNumber")
                if (selectedFlightNumber == flightNumbers[(flightIndex - 1)]) {
                    originDestinationAndSegmentIndexes[0] = originDestinatinationBlockIndex
                    originDestinationAndSegmentIndexes[1] = segmentIndex
                    break
                }
            }
            if (selectedFlightNumber != null && selectedFlightNumber == flightNumbers[(flightIndex - 1)])
                break
        }
        return originDestinationAndSegmentIndexes
    }

}
