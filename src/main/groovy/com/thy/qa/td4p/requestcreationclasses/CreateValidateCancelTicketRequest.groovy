package com.thy.qa.td4p.requestcreationclasses

import com.eviware.soapui.support.XmlHolder
import com.thy.qa.td4p.baseclasses.TestStepFinder
import com.thy.qa.td4p.enums.CorrelationID

class CreateValidateCancelTicketRequest implements CreateRequest {

    TestStepFinder testStepFinder = new TestStepFinder(context)

    CreateValidateCancelTicketRequest(context, testRunner, log) {
        this.context = context
        this.testRunner = testRunner
        this.log = log
    }

    void updateValidateCancelTicketRequest(CorrelationID correlationID) {
        requestHolder = new XmlHolder(getRequestContent("validateCancelTicket"))
        XmlHolder responseHolder = new XmlHolder(testStepFinder.findSuccessfulStepOfType('retrieveReservationDetail').getPropertyValue('Response'))

        Map ticketPassengerMap = [:]
        List ticketPassengerRPHList = responseHolder.getNodeValues("//*:OTA_AirBookRS//*:Ticketing//@TravelerRefNumber")
        ticketPassengerRPHList.eachWithIndex { it, index -> ticketPassengerMap.put(responseHolder.getNodeValue("//*:Ticketing[" + (index + 1) + "]//@TravelerRefNumber"), responseHolder.getNodeValue("//*:Ticketing[" + (index + 1) + "]//@TicketDocumentNbr")) }
        context.setProperty('ticketPassengerMap', ticketPassengerMap)

        boolean isAwardPnr = (responseHolder.getNodeValue("//*:retrieveReservationOTAResponse//*:reservationInfoOTAResponse//*:isAward") == "true") ? true : false
        context.setProperty('isAwardPnr', isAwardPnr)

        setRequestHeader()
        setOTA_CancelRQNode(ticketPassengerMap, responseHolder)

        testRunner.testCase.setPropertyValue('correlationID', correlationID.toString())
        context.getCurrentStep().setPropertyValue("Request", requestHolder.getPrettyXml())

        log.info('validateCancelTicket request serialized.')
    }

    private void setOTA_CancelRQNode(Map ticketPassengerMap, XmlHolder responseHolder) {
        requestHolder["//*:OTA_CancelRQ/@TransactionIdentifier"] = context["transactionIdentifier"]
        int totalTicketCount = ticketPassengerMap.size()
        cloneNodes("//*:OTA_CancelRQ//*:UniqueID[2]", totalTicketCount - 1)

        ticketPassengerMap.eachWithIndex { passengerRPH, ticketNumber, index ->
            requestHolder["//*:OTA_CancelRQ//*:UniqueID[" + (index + 2) + "]/@ID"] = ticketNumber
            requestHolder["//*:OTA_CancelRQ//*:UniqueID[" + (index + 2) + "]/@Type"] = "Ticket"
            requestHolder["//*:OTA_CancelRQ//*:UniqueID[" + (index + 2) + "]/@Instance"] = passengerRPH
        }
        requestHolder["//*:OTA_CancelRQ//*:UniqueID[@Type='Reservation']/@ID"] = context["pnr"]
        requestHolder["//*:OTA_CancelRQ//*:Verification//*:PersonName/*:Surname"] = context["surname"]

        setReasonAttribute()
        setCustLoyaltyNode(responseHolder)
    }

    private void setReasonAttribute() {
        if (context["refundReason"]) {
            requestHolder["//*:OTA_CancelRQ//*:UniqueID[@Type='Reservation']/@Reason"] = context["refundReason"]
        } else {
            requestHolder.getDomNode("//*:OTA_CancelRQ//*:UniqueID[1]").removeAttribute("Reason")
        }
    }

    private void setCustLoyaltyNode(XmlHolder responseHolder) {
        if (context['isAwardPnr']) {
            List msNos = responseHolder.getNodeValues("//*:TravelerInfo//*:AirTraveler//*:CustLoyalty//@MembershipID")
            context.setProperty('msNos', msNos)
            requestHolder["//*:OTA_CancelRQ//*:Verification//*:CustLoyalty/@MembershipID"] = msNos[0]
            context.setProperty('ticketType', "award")
        } else {
            requestHolder.removeDomNodes("//*:OTA_CancelRQ//*:Verification//*:CustLoyalty")
        }
    }
}