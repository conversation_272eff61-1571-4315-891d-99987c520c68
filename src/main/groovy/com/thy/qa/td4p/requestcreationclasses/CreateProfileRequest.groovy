package com.thy.qa.td4p.requestcreationclasses

import com.eviware.soapui.support.XmlHolder
import com.thy.qa.td4p.baseclasses.TestStepFinder
import com.thy.qa.td4p.passenger.contact.ContactInfo
import com.thy.qa.td4p.passenger.contact.CountryCode
import com.thy.qa.td4p.passenger.contact.MobilePhoneNumber
import org.w3c.dom.Element
import org.w3c.dom.Node

import java.time.LocalDate
import java.time.Period
import java.time.ZoneId
import java.time.temporal.ChronoUnit

class CreateProfileRequest implements CreateRequest {

    TestStepFinder testStepFinder = new TestStepFinder(context)
    LocalDate now = LocalDate.now(ZoneId.systemDefault())

    CreateProfileRequest(context, testRunner, log) {
        this.context = context
        this.testRunner = testRunner
        this.log = log
    }

    protected void setContactInfoNode(XmlHolder requestHolder, ContactInfo contactInfo) {
        requestHolder['//*:TK_ContactInfo//*:PassengerIndex'] = contactInfo.passengerIndex ?: ''
        requestHolder['//*:TK_ContactInfo//*:Name'] = contactInfo.fullName
        requestHolder['//*:TK_ContactInfo//*:Email'] = contactInfo.email
        Node telephone = requestHolder.getDomNode("//*:TK_ContactInfo//*:Telephone")
        MobilePhoneNumber mobilePhoneNumber = contactInfo.mobilePhoneNumber
        CountryCode countryCode = mobilePhoneNumber.countryCode()
        telephone.setAttribute("PhoneLocationType", countryCode.code)
        telephone.setAttribute("CountryAccessCode", countryCode.countryAccessCode)
        telephone.setAttribute("AreaCityCode", mobilePhoneNumber.areaCode().toString())
        telephone.setAttribute("PhoneNumber", mobilePhoneNumber.phoneNumber().toString())
    }

    private void addCustLoyaltyNode(XmlHolder requestHolder, int airTravelerIndex, String programID, String membershipID) {
        Node airTravelerNode = requestHolder.getDomNode("//*:AirTraveler[" + airTravelerIndex + "]")
        Element custLoyaltyNode = airTravelerNode.getOwnerDocument().createElementNS(airTravelerNode.getNamespaceURI(), "" + airTravelerNode.getPrefix() + ":CustLoyalty")
        custLoyaltyNode.setAttribute("MembershipID", membershipID)
        custLoyaltyNode.setAttribute("ProgramID", programID)
        airTravelerNode.appendChild(custLoyaltyNode)
    }

    private void setAirTraveler(XmlHolder requestHolder, int airTravelerIndex, List passengerInfo) {
        requestHolder["//*:AirTraveler[" + airTravelerIndex + "]/@BirthDate"] = passengerInfo[0]
        requestHolder["//*:AirTraveler[" + airTravelerIndex + "]/@Gender"] = passengerInfo[1]
        requestHolder["//*:AirTraveler[" + airTravelerIndex + "]/@PassengerTypeCode"] = passengerInfo[2]
        requestHolder["//*:AirTraveler[" + airTravelerIndex + "]//*:PersonName//*:GivenName"] = passengerInfo[3]
        requestHolder["//*:AirTraveler[" + airTravelerIndex + "]//*:PersonName//*:NamePrefix"] = passengerInfo[4]
        requestHolder["//*:AirTraveler[" + airTravelerIndex + "]//*:PersonName//*:Surname"] = passengerInfo[5]
        if (passengerInfo[6] != "0") {
            Node airTraveler = requestHolder.getDomNode("//*:AirTraveler[" + airTravelerIndex + "]")
            Element passengerTypeQuantityNode = airTraveler.getOwnerDocument().createElementNS(airTraveler.getNamespaceURI(), "PassengerTypeQuantity")
            passengerTypeQuantityNode.with {
                setAttribute("Quantity", passengerInfo[6])
                setAttribute("CodeContext", "EXST")
            }
            airTraveler.appendChild(passengerTypeQuantityNode)
        }
    }

    void setEmail(XmlHolder requestHolder, int airTravelerIndex, String email) {
        Node airTraveler = requestHolder.getDomNode("//*:AirTraveler[" + airTravelerIndex + "]")
        Element emailNode = airTraveler.getOwnerDocument().createElementNS(airTraveler.getNamespaceURI(), "Email")
        emailNode.setAttribute("Value", email)
        airTraveler.appendChild(emailNode)
    }

    void setTelephone(XmlHolder requestHolder, int airTravelerIndex, MobilePhoneNumber mobilePhoneNumber) {
        Node airTraveler = requestHolder.getDomNode("//*:AirTraveler[" + airTravelerIndex + "]")
        Element telephone = airTraveler.getOwnerDocument().createElementNS(airTraveler.getNamespaceURI(), "Telephone")
        CountryCode countryCode = mobilePhoneNumber.countryCode()
        telephone.setAttribute("PhoneUseType", "M")
        telephone.setAttribute("PhoneLocationType", countryCode.code)
        telephone.setAttribute("CountryAccessCode", countryCode.countryAccessCode)
        telephone.setAttribute("AreaCityCode", mobilePhoneNumber.areaCode().toString())
        telephone.setAttribute("PhoneNumber", mobilePhoneNumber.phoneNumber().toString())
        telephone.setAttribute("Remark", "SMS:TR")
        airTraveler.appendChild(telephone)
    }

    private void setIdentityNumberDocumentInfo(XmlHolder requestHolder, int airTravelerIndex, String tcNo) {
        requestHolder["//*:AirTraveler[" + airTravelerIndex + "]//*:PersonName//*:Document/@DocID"] = tcNo
    }

    private void setDocumentInfo(XmlHolder requestHolder, int airTravelerIndex) {
        requestHolder["//*:AirTraveler[" + airTravelerIndex + "]//*:PersonName//*:Document/@DocID"] = '0'
        requestHolder["//*:AirTraveler[" + airTravelerIndex + "]//*:PersonName//*:Document/@DocType"] = "P"
        Node airTravelerNode = requestHolder.getDomNode("//*:AirTraveler[" + airTravelerIndex + "]")
        Node docsNode = requestHolder.getDomNode("//*:AirTraveler[" + airTravelerIndex + "]//*:PersonName//*:Document")
        docsNode.setAttribute("DocIssueCountry", '')
        docsNode.setAttribute("DocHolderNationality", '')
        docsNode.setAttribute("Remark", "DOCS")
        airTravelerNode.appendChild(docsNode)
    }

    private List getPaxInfo(String paxType) {
        String title = ''
        if (paxType in [
                'CHILD',
                'INFANT',
                'UNACCOMPANIED'
        ]) {
            title = ['MSTR', 'MISS'].shuffled().get(0)
        } else {
            title = ['MR', 'MS', 'MRS'].shuffled().get(0)
        }
        return [title, getGender(title)]
    }

    private String getBirthDateByPassengerCode(String passengerCode) {
        if (passengerCode != 'INFANT') {
            List range = getBirthDateRange(passengerCode)
            LocalDate firstFlightDate = now + context['daysToFlights'][0]
            LocalDate startDateInclusive = firstFlightDate - Period.ofYears(range[0])
            LocalDate endDateExclusive = firstFlightDate - Period.ofYears(range[1])
            getBirtDate(startDateInclusive, endDateExclusive)
        } else {
            getBirtDate(now - 8, now + context['daysToFlights'][-1] - Period.ofYears(2))
        }
    }

    private String getBirtDate(LocalDate startDateInclusive, LocalDate endDateExclusive) {
        List days = (0..endDateExclusive.plusDays(1).until(startDateInclusive, ChronoUnit.DAYS)).collect()
        startDateInclusive.minusDays(days.shuffled().first()).toString()
    }

    private String getGender(String title) {
        switch (title) {
            case ['MS', 'MRS', 'MISS']:
                return 'F'
                break
            case ['MR', 'MSTR']:
                return 'M'
                break
        }
    }

    private List getBirthDateRange(String passengerTypeCode) {
        switch (passengerTypeCode) {
            case [
                    'ADULT',
                    'DISABLED',
                    'SOLDIER',
                    'COMPANION'
            ]:
                return [12, 65]
                break
            case 'CHILD':
                return [2, 12]
                break
            case 'UNACCOMPANIED':
                return [7, 11]
                break
            case 'YOUTH':
                return [12, 30]
                break
            case 'TEACHER':
                return [12, 80]
                break
            case 'STUDENT':
                return [12, 35]
                break
            case 'SENIOR':
                return [65, 99]
                break
            case 'ATTENDANT':
                return [20, 80]
                break
            case 'VETERAN':
                return [20, 99]
                break
        }
    }
}
