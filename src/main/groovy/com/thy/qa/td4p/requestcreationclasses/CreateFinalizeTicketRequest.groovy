package com.thy.qa.td4p.requestcreationclasses

import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCaseRunner
import com.eviware.soapui.impl.wsdl.testcase.WsdlTestRunContext
import com.eviware.soapui.support.XmlHolder
import com.thy.qa.td4p.Pnr
import com.thy.qa.td4p.enums.Channel
import groovy.transform.CompileStatic
import org.apache.logging.log4j.core.Logger

@CompileStatic
class CreateFinalizeTicketRequest implements CreateRequest {

    CreateFinalizeTicketRequest(WsdlTestRunContext context, WsdlTestCaseRunner testRunner, Logger log) {
        this.context = context
        this.testRunner = testRunner
        this.log = log
    }

    void updateFinalizeTicketRequest() {
        requestHolder = new XmlHolder(getRequestContent("finalizeTicket"))

        setRequestHeader()

        String pnrNumber = ((Pnr) context.getProperty('pnr')).pnrNumber

        requestHolder["//*:BookingReferenceID/@ID"] = pnrNumber
        requestHolder["//*:PassengerName//*:Surname"] = context.getProperty('surname')

        context.getCurrentStep().setPropertyValue("Request", requestHolder.getPrettyXml())

        log.info("finalizeTicket request serialized.")
    }

    @Override
    void setRequestHeader() {
        Channel channel = (Channel) context.getProperty('channel')
        Map requestHeaderInfo = channel.value()["requestHeaderInfo"]
        requestHolder["//*:requestHeader//*:clientCode"] = requestHeaderInfo["clientCode"]
        requestHolder["//*:requestHeader//*:clientUsername"] = requestHeaderInfo["clientUsername"]
        requestHolder["//*:requestHeader//*:channel"] = "BATCH"
        requestHolder["//*:requestHeader//*:application"] = "BATCH"
        requestHolder["//*:requestHeader//*:clientTransactionId"] = getClientTransactionId()
        requestHolder["//*:requestHeader//*[@key='SESSION_ID']/@value"] = context.getProperty("sessionId")
        requestHolder["//*:requestHeader//*[@key='ALCS_SYSTEM']/@value"] = context.getProperty("troyaEnvironment")
    }
}