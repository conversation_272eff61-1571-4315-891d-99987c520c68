package com.thy.qa.td4p.requestcreationclasses

import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCaseRunner
import com.eviware.soapui.impl.wsdl.testcase.WsdlTestRunContext
import com.eviware.soapui.support.XmlHolder
import com.thy.qa.td4p.baseclasses.TestStepFinder
import org.apache.logging.log4j.core.Logger

class CreateGetMemberDetailsRequest implements CreateRequest {

    private TestStepFinder testStepFinder = new TestStepFinder(context)

    CreateGetMemberDetailsRequest(WsdlTestRunContext context, WsdlTestCaseRunner testRunner, Logger log) {
        this.context = context
        this.testRunner = testRunner
        this.log = log
    }

    void updateGetMemberDetailsRequest() {
        requestHolder = new XmlHolder(getRequestContent("getMemberDetails"))
        setRequestHeader()
        List msNos = context.getProperty("msNos")
        List uniqueMsNos = msNos.unique(false).findAll()
        requestHolder["//*:getMemberDetails//*:getMemberDetailsDetail//*:ffid"] = uniqueMsNos[testStepFinder.countPreviousSuccessfulStepsOfType("getMemberDetails")]
        context.getCurrentStep().setPropertyValue("Request", requestHolder.getPrettyXml())
        log.info("getMemberDetails request serialized.")
    }

    @Override
    void setRequestHeader() {
        requestHolder["//*:requestHeader//*:clientTransactionId"] = getClientTransactionId()
        requestHolder["//*:requestHeader//*[@key='SESSION_ID']/@value"] = context.getProperty("sessionId")
        requestHolder["//*:requestHeader//*[@key='ALCS_SYSTEM']/@value"] = context.getProperty("troyaEnvironment")
        requestHolder["//*:requestHeader//*[@key='CLIENT_IP']/@value"] = getIp()
        if (context.getProperty('channel').value()["application"] == "SMARTMOBILE") {
            requestHolder["//*:requestHeader//*:clientCode"] = "SMART_MOB"
            requestHolder["//*:requestHeader//*:clientUsername"] = "SMART_MOB"
            requestHolder["//*:requestHeader//*:channel"] = "MOBILE"
            requestHolder["//*:requestHeader//*:application"] = "TKMILES"
        } else {
            requestHolder["//*:requestHeader//*:clientCode"] = "WEB3"
            requestHolder["//*:requestHeader//*:clientUsername"] = "WEB3"
            requestHolder["//*:requestHeader//*:channel"] = "WEB"
            requestHolder["//*:requestHeader//*:application"] = "IBE"
        }
    }
}