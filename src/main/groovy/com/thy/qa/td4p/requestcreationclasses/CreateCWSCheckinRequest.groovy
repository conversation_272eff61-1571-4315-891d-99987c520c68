package com.thy.qa.td4p.requestcreationclasses

import com.eviware.soapui.support.XmlHolder
import com.thy.qa.td4p.baseclasses.TestStepFinder

class CreateCWSCheckinRequest implements CreateRequest {

    TestStepFinder testStepFinder = new TestStepFinder(context)

    CreateCWSCheckinRequest(context, testRunner, log) {
        this.context = context
        this.testRunner = testRunner
        this.log = log
    }

    void updateCWSCheckinRequest() {
        requestHolder = new XmlHolder(getRequestContent("CheckinWS-checkin"))
        XmlHolder responseHolder = new XmlHolder(testStepFinder.findSuccessfulStepOfType('searchPassenger').getPropertyValue('Response'))

        CreateCheckinRequest createCheckinRequest = new CreateCheckinRequest(context, testRunner, log)

        String ctidHolder = getClientTransactionId()
        requestHolder["//*:clientTransactionId"] = ctidHolder

        List checkinPassengerIndexList = context['checkinPassengerIndexes']
        int checkinFlightIndex = context['checkinFlightIndex']

        cloneNodes("//*:PassengerInfoList//*:PassengerInfo", checkinPassengerIndexList.size() - 1)
        cloneNodes("//*:PassengerFlightInfoList//*:PassengerFlightInfo", checkinPassengerIndexList.size() - 1)

        List flightPropertiesList = createCheckinRequest.getFlightProperties(checkinFlightIndex, responseHolder)
        setSegmentNodeValues(1, flightPropertiesList)

        requestHolder["//*:TripData/@PnrCode"] = context['pnr'].pnrNumber
        requestHolder["//*:OriginDestinationList//*:OriginDestination/@IsReturnFlight"] = flightPropertiesList[0]

        checkinPassengerIndexList.each { passengerIndex ->
            List passengerPropertiesList = createCheckinRequest.getPassengerProperties(passengerIndex, responseHolder)
            setPassengerInfoListNodeValues(passengerIndex, passengerPropertiesList)
            setPassengerFlightInfoNodeValues(passengerIndex, flightPropertiesList, passengerPropertiesList)
        }

        context.currentStep.setPropertyValue("Request", requestHolder.getPrettyXml())

        log.info("checkin request serialized with ClientTransactionId " + ctidHolder + " for reporting any issue or reviewing service call logs.")
    }

    /**
     *
     * @param nodeIndex PassengerInfo nodes index
     * @param passengerPropertyList = [(0)AdultRPH, (1)Gender, (2)PassengerRPH, (3)TravelingWithInfant, (4)NamePrefix, (5)GivenName, (6)Surname, (7)PassengerTypeCode, (8)SeatNumber ]
     */
    private void setPassengerInfoListNodeValues(int passengerNodeIndex, List passengerPropertyList) {
        requestHolder["//*:PassengerInfoList//*:PassengerInfo[" + passengerNodeIndex + "]/@AdultRPH"] = passengerPropertyList[0]
        requestHolder["//*:PassengerInfoList//*:PassengerInfo[" + passengerNodeIndex + "]/@Gender"] = passengerPropertyList[1]
        requestHolder["//*:PassengerInfoList//*:PassengerInfo[" + passengerNodeIndex + "]/@RPH"] = passengerPropertyList[2]
        requestHolder["//*:PassengerInfoList//*:PassengerInfo[" + passengerNodeIndex + "]/@TravelingWithInfant"] = passengerPropertyList[3]
        requestHolder["//*:PassengerInfoList//*:PassengerInfo[" + passengerNodeIndex + "]//*:PassengerName//*:NamePrefix"] = passengerPropertyList[4]
        requestHolder["//*:PassengerInfoList//*:PassengerInfo[" + passengerNodeIndex + "]//*:PassengerName//*:GivenName"] = passengerPropertyList[5]
        requestHolder["//*:PassengerInfoList//*:PassengerInfo[" + passengerNodeIndex + "]//*:PassengerName//*:Surname"] = passengerPropertyList[6]
        requestHolder["//*:PassengerInfoList//*:PassengerInfo[" + passengerNodeIndex + "]//*:PassengerTypeCode"] = passengerPropertyList[7]
    }

    /**
     *
     * @param nodeIndex
     * @param flightPropertyList = [(0)IsReturnFlight, (1)FlightNumber, (2)MarketingAirlineCode, (3)flightRPH, (4)DepartureDate, (5)DepartureTime,
     * (6)DepartureLocationCode, (7)ArrivalDate, (8)ArrivalTime, (9)ArrivalLocationCode, (10)CabinCode, (11)IsStandBy ]
     */
    private void setSegmentNodeValues(int nodeIndex, List flightPropertyList) {
        requestHolder["//*:OriginDestination//*:Segment[" + nodeIndex + "]/@FlightNumber"] = flightPropertyList[1]
        requestHolder["//*:OriginDestination//*:Segment[" + nodeIndex + "]/@MarketingAirlineCode"] = flightPropertyList[2]
        requestHolder["//*:OriginDestination//*:Segment[" + nodeIndex + "]/@RPH"] = flightPropertyList[3]
        requestHolder["//*:OriginDestination//*:Segment[" + nodeIndex + "]//*:DepartureInformation/@DepartureDate"] = flightPropertyList[4]
        requestHolder["//*:OriginDestination//*:Segment[" + nodeIndex + "]//*:DepartureInformation/@DepartureTime"] = flightPropertyList[5]
        requestHolder["//*:OriginDestination//*:Segment[" + nodeIndex + "]//*:DepartureInformation/@LocationCode"] = flightPropertyList[6]
        requestHolder["//*:OriginDestination//*:Segment[" + nodeIndex + "]//*:ArrivalInformation/@ArrivalDate"] = flightPropertyList[7]
        requestHolder["//*:OriginDestination//*:Segment[" + nodeIndex + "]//*:ArrivalInformation/@ArrivalTime"] = flightPropertyList[8]
        requestHolder["//*:OriginDestination//*:Segment[" + nodeIndex + "]//*:ArrivalInformation/@LocationCode"] = flightPropertyList[9]
    }

    /**
     *
     * @param nodeIndex
     * @param flightPropertyList = [(0)IsReturnFlight, (1)FlightNumber, (2)MarketingAirlineCode, (3)flightRPH, (4)DepartureDate, (5)DepartureTime,
     * (6)DepartureLocationCode, (7)ArrivalDate, (8)ArrivalTime, (9)ArrivalLocationCode, (10)CabinCode, (11)IsStandBy ]
     * @param passengerPropertyList = [(0)AdultRPH, (1)Gender, (2)PassengerRPH, (3)TravelingWithInfant, (4)NamePrefix, (5)GivenName, (6)Surname, (7)PassengerTypeCode, (8)SeatNumber ]
     */
    private void setPassengerFlightInfoNodeValues(int passengerNodeIndex, List flightPropertyList, List passengerPropertyList) {
        requestHolder["//*:PassengerFlightInfoList//*:PassengerFlightInfo[" + passengerNodeIndex + "]/@CabinCode"] = flightPropertyList[10]
        requestHolder["//*:PassengerFlightInfoList//*:PassengerFlightInfo[" + passengerNodeIndex + "]/@FlightRPH"] = flightPropertyList[3]
        requestHolder["//*:PassengerFlightInfoList//*:PassengerFlightInfo[" + passengerNodeIndex + "]/@IsStandBy"] = flightPropertyList[11]
        requestHolder["//*:PassengerFlightInfoList//*:PassengerFlightInfo[" + passengerNodeIndex + "]/@PassengerRPH"] = passengerPropertyList[2]
        requestHolder["//*:PassengerFlightInfoList//*:PassengerFlightInfo[" + passengerNodeIndex + "]//*:PaxIdentifier/@SeatNumber"] = passengerPropertyList[8]
    }

}
