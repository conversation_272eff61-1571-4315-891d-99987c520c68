package com.thy.qa.td4p.requestcreationclasses

import com.eviware.soapui.support.XmlHolder

class CreateInfantAdditionRequest implements CreateRequest {

    CreateInfantAdditionRequest(context, testRunner, log) {
        this.context = context
        this.testRunner = testRunner
        this.log = log
    }

    void updateInfantAdditionRequest(String pnrNumber, String surname, List passengerCodes, List seats, List nationalities) {
        requestHolder = new XmlHolder(getRequestContent("infantAddition"))

        setRequestHeader()

        CreateProfileRequest createProfileRequest = new CreateProfileRequest(context, testRunner, log)

        def infantAdditionRequestPropertiesMap = context["infantAdditionRequestPropertiesMap"]

        int totalTravelerNumber = seats.sum()

        cloneNodes("//*:TravelerInfo//*:AirTraveler", totalTravelerNumber - 1)

        for (int airTravelerIndex = 0; airTravelerIndex < totalTravelerNumber; airTravelerIndex++) {
            List infantPassengerInfoList = infantAdditionRequestPropertiesMap[airTravelerIndex]
            String nationality = infantPassengerInfoList[8].toString()
            requestHolder['//*:AirTraveler[' + (airTravelerIndex + 1) + ']//*:UniqueID/@ID'] = infantPassengerInfoList[7]
            createProfileRequest.setAirTraveler(requestHolder, airTravelerIndex + 1, infantPassengerInfoList)

            if (nationality == 'TR') {
                createProfileRequest.setIdentityNumberDocumentInfo(requestHolder, airTravelerIndex + 1, infantPassengerInfoList[6])
            } else {
                requestHolder.removeDomNodes("//*:AirTraveler[" + (airTravelerIndex + 1) + "]//*:PersonName//*:Document")
            }
        }

        requestHolder['//*:OTA_AirBookRQ//*:BookingReferenceID/@ID'] = pnrNumber
        requestHolder['//*:OTA_AirBookRQ//*:Fulfillment//*:Name//*:Surname'] = surname

        CreatePaymentRequest createPaymentRequest = new CreatePaymentRequest(context, testRunner, log)

        Double amount = Double.parseDouble(context['amount'].toString()).round(2)
        String currencyCode = context['currencyCode']
        String amountString = amount.toString()
        createPaymentRequest.setPaymentRequest(requestHolder, amountString, currencyCode, 'createTicketOTARequest')

        context.getCurrentStep().setPropertyValue("Request", requestHolder.getPrettyXml())

        log.info("infantAddition request serialized.")
    }

}
