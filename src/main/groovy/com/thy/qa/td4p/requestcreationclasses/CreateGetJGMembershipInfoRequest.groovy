package com.thy.qa.td4p.requestcreationclasses

import com.eviware.soapui.support.XmlHolder
import com.thy.qa.td4p.baseclasses.TestStepFinder

class CreateGetJGMembershipInfoRequest implements CreateRequest {

    TestStepFinder testStepFinder = new TestStepFinder(context)

    CreateGetJGMembershipInfoRequest(context, testRunner, log) {
        this.context = context
        this.testRunner = testRunner
        this.log = log
    }

    void updateGetJGMembershipInfoRequest() {
        requestHolder = new XmlHolder(getRequestContent("getJGmembershipInfo"))

        requestHolder["//*:clientTransactionId"] = getClientTransactionId()
        requestHolder["//*[@key='SESSION_ID']/@value"] = context.getProperty("sessionId")
        requestHolder["//*[@key='CLIENT_IP']/@value"] = getIp()

        List jetGencNos = context.getProperty("jetGencNos")
        List uniqueJetGencNos = jetGencNos.unique(false).findAll()

        requestHolder["//*:getJGmembershipInfoOTARequest//*:telephone/@PhoneNumber"] = uniqueJetGencNos[testStepFinder.countPreviousSuccessfulStepsOfType("getJGmembershipInfo")]

        context.getCurrentStep().setPropertyValue("Request", requestHolder.getPrettyXml())

        log.info("getJGmembershipInfo request serialized.")
    }
}