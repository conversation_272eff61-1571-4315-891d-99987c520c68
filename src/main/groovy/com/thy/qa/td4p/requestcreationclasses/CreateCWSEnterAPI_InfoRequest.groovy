package com.thy.qa.td4p.requestcreationclasses

import com.eviware.soapui.support.XmlHolder
import com.thy.qa.td4p.baseclasses.TestStepFinder

class CreateCWSEnterAPI_InfoRequest implements CreateRequest {

    TestStepFinder testStepFinder = new TestStepFinder(context)
    CreateCheckinRequest createCheckinRequest = new CreateCheckinRequest(context, testRunner, log)

    CreateCWSEnterAPI_InfoRequest(context, testRunner, log) {
        this.context = context
        this.testRunner = testRunner
        this.log = log
    }

    void updateCWSEnterAPI_InfoRequest() {
        requestHolder = new XmlHolder(getRequestContent("CheckinWS-enterAPI_Info"))
        XmlHolder sPresponseHolder = new XmlHolder(testStepFinder.findSuccessfulStepOfType('searchPassenger').getPropertyValue('Response'))
        XmlHolder rRresponseHolder = new XmlHolder(testStepFinder.findSuccessfulStepOfType('retrieveReservationDetail').getPropertyValue('Response'))

        String ctidHolder = getClientTransactionId()
        requestHolder["//*:clientTransactionId"] = ctidHolder

        int apisPassengerIndex = context['apisPassengerIndex']
        List apisFlightIndexes = context['apisFlightIndexes']
        List apisPassengerIndexes = getApisPassengerIndexList(apisPassengerIndex, sPresponseHolder)
        List apisPassengerFlightIndexes = []
        apisFlightIndexes.each { flightIndex -> apisPassengerIndexes.each { passengerIndex -> apisPassengerFlightIndexes.add([passengerIndex, flightIndex]) } }
        log.info("updateCWSEnterAPI_InfoRequest ->  apisPassengerIndexes : " + apisPassengerIndexes)
        log.info("updateCWSEnterAPI_InfoRequest ->  apisPassengerFlightIndexes : " + apisPassengerFlightIndexes)

        cloneNodes("//*:OriginDestinationList//*:OriginDestination//*:Segment", apisFlightIndexes.size() - 1)
        cloneNodes("//*:PassengerFlightInfoList//*:PassengerFlightInfo", apisFlightIndexes.size() * apisPassengerIndexes.size() - 1)

        requestHolder["//*:TripData/@PnrCode"] = context['pnr']

        log.info("#### -- > setAllPassengerInfoLists  :  apisPassengerIndexes : " + apisPassengerIndexes)
        setAllPassengerInfoLists(apisPassengerIndexes, sPresponseHolder)

        log.info("#### -- > setAllOriginDestinationOptions  :  apisFlightIndexes : " + apisFlightIndexes)
        setAllOriginDestinationOptions(apisFlightIndexes, sPresponseHolder)

        log.info("#### -- > setAllPassengerFlightLists  :  apisPassengerFlightIndexes : " + apisPassengerFlightIndexes)
        setAllPassengerFlightLists(apisPassengerFlightIndexes, sPresponseHolder, rRresponseHolder)

        context.currentStep.setPropertyValue("Request", requestHolder.getPrettyXml())

        log.info("enter_APIInfo request serialized with ClientTransactionId " + ctidHolder + " for reporting any issue or reviewing service call logs.")
    }

    /**
     *
     * @param passengerPropertyList List = [(0)AdultRPH, (1)Gender, (2)PassengerRPH, (3)TravelingWithInfant, (4)NamePrefix, (5)GivenName, (6)Surname,
     *      (7)PassengerTypeCode, (8)SeatNumber ]
     */
    private void setPassengerInfoListBlockProperties(int nodeIndex, List passengerPropertyList) {
        requestHolder["//*:PassengerInfoList//*:PassengerInfo[" + nodeIndex + "]/@AdultRPH"] = passengerPropertyList[0]
        requestHolder["//*:PassengerInfoList//*:PassengerInfo[" + nodeIndex + "]/@RPH"] = passengerPropertyList[2]
        requestHolder["//*:PassengerInfoList//*:PassengerInfo[" + nodeIndex + "]/@Gender"] = passengerPropertyList[1]
        requestHolder["//*:PassengerInfoList//*:PassengerInfo[" + nodeIndex + "]/@TravelingWithInfant"] = passengerPropertyList[3]
        requestHolder["//*:PassengerInfoList//*:PassengerInfo[" + nodeIndex + "]//*:PassengerName//*:NamePrefix"] = passengerPropertyList[4]
        requestHolder["//*:PassengerInfoList//*:PassengerInfo[" + nodeIndex + "]//*:PassengerName//*:GivenName"] = passengerPropertyList[5]
        requestHolder["//*:PassengerInfoList//*:PassengerInfo[" + nodeIndex + "]//*:PassengerName//*:Surname"] = passengerPropertyList[6]
        requestHolder["//*:PassengerInfoList//*:PassengerInfo[" + nodeIndex + "]//*:PassengerTypeCode"] = passengerPropertyList[7]
    }

    /**
     *
     * @param nodeIndex
     * @param flightPropertyList List = [(0)IsReturnFlight, (1)FlightNumber, (2)MarketingAirlineCode, (3)flightRPH, (4)DepartureDate, (5)DepartureTime, (6)DepartureLocationCode,
     *      (7)ArrivalDate, (8)ArrivalTime, (9)ArrivalLocationCode, (10)CabinCode, (11)IsStandBy, (12)DepartureLocalTime, (13)DepartureLocalDate, (14)DepartureDay ]
     */
    private void setSegmentListBlockProperties(int nodeIndex, List flightPropertyList) {
        requestHolder["//*:OriginDestinationList//*:OriginDestination//*:Segment[" + nodeIndex + "]/@CabinClassCode"] = flightPropertyList[10]
        requestHolder["//*:OriginDestinationList//*:OriginDestination//*:Segment[" + nodeIndex + "]/@FlightNumber"] = flightPropertyList[1]
        requestHolder["//*:OriginDestinationList//*:OriginDestination//*:Segment[" + nodeIndex + "]/@MarketingAirlineCode"] = flightPropertyList[2]
        requestHolder["//*:OriginDestinationList//*:OriginDestination//*:Segment[" + nodeIndex + "]/@RPH"] = flightPropertyList[3]
        requestHolder["//*:OriginDestinationList//*:OriginDestination//*:Segment[" + nodeIndex + "]//*:DepartureInformation/@DepartureDate"] = flightPropertyList[4]
        requestHolder["//*:OriginDestinationList//*:OriginDestination//*:Segment[" + nodeIndex + "]//*:DepartureInformation/@DepartureDay"] = flightPropertyList[14]
        requestHolder["//*:OriginDestinationList//*:OriginDestination//*:Segment[" + nodeIndex + "]//*:DepartureInformation/@DepartureTime"] = flightPropertyList[5]
        requestHolder["//*:OriginDestinationList//*:OriginDestination//*:Segment[" + nodeIndex + "]//*:DepartureInformation/@LocationCode"] = flightPropertyList[6]
        requestHolder["//*:OriginDestinationList//*:OriginDestination//*:Segment[" + nodeIndex + "]//*:DepartureInformation//*:LocalTime/@date"] = flightPropertyList[13]
        requestHolder["//*:OriginDestinationList//*:OriginDestination//*:Segment[" + nodeIndex + "]//*:DepartureInformation//*:LocalTime/@time"] = flightPropertyList[12]
        requestHolder["//*:OriginDestinationList//*:OriginDestination//*:Segment[" + nodeIndex + "]//*:ArrivalInformation/@ArrivalDate"] = flightPropertyList[7]
        requestHolder["//*:OriginDestinationList//*:OriginDestination//*:Segment[" + nodeIndex + "]//*:ArrivalInformation/@LocationCode"] = flightPropertyList[9]
    }

    /**
     *
     * @param nodeIndex
     * @param passengerFlightPropertiesList List = [ (0)FlightRPH, (1)PassengerRPH, (2)CabinCode, (3)PaxIdentifierDCS_PassengerRefNumber, (4)PaxIdentifierSeatNumber, (5)BrandCode,
     *      (6)ChargeableSeatType, (7)HasChargeableExitSeat, (8)HasChargeableSeat, (9)HesCode, (10)HesCodeIdLast3Digit, (11)HesCodeExpiryDate, (12)HesCodeStatus, (13)ResBookDesigCode,
     *      (14)SeatEMDSSRCode, (15)SeatSelectionAllowed, (16)Status, (17)Type, (18)CouponNumber, (19)TicketNumber, (20)TicketStatus, (21)TicketType, (22)BaggageAllowance, (23)FareBase ]
     * @param passengerPropertyList List = [(0)AdultRPH, (1)Gender, (2)PassengerRPH, (3)TravelingWithInfant, (4)NamePrefix, (5)GivenName, (6)Surname,
     *      (7)PassengerTypeCode, (8)SeatNumber ]
     * @param additionalApisInfoList List = [(0)Name, (1)Surname, (2)NamePrefix, (3)BirthDate, (4)TCKN, (5)DocType, (6)Gender, (7)PassengerRPH, (8)PassengerType ]
     */
    private void setPassengerFlightListBlockProperties(int nodeIndex, List passengerPropertyList, List passengerFlightPropertiesList, List additionalApisInfoList) {
        List bookingInfoBlockKeyList = ["/@FlightRPH", "/@PassengerRPH", "/@CabinCode", "//*:PaxIdentifier/@DCS_PassengerRefNumber", "//*:PaxIdentifier/@SeatNumber",
                                        "//*:BookingInfo/@BrandCode", "//*:BookingInfo/@ChargeableSeatType", "//*:BookingInfo/@HasChargeableExitSeat", "//*:BookingInfo/@HasChargeableSeat",
                                        "//*:BookingInfo/@HesCode", "//*:BookingInfo/@HesCodeIdLast3Digit", "//*:BookingInfo/@HesCodeExpiryDate", "//*:BookingInfo/@HesCodeStatus",
                                        "//*:BookingInfo/@ResBookDesigCode", "//*:BookingInfo/@SeatEMDSSRCode", "//*:BookingInfo/@SeatSelectionAllowed", "//*:BookingInfo/@Status",
                                        "//*:BookingInfo/@Type", "//*:Ticket/@CouponNumber", "//*:Ticket/@TicketNumber", "//*:Ticket/@TicketStatus", "//*:Ticket/@TicketType",
                                        "//*:FareBase/@BaggageAllowance", "//*:FareBase/@FareBase"]
        bookingInfoBlockKeyList.eachWithIndex { key, passengerFlightListIndex ->
            if (passengerFlightPropertiesList[passengerFlightListIndex] == null || passengerFlightPropertiesList[passengerFlightListIndex] == "null" || passengerFlightPropertiesList[passengerFlightListIndex] == "") {
                requestHolder.getDomNode("//*:PassengerFlightInfoList//*:PassengerFlightInfo[" + nodeIndex + "]" + key.split("/@")[0].toString() + "").removeAttribute(key.split("/@")[1].toString())
            } else {
                requestHolder["//*:PassengerFlightInfoList//*:PassengerFlightInfo[" + nodeIndex + "]" + key + ""] = passengerFlightPropertiesList[passengerFlightListIndex]
            }
        }
        requestHolder["//*:PassengerFlightInfoList//*:PassengerFlightInfo[" + nodeIndex + "]//*:API_Info/@Name"] = additionalApisInfoList[0]
        requestHolder["//*:PassengerFlightInfoList//*:PassengerFlightInfo[" + nodeIndex + "]//*:API_Info/@Surname"] = additionalApisInfoList[1]
        requestHolder["//*:PassengerFlightInfoList//*:PassengerFlightInfo[" + nodeIndex + "]//*:API_Info/@TCIdentity"] = additionalApisInfoList[4]
        requestHolder["//*:PassengerFlightInfoList//*:PassengerFlightInfo[" + nodeIndex + "]//*:API_Info/@Gender"] = additionalApisInfoList[6]
        requestHolder["//*:PassengerFlightInfoList//*:PassengerFlightInfo[" + nodeIndex + "]//*:API_Info/@Birthday"] = additionalApisInfoList[3]
        requestHolder["//*:PassengerFlightInfoList//*:PassengerFlightInfo[" + nodeIndex + "]//*:API_Info/@DocType"] = additionalApisInfoList[5]
        if (passengerPropertyList[1] = "I") {

        } else {
            requestHolder["//*:PassengerFlightInfoList//*:PassengerFlightInfo[" + nodeIndex + "]//*:IdInformation/@TCKIdNumber"] = additionalApisInfoList[4]
            requestHolder["//*:PassengerFlightInfoList//*:PassengerFlightInfo[" + nodeIndex + "]//*:PaxIdentifier/@SeatNumber"] = passengerPropertyList[8]
        }
    }

    private void setAllPassengerInfoLists(List apisPassengerIndexList, XmlHolder sPresponseHolder) {
        apisPassengerIndexList.eachWithIndex { int passengerIndex, blockIndex ->
            log.info("|| passengerIndex :" + passengerIndex + "  (blockIndex+1) :" + (blockIndex + 1))
            List passengerProperties = createCheckinRequest.getPassengerProperties(passengerIndex, sPresponseHolder)
            log.info("|| passengerProperties : " + passengerProperties)
            setPassengerInfoListBlockProperties((blockIndex + 1), passengerProperties)
        }
    }

    private void setAllOriginDestinationOptions(List apisFlightIndexes, XmlHolder sPresponseHolder) {
        apisFlightIndexes.each { int flightIndex ->
            log.info("|| flightIndex :" + flightIndex)
            List flightProperties = createCheckinRequest.getFlightProperties(flightIndex, sPresponseHolder)
            log.info("|| flightProperties :" + flightProperties)
            setSegmentListBlockProperties(flightIndex, flightProperties)
        }
    }

    private void setAllPassengerFlightLists(List apisPassengerFlightIndexes, XmlHolder sPresponseHolder, XmlHolder rRresponseHolder) {

        apisPassengerFlightIndexes.eachWithIndex { List passengerFlightIndexList, int passengerFlightInfoNodeIndex ->

            List passengerFlightProperties = createCheckinRequest.getPassengerFlightProperties(passengerFlightIndexList[1].toInteger(), passengerFlightIndexList[0].toInteger(), sPresponseHolder)
            List passengerProperties = createCheckinRequest.getPassengerProperties(passengerFlightIndexList[0].toInteger(), sPresponseHolder)
            int passengerRPHinRetrieveReservationDetailResponse = sPresponseHolder.getNodeValue("//*:PassengerInfoList//*:PassengerInfo[@RPH=" + passengerFlightIndexList[0] + "]/@ReservationIndex").toInteger()
            List additionalApisInfos = createCheckinRequest.getAdditionalApisInfo(passengerRPHinRetrieveReservationDetailResponse, rRresponseHolder)
            log.info("|| (passengerFlightInfoNodeIndex+1) : " + (passengerFlightInfoNodeIndex + 1))
            log.info("|| passengerProperties :" + passengerProperties)
            log.info("|| passengerFlightProperties :" + passengerFlightProperties)
            log.info("|| additionalApisInfoList :" + additionalApisInfos)
            setPassengerFlightListBlockProperties((passengerFlightInfoNodeIndex + 1), passengerProperties, passengerFlightProperties, additionalApisInfos)
        }
    }

    /** This method sets passenger indexes if there is Infant passenger connected to the selected passenger or Infant passenger itself selected for Apis entry.
     *
     * @param apisPassengerIndex
     * @param responseHolder must contain "searchPassenger" services response.
     * @return List that contains either only selected passenger index or infant passenger index and its connected passenger index.
     */
    List getApisPassengerIndexList(int apisPassengerIndex, XmlHolder responseHolder) {
        List apisPassengerIndexList = []
        if (responseHolder.getNodeValue("//*:PassengerInfoList//*:PassengerInfo[@ReservationIndex=" + apisPassengerIndex + "]/@Gender") == "I") {
            cloneNodes("//*:PassengerInfoList//*:PassengerInfo", 1)
            apisPassengerIndexList.add(responseHolder.getNodeValue("//*:PassengerInfoList//*:PassengerInfo[@ReservationIndex=" + apisPassengerIndex + "]/@AdultRPH").toInteger())
            apisPassengerIndexList.add(responseHolder.getNodeValue("//*:PassengerInfoList//*:PassengerInfo[@ReservationIndex=" + apisPassengerIndex + "]/@RPH").toInteger())
        } else if (responseHolder.getNodeValue("//*:PassengerInfoList//*:PassengerInfo[@ReservationIndex=" + apisPassengerIndex + "]/@TravelingWithInfant") == "true") {
            cloneNodes("//*:PassengerInfoList//*:PassengerInfo", 1)
            apisPassengerIndexList.add(responseHolder.getNodeValue("//*:PassengerInfoList//*:PassengerInfo[@ReservationIndex=" + apisPassengerIndex + "]/@RPH").toInteger())
            apisPassengerIndexList.add(responseHolder.getNodeValue("//*:PassengerInfoList//*:PassengerInfo[@AdultRPH=" + apisPassengerIndex + "]/@RPH").toInteger())
        } else {
            apisPassengerIndexList.add(responseHolder.getNodeValue("//*:PassengerInfoList//*:PassengerInfo[@ReservationIndex=" + apisPassengerIndex + "]/@RPH").toInteger())
        }
        return apisPassengerIndexList
    }
}
