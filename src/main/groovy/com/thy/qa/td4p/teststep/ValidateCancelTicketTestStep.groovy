package com.thy.qa.td4p.teststep

import com.eviware.soapui.impl.wsdl.teststeps.WsdlTestRequestStep
import com.eviware.soapui.model.testsuite.TestCaseRunContext
import com.eviware.soapui.model.testsuite.TestCaseRunner
import com.eviware.soapui.model.testsuite.TestStep
import com.eviware.soapui.support.XmlHolder
import com.thy.qa.td4p.requestcreationclasses.CreateValidateCancelTicketRequest

class ValidateCancelTicketTestStep implements WsdlRequestStep {

    List exceptionList = [
            "There was an error while connecting to Troya reservation system",
            "Read Timeout",
            "java.net.SocketTimeoutException: Read timed out"
    ]

    @Override
    void beforeStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStep testStep) {
        new CreateValidateCancelTicketRequest(context, testRunner, getLog()).updateValidateCancelTicketRequest(context.correlationID)
    }


    @Override
    void onRetry(TestCaseRunContext context) {
        context.getTestRunner().gotoStep(context.currentStepIndex)
    }

    @Override
    void onSuccessStatus(TestCaseRunContext context) {
        getLog().info(context.currentStep.getOperationName() + " call is successful.")
        XmlHolder responseHolder = new XmlHolder(context.currentStep.getPropertyValue('Response'))

        List cancelTicketPenalty = responseHolder.getNodeValues("//*:validateCancelTicketResponse//*:OTA_CancelRS//*:CancelInfoRS//*:CancelRule[@Type='Charge-CancelPenalty']/@Amount")
        BigDecimal cancelTicketPenaltyChargeAmount = ((!cancelTicketPenalty) ? 0.00 : cancelTicketPenalty.collect { new BigDecimal(it) }.sum())
        context["cancelTicketPenaltyChargeAmount"] = cancelTicketPenaltyChargeAmount
        context["cancelTicketPenaltyChargeCurrencyCode"] = responseHolder.getNodeValue("//*:validateCancelTicketResponse//*:OTA_CancelRS//*:CancelInfoRS//*:CancelRule[@Type='Charge-CancelPenalty']/@CurrencyCode")
        if (cancelTicketPenaltyChargeAmount == 0.00) {
            boolean isAwardPnrValue = context['isAwardPnr']

            if (isAwardPnrValue) {
                List cancelTicketPenaltyMilesValues = responseHolder.getNodeValues("//*:validateCancelTicketResponse//*:OTA_CancelRS//*:CancelInfoRS//*:CancelRule[@Type='Charge-Mile']/@Amount")
                BigDecimal cancelTicketPenaltyChargeMileAmount = (!cancelTicketPenaltyMilesValues) ? 0.00 : cancelTicketPenaltyMilesValues.collect { new BigDecimal(it) }.sum()
                context["cancelTicketPenaltyChargeMileAmount"] = cancelTicketPenaltyChargeMileAmount
                context["cancelTicketPenaltyChargeMileCurrencyCode"] = responseHolder.getNodeValue("//*:validateCancelTicketResponse//*:OTA_CancelRS//*:CancelInfoRS//*:CancelRule[@Type='Charge-Mile']/@CurrencyCode")

                if (!cancelTicketPenaltyChargeMileAmount) {
                    setPrePaymentDisabled(context)
                }
            } else {
                setPrePaymentDisabled(context)
            }
        }
    }

    private void setPrePaymentDisabled(TestCaseRunContext context) {
        WsdlTestRequestStep prePaymentStep = (context.getCurrentStepIndex()..(context.getTestCase().getTestStepCount() - 1)).collect { context.getTestCase().getTestStepAt(it) }.findAll { it instanceof WsdlTestRequestStep && it.getOperationName() == "prePayment" && it.getName().contains("TD4P") }.get(0)

        prePaymentStep.disabled = true
    }

}
