package com.thy.qa.td4p.teststep

import com.eviware.soapui.model.testsuite.TestCaseRunContext
import com.eviware.soapui.model.testsuite.TestCaseRunner
import com.eviware.soapui.model.testsuite.TestStep
import com.thy.qa.td4p.request.soap.MemberNonAirAccrualActivityRequest
import groovy.transform.CompileStatic

@CompileStatic
class MemberNonAirAccrualActivityTestStep implements WsdlRequestStep {

    List exceptionList = ['Read Timeout',
                          'java.net.SocketTimeoutException: Read timed out']

    @Override
    void beforeStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStep testStep) {
        String request = new MemberNonAirAccrualActivityRequest((String) context.getProperty('msNo'), MemberNonAirAccrualActivityRequest.MAX_MILES_PER_TRANSACTION).toString()
        log.info('memberNonAirAccrualActivity request serialized.')
        testStep.setPropertyValue('Request', request)
    }

    @Override
    void onSuccessStatus(TestCaseRunContext context) {
        WsdlRequestStep.super.onSuccessStatus(context)
        log.info("$MemberNonAirAccrualActivityRequest.MAX_MILES_PER_TRANSACTION miles loaded.")
    }

    @Override
    void onRetry(TestCaseRunContext context) {
        context.testRunner.gotoStep(context.currentStepIndex)
    }

}
