package com.thy.qa.td4p.teststep


import com.eviware.soapui.model.testsuite.TestCaseRunContext
import com.eviware.soapui.model.testsuite.TestCaseRunner
import com.eviware.soapui.model.testsuite.TestStep
import com.thy.qa.td4p.Pnr
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.environment.TroyaEnvironment
import com.thy.qa.td4p.merchandising.offer.Offer
import com.thy.qa.td4p.request.soap.CreateReservationRequest
import groovy.transform.CompileStatic

@CompileStatic
class CreateReservationTestStep implements WsdlRequestStep, RetrySeating {

    List exceptionList = ["OFFER_QUERY_SERVICE_ERROR",
                          "There was an error while connecting to Troya reservation system",
                          "Read Timeout",
                          "java.net.SocketTimeoutException: Read timed out"]

    @Override
    void beforeStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStep testStep) {
        Channel channel = (Channel) context.getProperty('channel')
        String sessionId = context.getProperty('sessionId')
        TroyaEnvironment troyaEnvironment = (TroyaEnvironment) context.getProperty('troyaEnvironment')
        Pnr pnr = (Pnr) context.getProperty('pnr')
        String surname = context.getProperty('surname')
        List offers = (List) context.getProperty('offers')
        Offer reservationOffer = (Offer) offers.get(0)
        String request = new CreateReservationRequest(channel: channel,
                sessionId: sessionId,
                troyaEnvironment: troyaEnvironment,
                pnrNumber: pnr.pnrNumber, surname: surname,
                offerItemId: reservationOffer.offerItemId)
        log.info('createReservation request serialized.')
        testStep.setPropertyValue('Request', request)
    }

    @Override
    void onRetry(TestCaseRunContext context) {
        if (getFailureMessage() in ["There was an error while connecting to Troya reservation system",
                                    "Read Timeout",
                                    "java.net.SocketTimeoutException: Read timed out"]) {
            context.getTestRunner().gotoStep(context.currentStepIndex)
        } else if (getFailureMessage() in ["OFFER_QUERY_SERVICE_ERROR"]) {
            retrySeating(context)
        }
    }

}
