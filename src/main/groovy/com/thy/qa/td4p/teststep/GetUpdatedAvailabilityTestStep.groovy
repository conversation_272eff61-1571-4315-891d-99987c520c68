package com.thy.qa.td4p.teststep

import com.eviware.soapui.model.testsuite.TestCaseRunContext
import com.eviware.soapui.model.testsuite.TestCaseRunner
import com.eviware.soapui.model.testsuite.TestStep
import com.thy.qa.td4p.baseclasses.OriginDestinationOption
import com.thy.qa.td4p.baseclasses.TestStepFinder
import com.thy.qa.td4p.enums.CabinType
import com.thy.qa.td4p.enums.FareType
import com.thy.qa.td4p.parser.InternationalAvailabilityResponseParser
import com.thy.qa.td4p.request.soap.GetUpdatedAvailabilityRequest
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult

class GetUpdatedAvailabilityTestStep implements WsdlRequestStep, AvailabilityOptionFiltering, RetrySeating {

    List exceptionList = ["We are unable to find recommendations for the date(s) / time(s) specified. However, you can see flights for alternate days by selecting other dates below.",
                          "There is no recommendation for the requested date and route",
                          "AMADEUS_TIMEOUT_ERROR",
                          "AMADEUS_SERVICE_EXECUTION_ERROR",
                          "NO_FARE_OR_SEAT_AVAILABLE",
                          "NO_FLIGHTS_TO_DISPLAY",
                          "NO_FLIGHTS_WITH_THIS_CRITERIA",
                          "Read Timeout",
                          "java.net.SocketTimeoutException: Read timed out",
                          "TROYA_MERCURY_TIMEOUT_ERROR",
                          'DFS_RESTFUL_SERVICE_TIMEOUT_ERROR']

    private static final List ECONOMY_FARE_TYPES = [FareType.ECOFLY, FareType.EXTRAFLY, FareType.PRIMEFLY, FareType.FLEXFLY, FareType.ECONOMY_PROMOTIONAL,
                                                    FareType.ECONOMY_FLEXIBLE, FareType.ECONOMY]

    private static final List BUSINESS_FARE_TYPES = [FareType.BUSINESS_PROMOTIONAL, FareType.BUSINESS_FLEXIBLE, FareType.BUSINESS_RESTRICTED,
                                                     FareType.BUSINESSFLY, FareType.BUSINESSPRIMEFLY, FareType.BUSINESS]

    @Override
    void beforeStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStep testStep) {
        def text = { node -> node.text() }
        String availabilityResponse = new TestStepFinder(context).findSuccessfulStepOfType('getAvailability').getPropertyValue('Response')
        GPathResult response = new XmlSlurper().parseText(availabilityResponse)
        OriginDestinationOption outboundFlight = context.pnr.originDestinationOptions[0]
        List flightNumbers = outboundFlight.flightSegments*.flightNumber
        List brandCodes = outboundFlight.flightSegments*.brandCode*.name()
        GPathResult listProposedBound = response.depthFirst().find { it.name() == 'LIST_PROPOSED_BOUND' }
        int outboundFlightId = listProposedBound.LIST_FLIGHT.find {
            it.LIST_SEGMENT.FLIGHT_NUMBER.collect(text) == flightNumbers
        }.FLIGHT_ID.toInteger()
        List listRecommendations = response.depthFirst().findAll { it.name() == 'LIST_RECOMMENDATION' }
        GPathResult listRecommendation = listRecommendations.find {
            it.LIST_BOUND[0].LIST_FLIGHT.any { it.FLIGHT_ID.toInteger() == outboundFlightId } && it.LIST_PNR.LIST_TRAVELLER_TYPE[0].LIST_BOUND[0].LIST_SEGMENT.FARE_FAMILY.SHORT_NAME.collect(text) == brandCodes
        }
        int inboundFlightId = listRecommendation.LIST_BOUND[1].LIST_FLIGHT[0].FLIGHT_ID.toInteger()
        GPathResult flexPricerAvailabilityOutput = response.depthFirst().find { it.name() == 'flexPricerAvailabilityOutput' }
        int pageTicket = flexPricerAvailabilityOutput.PAGE_TICKET.toInteger()
        String jSessionId = flexPricerAvailabilityOutput.@jSessionId
        int recommendationId = listRecommendation.RECOMMENDATION_ID.toInteger()
        String request = new GetUpdatedAvailabilityRequest(context, pageTicket, jSessionId, recommendationId, outboundFlightId, inboundFlightId, getCabinType(context)).toString()
        log.info('getUpdatedAvailability request serialized.')
        testStep.setPropertyValue('Request', request)
    }

    @Override
    void onSuccessStatus(TestCaseRunContext context) {
        log.info(context.currentStep.operationName + ' call is successful.')
        List inboundOptions = parseInboundOptions(context)
        filterInboundOptions(inboundOptions, context)
        if (!inboundOptions) {
            if (context['retryCount']) {
                context['retryCount'] -= 1
                log.info('Retrying getAvailability...')
                retrySeating(context)
            } else {
                log.error('Tried to find flights. But flights for all routes could not be found.')
                context.testRunner.fail("Tried to find flights. But flights for all routes could not be found.")
            }
        } else {
            log.info("${inboundOptions.size()} option(s) found for 2. flight.")
            context.pnr.originDestinationOptions << inboundOptions.shuffled().get(0)
        }
    }

    @Override
    void onRetry(TestCaseRunContext context) {
        if (failureMessage in ["AMADEUS_TIMEOUT_ERROR",
                               "AMADEUS_SERVICE_EXECUTION_ERROR",
                               "Read Timeout",
                               "java.net.SocketTimeoutException: Read timed out",
                               "TROYA_MERCURY_TIMEOUT_ERROR",
                               'DFS_RESTFUL_SERVICE_TIMEOUT_ERROR']) {
            getLog().info("Retrying getUpdatedAvailability without incrementing day values.")
            context.testRunner.gotoStep(context.currentStepIndex)
        } else if (failureMessage in ["We are unable to find recommendations for the date(s) / time(s) specified. However, you can see flights for alternate days by selecting other dates below.",
                                      "There is no recommendation for the requested date and route",
                                      "NO_FARE_OR_SEAT_AVAILABLE",
                                      "NO_FLIGHTS_TO_DISPLAY",
                                      "NO_FLIGHTS_WITH_THIS_CRITERIA",
                                      "NO_OPERATION_FOR_THIS_DATE"]) {
            retrySeating(context)
        }
    }

    private CabinType getCabinType(TestCaseRunContext context) {
        List uniqued = context.getProperty('fareTypes').unique(false)
        if (uniqued.size() == 1) {
            FareType fareType = uniqued[0]
            (fareType == FareType.ANY) ? CabinType.ANY : (fareType in ECONOMY_FARE_TYPES) ? CabinType.ECONOMY : CabinType.BUSINESS
        } else if (uniqued.every { it in ECONOMY_FARE_TYPES }) {
            return CabinType.ECONOMY
        } else if (uniqued.every { it in BUSINESS_FARE_TYPES }) {
            return CabinType.BUSINESS
        } else {
            return CabinType.ANY
        }
    }

    void filterInboundOptions(List inboundOptions, TestCaseRunContext context) {
        filterOptionsByStopover(inboundOptions, context.stopovers[1])
        filterOptionsByClassCodes(inboundOptions, context.flights[1].fareClassCodes)
        FareType fareType = context.fareTypes[1]
        if (fareType != FareType.ANY) {
            filterInternationalOptionsByBrandCode(inboundOptions, fareType)
        }
        def airlineCodes = context.airlineCodes
        if (airlineCodes) {
            filterOptionsByAirlineCodes(inboundOptions, getFlightAirlineCodes(airlineCodes, context.stopovers, 1))
        }
    }

    List parseInboundOptions(TestCaseRunContext context) {
        InternationalAvailabilityResponseParser parser = new InternationalAvailabilityResponseParser()
        GPathResult response = new XmlSlurper().parseText(context.currentStep.getPropertyValue('Response'))
        GPathResult listTab = response.depthFirst().find { it.name() == 'flexPricerAvailabilityOutput' }.LIST_PANEL[1].LIST_TAB
        parser.setListTab(listTab)
        parser.parseOriginDestinationOptions(1)
    }

}
