package com.thy.qa.td4p.teststep

import com.eviware.soapui.model.testsuite.TestCaseRunContext
import com.eviware.soapui.model.testsuite.TestCaseRunner
import com.eviware.soapui.model.testsuite.TestStep
import com.thy.qa.td4p.requestcreationclasses.CreateCWSPrintBoardingPass

class CWSPrintBoardingPassTestStep implements WsdlRequestStep {

    @Override
    void beforeStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStep testStep) {
        new CreateCWSPrintBoardingPass(context, testRunner, getLog()).updatePrintBoardingPassRequest()
    }

    @Override
    void onRetry(TestCaseRunContext context) {

    }
}
