package com.thy.qa.td4p.teststep

import com.eviware.soapui.impl.wsdl.teststeps.WsdlTestRequestStep
import com.eviware.soapui.model.testsuite.TestCase
import com.eviware.soapui.model.testsuite.TestCaseRunContext
import com.eviware.soapui.model.testsuite.TestCaseRunner
import com.eviware.soapui.model.testsuite.TestStep
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.pnr.PnrType
import com.thy.qa.td4p.request.soap.RetrieveReservationDetailRequest
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.Attribute
import groovy.xml.slurpersupport.GPathResult

class RetrieveReservationDetailTestStep implements WsdlRequestStep {

    List exceptionList = ["There was an error while connecting to Troya reservation system",
                          "Read Timeout",
                          "java.net.SocketTimeoutException: Read timed out",
                          'PNR_RETRIEVAL_ERROR']

    @Override
    void beforeStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStep testStep) {
        String request = new RetrieveReservationDetailRequest(channel: context.channel,
                sessionId: context.sessionId,
                troyaEnvironment: context.troyaEnvironment,
                pnrNumber: context.pnr.pnrNumber,
                surname: context.surname).toString()
        log.info('retrieveReservationDetail request serialized.')
        testStep.setPropertyValue('Request', request)
    }

    @Override
    void onRetry(TestCaseRunContext context) {
        context.getTestRunner().gotoStep(context.currentStepIndex)
    }

    @Override
    void onSuccessStatus(TestCaseRunContext context) {
        log.info('retrieveReservationDetail call is successful.')
        if (context['addReissueFlights'] == true || context['cancelFlights'] == true) {
            setReissueContextParameters(context)
            if (!context.addReissueFlights) {
                disableGetAvailabilityStep(context)
            }
        }
    }

    private void setReissueContextParameters(TestCaseRunContext context) {
        GPathResult result = new XmlSlurper().parseText(context.currentStep.getPropertyValue('Response'))
        Iterator children = result.depthFirst()
        GPathResult priceInfo = children.find { it.name() == 'PriceInfo' }
        context.pnr.currencyCode = <EMAIL>()
        GPathResult travelerInfo = (GPathResult) children.find { it.name() == 'TravelerInfo' }
        List passengerCodes = <EMAIL> { Attribute it -> PassengerCode.valueOf(it.toString()) }
        context.passengerCodes = passengerCodes
        boolean awardPnr = children.find { it.name() == 'reservationInfoOTAResponse' }.isAward.toBoolean()
        if (awardPnr) {
            context.pnrType = PnrType.AWARD_TICKET
            context.routingType = RoutingType.ONEWAY
            List msNos = travelerInfo.AirTraveler.collect { <EMAIL>() + <EMAIL>() }
            context.msNos = msNos
        }
    }

    private void disableGetAvailabilityStep(TestCaseRunContext context) {
        TestCase testCase = context.testCase
        TestStep getAvailabilityStep = testCase.testStepList[(context.currentStepIndex + 1)..<testCase.testStepCount].find {
            it instanceof WsdlTestRequestStep && it.operationName == 'getAvailability' && it.name.startsWith('TD4P')
        }
        getAvailabilityStep.disabled = true
    }

}
