package com.thy.qa.td4p.merchandising.offer.service

import com.thy.qa.td4p.merchandising.request.seat.SeatPosition
import groovy.xml.slurpersupport.GPathResult

class SeatService extends AssociatedAncillaryService {
    SeatKind seatKind
    SeatPosition seatPosition

    SeatService(GPathResult serviceNode) {
        super(serviceNode)
        setPriceDetails(serviceNode)
        this.seatKind = SeatKind.valueOf(serviceNode.Specification.Parameter[0].Value.text().toUpperCase())
        this.seatPosition = SeatPosition.valueOf(serviceNode.Specification.Parameter[1].Value.text().toUpperCase())
    }

    @Override
    Closure toXml() { super.toXml() << segmentAssociation() }

    @Override
    String getSsrCode() { 'RQST' }

    @Override
    ServiceCategory getServiceCategory() { ServiceCategory.SEAT }

    @Override
    String toString() {
        return "${seatKind} $seatPosition seat: passengerIndex=$passengerIndex, price=$price, discountPrice=$discountPrice, discountRate=$discountRate, currencyCode=$currencyCode"
    }

    def equals(SeatService obj) {
        obj.segmentIndexes == segmentIndexes && obj.passengerIndex == passengerIndex && obj.seatKind == seatKind && obj.seatPosition == seatPosition
    }
}
