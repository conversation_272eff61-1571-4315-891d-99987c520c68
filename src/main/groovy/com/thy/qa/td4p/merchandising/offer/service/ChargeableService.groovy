package com.thy.qa.td4p.merchandising.offer.service

import groovy.xml.slurpersupport.GPathResult

abstract class ChargeableService extends AncillaryService {

    BigDecimal discountRate
    BigDecimal price
    BigDecimal discountPrice
    String currencyCode

    ChargeableService(GPathResult serviceNode) {
        super(serviceNode)
        setPriceDetails(serviceNode)
    }

    void setPriceDetails(GPathResult service) {
        setDiscountRate(<EMAIL>() ?: 0)
        setPrice(<EMAIL>() ?: 0)
        setDiscountPrice(<EMAIL>() ?: 0)
        setCurrencyCode(<EMAIL>())
    }

    Closure toXml() {
        return {
            passengerIndex passengerIndex
            ssrCode getSsrCode()
            ancillaryItemType ancillaryItemType.toString()
            flow 'BOOKING'
            service(ServiceID: serviceId)
            paymentAmount discountPrice ?: price
            paymentCurrency currencyCode
        }
    }
}
