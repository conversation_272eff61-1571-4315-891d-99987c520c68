package com.thy.qa.td4p.merchandising.offer.service

import groovy.xml.slurpersupport.GPathResult

class FreeReservationService extends AncillaryService implements ReservationService {

    FreeReservationService(GPathResult serviceNode) {
        super(serviceNode)
        this.duration = parseDuration(serviceNode)
    }

    @Override
    ServiceCategory getServiceCategory() { ServiceCategory.OPT_USDOT }

    @Override
    String toString() {
        return "FreeReservationService{" + "passengerIndex=" + passengerIndex + ", duration=" + duration + '}'
    }

    @Override
    boolean isChargeable() { false }
}
