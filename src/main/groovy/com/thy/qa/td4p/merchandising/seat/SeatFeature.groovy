package com.thy.qa.td4p.merchandising.seat

import groovy.transform.CompileStatic

@CompileStatic
enum SeatFeature {
    BASSINET, LEG_SPACE, EMERGENCY_EXIT, NORMAL,
    NON_SMOKING, WINDOW, NON_INFANT, LEAST_DESIRABLE, FIRST_ROW,
    <PERSON><PERSON><PERSON><PERSON>, <PERSON>SL<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, NON_CHILD, NON_MEDICAL, PREFERRED

    static SeatFeature fromString(String feature) {
        switch (feature) {
            case "Non-smoking" -> NON_SMOKING
            case "Window" -> WINDOW
            case "Non-infant" -> NON_INFANT
            case "Leg space" -> LEG_SPACE
            case "Least Desirable" -> LEAST_DESIRABLE
            case "First Row" -> FIRST_ROW
            case "Bassinet" -> BASSINET
            case "Middle" -> MIDDLE
            case "Aisle" -> AISLE
            case "Normal" -> NORMAL
            case "Chargable" -> CHARGEABLE
            case "Emergency Exit" -> EMERGENCY_EXIT
            case "Non-child" -> NON_CHILD
            case "Non-medical" -> NON_MEDICAL
            case 'Preferred' -> PREFERRED
            default -> throw new IllegalArgumentException("Unknown seat feature: $feature")
        }
    }
}