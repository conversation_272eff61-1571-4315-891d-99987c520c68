package com.thy.qa.td4p.merchandising.seat

import com.thy.qa.td4p.enums.CabinType
import com.thy.qa.td4p.merchandising.offer.service.SeatKind
import groovy.transform.CompileStatic
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult

class GetSeatMapResponseParser {

    SeatMap parse(String getSeatMapResponse) {
        GPathResult result = new XmlSlurper(false, true).parseText(getSeatMapResponse)
        List seatRows = result.depthFirst().findAll { it.name() == 'RowInfo' }
        List<SeatRow> rows = seatRows.findAll { row -> row.SeatInfo.Availability.any { it.text() != 'NoSeatHere' } }.collect { parseRow(it) }
        return new SeatMap(rows: rows)
    }

    private SeatRow parseRow(GPathResult row) {
        CabinType cabinType = CabinType.valueOf(<EMAIL>().toUpperCase())
        int rowNumber = <EMAIL>()
        GPathResult seatInfos = row.SeatInfo.findAll { it.Availability != 'NoSeatHere' }
        List<SeatInfo> seats = seatInfos.collect(this::parseSeatInfo)
        new SeatRow(cabinType: cabinType, rowNumber: rowNumber, seats: seats)
    }

    private SeatInfo parseSeatInfo(GPathResult seatInfo) {
        String seatNumber = seatInfo.Summary.@SeatNumber
        GPathResult features = seatInfo.Features
        List<SeatFeature> seatFeatures = features.collect { SeatFeature.fromString(<EMAIL>()) }
        boolean reserved = isReserved(seatInfo)
        String planeSection = <EMAIL>()
        if (seatFeatures.any { it == SeatFeature.CHARGEABLE }) {
            SeatKind seatKind = SeatKind.valueOf(planeSection.split('_')[1])
            ChargeableSeatInfo chargeableSeatInfo = new ChargeableSeatInfo(seatNumber: seatNumber, seatKind: seatKind, seatFeatures: seatFeatures, reserved: reserved)
            List<GPathResult> amenities = seatInfo.depthFirst().findAll { it.name() == 'Amenity' }
            GPathResult discountedAmenity = amenities.find { <EMAIL>() == 'DISCOUNTED_LOCAL FARE' }
            if (discountedAmenity != null) {
                chargeableSeatInfo.setAmount(<EMAIL>())
                chargeableSeatInfo.setCurrencyCode(<EMAIL>())
            } else {
                GPathResult seatAmenity = amenities.find { <EMAIL>() == 'LOCAL FARE' }
                chargeableSeatInfo.setAmount(<EMAIL>())
                chargeableSeatInfo.setCurrencyCode(<EMAIL>())
            }
            if (!reserved) {
                GPathResult mileFee = amenities.find { <EMAIL>() == 'PTS' }
                if (mileFee != null) {
                    chargeableSeatInfo.setMiles(<EMAIL>())
                }
            }
            return chargeableSeatInfo
        } else {
            SeatKind seatKind = planeSection ? SeatKind.valueOf(planeSection.split('_')[1].toUpperCase()) : getSeatKind(seatFeatures)
            return new SeatInfo(seatNumber: seatNumber, seatKind: seatKind, seatFeatures: seatFeatures, reserved: reserved)
        }
    }

    private SeatKind getSeatKind(List<SeatFeature> seatFeatures) {
        return switch (seatFeatures) {
            case (seatFeatures.contains(SeatFeature.LEG_SPACE) && !seatFeatures.contains(SeatFeature.EMERGENCY_EXIT)) -> SeatKind.LEGROOM
            case (seatFeatures.contains(SeatFeature.EMERGENCY_EXIT)) -> SeatKind.EXIT
            case (seatFeatures.contains(SeatFeature.BASSINET)) -> SeatKind.LEGROOM
            case (seatFeatures.contains(SeatFeature.PREFERRED)) -> SeatKind.PREFERRED
            default -> SeatKind.NORMAL
        }
    }

    @CompileStatic
    private boolean isReserved(GPathResult seatInfoNode) {
        ((GPathResult) seatInfoNode.getProperty('Availability')).text() == 'SeatReserved'
    }
}