package com.thy.qa.td4p.merchandising.pet

import com.thy.qa.td4p.merchandising.offer.service.ServiceCategory

enum PetType {
    DOG_CABIN("DOG", "ARMANT", 5, "20x25x35", ServiceCategory.PETC),
    CAT_CABIN("CAT", "TURKISH VAN", 3, "15x20x30", ServiceCategory.PETC),
    BIRD_CABIN("BIRD", "CANARIES", 1, "5x5x5", ServiceCategory.PETC),
    DOG_AIRCRAFT("DOG", "ARMANT", 40, "75x75x125", ServiceCategory.AVIH),
    CAT_AIRCRAFT("CAT", "SAVANNAH", 15, "30x30x30", ServiceCategory.AVIH),
    BIRD_AIRCRAFT("BIRD", "DOVES", 20, "50x50x50", ServiceCategory.AVIH);

    private String family
    private String species
    private String dimensions
    private int weight
    private ServiceCategory serviceCategory

    PetType(String family, String species, int weight, String dimensions, ServiceCategory serviceCategory) {
        this.family = family
        this.species = species
        this.weight = weight
        this.dimensions = dimensions
        this.serviceCategory = serviceCategory
    }

    String getFamily() {
        family
    }

    String getSpecies() {
        species
    }

    String getDimensions() {
        dimensions
    }

    int getWeight() {
        weight
    }

    ServiceCategory getServiceCategory() {
        serviceCategory
    }
}