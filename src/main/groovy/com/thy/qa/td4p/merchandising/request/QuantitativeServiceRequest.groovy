package com.thy.qa.td4p.merchandising.request

import com.thy.qa.td4p.merchandising.offer.service.Unit

abstract class QuantitativeServiceRequest extends AssociatedServiceRequest {

    QuantitativeServiceRequest(int passengerIndex, int flightIndex) {
        super(passengerIndex, flightIndex)
    }

    abstract Unit getUnit()

    abstract int getQuantity()

    @Override
    boolean isQuantitative() { true }
}