package com.thy.qa.td4p.merchandising.request


import com.thy.qa.td4p.merchandising.offer.service.ServiceCategory
import com.thy.qa.td4p.merchandising.offer.service.Unit
import com.thy.qa.td4p.merchandising.pet.PetType

class PetCarriage extends QuantitativeServiceRequest {

    final Unit unit = Unit.KILOGRAM
    final PetType petType
    final ServiceCategory category

    PetCarriage(int passengerIndex, int flightIndex, PetType petType) {
        super(passengerIndex, flightIndex)
        this.petType = petType
        this.category = petType.serviceCategory
    }

    @Override
    ServiceCategory getServiceCategory() { category }

    String getDescription() {
        "TTL" + petType.weight + unit.value() + " " + petType.dimensions + "CM" + " " + petType.family + " " + petType.species
    }

    @Override
    int getQuantity() { petType.weight }
}
