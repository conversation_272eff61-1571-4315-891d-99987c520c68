package com.thy.qa.td4p.merchandising.offer.service

import com.thy.qa.td4p.merchandising.request.QuantitativeServiceRequest
import groovy.xml.slurpersupport.GPathResult

abstract class QuantitativeAncillaryService extends AssociatedAncillaryService {
    Unit unit
    int quantity

    QuantitativeAncillaryService(GPathResult serviceNode) {
        super(serviceNode)
        setUnit(serviceNode)
        setQuantity(serviceNode)
    }

    Unit setUnit(GPathResult serviceNode) {
        this.unit = Unit.valueOf(serviceNode.Specification.Parameter[0].Value.text().toUpperCase())
    }

    void setQuantity(GPathResult serviceNode) {
        this.quantity = serviceNode.Specification.Parameter[1].Value.toInteger()
    }

    void setQuantity(int quantity) {
        this.quantity = quantity
    }

    Closure quantitative() {
        return {
            ancillaryServiceAmount quantity
            ancillaryServiceUnit unit.value()
        }
    }

    boolean matches(QuantitativeServiceRequest request) {
        return request.quantity == quantity && request.unit == unit
    }
}