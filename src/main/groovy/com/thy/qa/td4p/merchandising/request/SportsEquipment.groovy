package com.thy.qa.td4p.merchandising.request

import com.thy.qa.td4p.merchandising.offer.service.ServiceCategory
import com.thy.qa.td4p.merchandising.offer.service.Unit
import com.thy.qa.td4p.merchandising.speq.SportsEquipmentSpecification
import com.thy.qa.td4p.merchandising.speq.SportsEquipmentSpecifications
import com.thy.qa.td4p.merchandising.speq.SportsEquipmentType

class SportsEquipment extends QuantitativeServiceRequest {
    final int piece
    final SportsEquipmentSpecification specification
    final Unit unit = Unit.PIECE

    SportsEquipment(int passengerIndex, int flightIndex, int piece, SportsEquipmentType speqName) {
        super(passengerIndex, flightIndex)
        this.piece = piece
        this.specification = SportsEquipmentSpecifications.of(speqName, piece)
        assert piece <= 2, "You can choose a maximum of 2 pieces of sports equipment for one passenger each time."
    }

    @Override
    ServiceCategory getServiceCategory() { ServiceCategory.SPEQ }

    @Override
    int getQuantity() { piece }
}
