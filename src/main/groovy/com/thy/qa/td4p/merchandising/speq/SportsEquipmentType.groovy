package com.thy.qa.td4p.merchandising.speq

enum SportsEquipmentType {
    <PERSON><PERSON>,
    SNOWBOARD,
    SPORTING_FIREARMS,
    TANDEM, ARCH, ARCHERY, BIKE, BOAT,
    BOOGIE_BOARD, BOWL_EQUIPMENT, CAMP, CANOE,
    CLIM<PERSON>NG, DIVE, FISH, H<PERSON>G_GLIDING, <PERSON>OCKE<PERSON>,
    HORSE_RIDING, JAVELIN, KITE_SURFING, PARACHUT<PERSON>, TENNIS_SQUASH_BADMINT, FENCING_EQUIPMENT, SCOOTER, SKATEBOARD

    String ssrCode() {
        if (this == BIKE) {
            return toString()
        } else {
            return "SPEQ"
        }
    }
}