package com.thy.qa.td4p.merchandising.offer.service

import com.thy.qa.td4p.merchandising.offer.AncillaryItemType
import groovy.xml.slurpersupport.GPathResult

abstract class AncillaryService {
    int passengerIndex
    String serviceId
    AncillaryItemType ancillaryItemType

    AncillaryService(GPathResult serviceNode) {
        passengerIndex = serviceNode.PassengerRef.toInteger()
        serviceId = <EMAIL>()
        ancillaryItemType = AncillaryItemType.fromString(<EMAIL>())
    }

    abstract String getSsrCode()

    abstract ServiceCategory getServiceCategory()
}
