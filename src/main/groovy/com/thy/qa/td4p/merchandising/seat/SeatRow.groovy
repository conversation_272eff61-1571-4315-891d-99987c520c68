package com.thy.qa.td4p.merchandising.seat

import com.thy.qa.td4p.enums.CabinType
import com.thy.qa.td4p.merchandising.offer.service.SeatKind
import groovy.transform.CompileStatic

@CompileStatic
class SeatRow {
    List<SeatInfo> seats
    CabinType cabinType
    int rowNumber

    boolean isStandardSeatRow() { seats.every { it.seatKind == SeatKind.NORMAL } }

    boolean isAllSeatsReserved() { seats.every { it.reserved } }
}