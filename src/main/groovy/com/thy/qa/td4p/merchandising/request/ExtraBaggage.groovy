package com.thy.qa.td4p.merchandising.request

import com.thy.qa.td4p.merchandising.offer.service.ServiceCategory
import com.thy.qa.td4p.merchandising.offer.service.Unit

class ExtraBaggage extends QuantitativeServiceRequest {
    final Unit unit
    final int quantity

    ExtraBaggage(int passengerIndex, int flightIndex, int quantity, Unit unit) {
        super(passengerIndex, flightIndex)
        this.quantity = quantity
        this.unit = unit
    }

    @Override
    ServiceCategory getServiceCategory() { ServiceCategory.XBAG }
}
