package com.thy.qa.td4p.merchandising.offer.service

import groovy.xml.slurpersupport.GPathResult

abstract class AssociatedAncillaryService extends ChargeableService {
    List segmentIndexes

    AssociatedAncillaryService(GPathResult serviceNode) {
        super(serviceNode)
        setSegmentRefs(serviceNode)
    }

    void setSegmentRefs(GPathResult serviceNode) {
        this.segmentIndexes = serviceNode.SegmentRefs.collect { segmentRef -> segmentRef.toInteger() }
    }

    Closure segmentAssociation() { return { segmentIndexes.each { segmentIndexList it } } }
}
