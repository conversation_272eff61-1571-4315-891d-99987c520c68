package com.thy.qa.td4p.merchandising.request.seat

import com.thy.qa.td4p.merchandising.offer.service.SeatKind
import groovy.transform.CompileStatic

@CompileStatic
class ExitSeat extends SeatServiceRequest {
    ExitSeat(int passengerIndex, int segmentIndex, SeatPosition seatPosition = SeatPosition.ANY) {
        super(passengerIndex, segmentIndex, seatPosition)
    }

    @Override
    SeatKind getSeatKind() { SeatKind.EXIT }
}
