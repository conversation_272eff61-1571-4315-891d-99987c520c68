package com.thy.qa.td4p.merchandising.request.seat

import com.thy.qa.td4p.merchandising.offer.service.SeatKind
import groovy.transform.CompileStatic

@CompileStatic
class StandardSeat extends SeatServiceRequest {
    StandardSeat(int passengerIndex, int segmentIndex, SeatPosition seatPosition = SeatPosition.ANY) {
        super(passengerIndex, segmentIndex, seatPosition)
    }

    @Override
    SeatKind getSeatKind() { SeatKind.NORMAL }

    @Override
    String toString() {
        "STANDART $seatPosition seat for $passengerIndex. passenger in segment ${flightIndex}"
    }
}
