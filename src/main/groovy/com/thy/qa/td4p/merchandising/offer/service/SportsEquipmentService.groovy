package com.thy.qa.td4p.merchandising.offer.service

import com.thy.qa.td4p.merchandising.request.QuantitativeServiceRequest
import com.thy.qa.td4p.merchandising.request.SportsEquipment
import com.thy.qa.td4p.merchandising.speq.SportsEquipmentSpecifications
import com.thy.qa.td4p.merchandising.speq.SportsEquipmentType
import groovy.xml.slurpersupport.GPathResult

class SportsEquipmentService extends QuantitativeAncillaryService {
    final SportsEquipmentType sportsEquipmentType

    SportsEquipmentService(GPathResult serviceNode) {
        super(serviceNode)
        setPriceDetails(serviceNode)
        this.sportsEquipmentType = SportsEquipmentType.valueOf(serviceNode.Specification.Parameter[2].Value.text().toUpperCase())
    }

    @Override
    String getSsrCode() { sportsEquipmentType.ssrCode() }

    @Override
    ServiceCategory getServiceCategory() { ServiceCategory.SPEQ }

    @Override
    String toString() {
        return "$quantity ${sportsEquipmentType}(s): passengerIndex=$passengerIndex, segments=$segmentIndexes price=$price, discountPrice=$discountPrice, discountRate=$discountRate, currencyCode=$currencyCode"
    }

    @Override
    Closure toXml() {
        super.toXml() << segmentAssociation() << quantitative() << { ssrDescription SportsEquipmentSpecifications.of(sportsEquipmentType, quantity).toString() }
    }

    def equals(SportsEquipmentService obj) {
        obj.segmentIndexes == segmentIndexes && obj.passengerIndex == passengerIndex && obj.quantity == quantity && obj.sportsEquipmentType == sportsEquipmentType
    }

    @Override
    boolean matches(QuantitativeServiceRequest request) {
        return ((SportsEquipment) request).specification.sportsEquipmentType().ssrCode() == getSsrCode()
    }
}
