package com.thy.qa.td4p.merchandising.speq

import static com.thy.qa.td4p.merchandising.speq.SportsEquipmentType.SPORTING_FIREARMS

class SportsEquipmentSpecifications {
    static SportsEquipmentSpecification of(SportsEquipmentType speqName, int quantity) {
        return switch (speqName) {
            case SPORTING_FIREARMS -> new SportsEquipmentSpecification(speqName, quantity, new Dimension3D(3, 6, 3))
            default -> new SportsEquipmentSpecification(speqName, quantity, new Dimension3D(30, 50, 70))
        }
    }
}