package com.thy.qa.td4p.merchandising.request.seat

import com.thy.qa.td4p.merchandising.offer.service.SeatKind
import com.thy.qa.td4p.merchandising.offer.service.ServiceCategory
import com.thy.qa.td4p.merchandising.request.AssociatedServiceRequest
import groovy.transform.CompileStatic

@CompileStatic
abstract class SeatServiceRequest extends AssociatedServiceRequest {
    SeatPosition seatPosition

    SeatServiceRequest(int passengerIndex, int segmentIndex, SeatPosition seatPosition) {
        super(passengerIndex, segmentIndex)
        this.seatPosition = seatPosition
    }

    abstract SeatKind getSeatKind()

    @Override
    ServiceCategory getServiceCategory() { ServiceCategory.SEAT }

    @Override
    String toString() {
        "${getSeatKind()} $seatPosition seat for $passengerIndex. passenger in segment ${flightIndex}"
    }
}
