package com.thy.qa.td4p.merchandising.offer

import com.thy.qa.td4p.merchandising.offer.service.AncillaryService

class Offer {
    String offerId
    String offerItemId
    List<AncillaryService> services
    BigDecimal amount
    String currencyCode
    OfferType offerType

    @Override
    String toString() {
        return "Offer{" + "\nofferId=" + offerId + "\nofferItemId=" + offerItemId + "\nservices=" + services + "\namount=" + amount + "\ncurrencyCode='" + currencyCode + '\'' + "\nofferType=" + offerType + '\n}'
    }

    Closure toXml() {
        return {
            services.each { service ->
                Closure serviceXml = service.toXml()
                emdFareItem serviceXml << {
                    'pricedOffer'(OfferID: offerId, Owner: 'TK')
                    'offerItem'(OfferItemID: offerItemId, OfferKind: 'OPT')
                }
            }
        }
    }
}
