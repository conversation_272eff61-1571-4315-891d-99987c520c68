package com.thy.qa.td4p.merchandising.seat

import com.thy.qa.td4p.merchandising.request.seat.*
import groovy.transform.CompileStatic

import java.time.LocalDateTime

import static com.thy.qa.td4p.merchandising.request.seat.SeatPosition.*

@CompileStatic
class SeatMap {
    List<SeatRow> rows

    static SeatMap ofFlight(String flightNumber, LocalDateTime departureDateTime) {}

    SeatRow firstRow() { rows.first() }

    List<SeatInfo> bassinetSeats() {
        rows.seats.sum().findAll(filtering(available(), hasFeature(SeatFeature.BASSINET)))
    }

    List<SeatInfo> exitSeats() {
        rows.seats.sum().findAll(filtering(available(), hasFeature(SeatFeature.EMERGENCY_EXIT)))
    }

    List<SeatInfo> extraLegroomSeats() {
        rows.seats.sum().findAll(filtering(available(), hasFeature(SeatFeature.LEG_SPACE), hasNotFeature(SeatFeature.EMERGENCY_EXIT)))
    }

    List<SeatInfo> standardSeats() {
        rows.seats.sum().findAll(filtering(available(), hasFeature(SeatFeature.NORMAL)))
    }

    List<SeatInfo> preferredSeats() {
        rows.seats.sum().findAll(filtering(available(), hasFeature(SeatFeature.PREFERRED)))
    }

    List<SeatInfo> findAll(SeatServiceRequest seat, boolean accompaniedByInfant) {
        List<SeatInfo> filtered = rows.seats.sum().findAll(filtering(available(), ofType(seat), positioned(seat.seatPosition)))
        if (accompaniedByInfant) {
            filtered.removeAll { ((SeatInfo) it).seatFeatures.contains(SeatFeature.NON_INFANT) }
        }
        return filtered
    }

    List<List<SeatInfo>> findExtraSeatPairs(boolean accompaniedByInfant) {
        List<List<SeatInfo>> filtered = rows.findAll { !it.isAllSeatsReserved() && it.isStandardSeatRow() }.collectMany { SeatRow row ->
            List<List<SeatInfo>> pairs = row.seats.withIndex().collect { seat, index -> [seat, row.seats[index + 1]] }.dropRight(1)
            pairs.findAll { List<SeatInfo> pair ->
                pair.every {
                    (!it.reserved) && it.seatFeatures.contains(SeatFeature.NORMAL)
                } && pair.every {
                    it.position() != AISLE
                }
            }
        }
        if (accompaniedByInfant) {
            filtered.removeAll { pair -> pair.every { it.seatFeatures.contains(SeatFeature.NON_INFANT) } }
            filtered.findAll { pair -> pair[0].seatFeatures.contains(SeatFeature.NON_INFANT) }.each { it.reverse(true) }
        }
        return filtered
    }

    private Closure<Boolean> available() { { SeatInfo seatInfo -> !(seatInfo.isReserved()) } }

    private Closure<Boolean> filtering(Closure<Boolean>... conditions) {
        { SeatInfo seatInfo -> conditions.every { it(seatInfo) } }
    }

    private Closure<Boolean> ofType(SeatServiceRequest seatRequest) {
        return switch (seatRequest) {
            case StandardSeat -> hasFeature(SeatFeature.NORMAL)
            case PreferredSeat -> hasFeature(SeatFeature.PREFERRED)
            case ExitSeat -> hasFeature(SeatFeature.EMERGENCY_EXIT)
            case LegroomSeat -> { SeatInfo seatInfo -> seatInfo.seatFeatures.contains(SeatFeature.LEG_SPACE) && !seatInfo.seatFeatures.contains(SeatFeature.EMERGENCY_EXIT)
            }
            case BabyBassinetSeat -> hasFeature(SeatFeature.BASSINET)
            default -> throw new IllegalArgumentException("Unknown seat request type: $seatRequest")
        }
    }

    private Closure<Boolean> hasFeature(SeatFeature seatFeature) {
        { SeatInfo seatInfo -> seatInfo.seatFeatures.contains(seatFeature) }
    }

    private Closure<Boolean> hasNotFeature(SeatFeature seatFeature) {
        { SeatInfo seatInfo -> !seatInfo.seatFeatures.contains(seatFeature) }
    }

    private Closure<Boolean> positioned(SeatPosition seatPosition) {
        return switch (seatPosition) {
            case AISLE -> { SeatInfo seatInfo -> seatInfo.seatFeatures.contains(SeatFeature.AISLE) }
            case WINDOW -> { SeatInfo seatInfo -> seatInfo.seatFeatures.contains(SeatFeature.WINDOW) }
            case MIDDLE -> { SeatInfo seatInfo -> seatInfo.seatFeatures.contains(SeatFeature.MIDDLE) }
            case ANY -> { SeatInfo seatInfo -> true }
        }
    }
}