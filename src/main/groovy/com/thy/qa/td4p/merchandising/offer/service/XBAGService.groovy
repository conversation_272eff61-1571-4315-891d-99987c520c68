package com.thy.qa.td4p.merchandising.offer.service

import groovy.xml.slurpersupport.GPathResult

class XBAGService extends QuantitativeAncillaryService {

    XBAGService(GPathResult serviceNode) {
        super(serviceNode)
    }

    @Override
    String getSsrCode() { 'XBAG' }

    @Override
    ServiceCategory getServiceCategory() { ServiceCategory.XBAG }

    @Override
    String toString() {
        return "$quantity $unit extra baggage(s): passengerIndex=$passengerIndex, segments=$segmentIndexes, price=$price, discountPrice=$discountPrice, discountRate=$discountRate, currencyCode=$currencyCode"
    }

    @Override
    Closure toXml() { super.toXml() << segmentAssociation() << quantitative() }

    def equals(XBAGService obj) {
        obj.segmentIndexes == segmentIndexes && obj.passengerIndex == passengerIndex && obj.quantity == quantity && obj.unit == unit
    }
}
