package com.thy.qa.td4p.merchandising.request

import groovy.transform.CompileStatic

@CompileStatic
abstract class AssociatedServiceRequest extends ServiceRequest {
    final int passengerIndex
    final int flightIndex

    AssociatedServiceRequest(int passengerIndex, int flightIndex) {
        this.passengerIndex = passengerIndex
        this.flightIndex = flightIndex
    }

    boolean isQuantitative() { false }
}
