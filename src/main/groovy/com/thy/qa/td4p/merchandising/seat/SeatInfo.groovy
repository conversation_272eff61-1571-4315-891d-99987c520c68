package com.thy.qa.td4p.merchandising.seat

import com.thy.qa.td4p.merchandising.offer.service.SeatKind
import com.thy.qa.td4p.merchandising.request.seat.SeatPosition
import groovy.transform.CompileStatic

@CompileStatic
class SeatInfo {
    String seatNumber
    SeatKind seatKind
    List<SeatFeature> seatFeatures
    boolean reserved = false

    SeatPosition position() {
        if (seatFeatures.contains(SeatFeature.WINDOW)) {
            return SeatPosition.WINDOW
        } else if (seatFeatures.contains(SeatFeature.AISLE)) {
            return SeatPosition.AISLE
        } else {
            return SeatPosition.MIDDLE
        }
    }

    boolean isChargeable() {
        return seatFeatures.contains(SeatFeature.CHARGEABLE)
    }
}