package com.thy.qa.td4p.merchandising.offer

enum AncillaryItemType {
    _04C, _0B5, _0DG, _05P, _0O0, _0OX, _0BS, _0BT, _0NY, _0DD, _0EC, _0FS, _0FO, _07W, _0FR, _0PF,
    _0EI, _OED, _OIU, _0FT, _0EE, _0D6, _0FZ, _0O7, _0F1, _0G0, _0KU, _0GO, _0IU, _05R, _0F0, _0IS

    @Override
    String toString() {
        return name().substring(1)
    }

    static AncillaryItemType fromString(String ancillaryItemType) {
        return valueOf("_" + ancillaryItemType)
    }
}