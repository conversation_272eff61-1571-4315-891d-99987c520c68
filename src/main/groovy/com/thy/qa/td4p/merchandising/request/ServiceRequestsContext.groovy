package com.thy.qa.td4p.merchandising.request

import com.thy.qa.td4p.Pnr
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.environment.TroyaEnvironment
import com.thy.qa.td4p.payments.CreditCardPayment
import com.thy.qa.td4p.payments.Payment
import com.thy.qa.td4p.request.RequestHeader

import java.time.LocalDateTime
import java.time.YearMonth
import java.time.ZoneId
import java.time.format.DateTimeFormatter

class ServiceRequestsContext {
    Enum<Channel> channel = Channel.WEB3
    Payment payment = new CreditCardPayment(YearMonth.of(2030, 02), "123", "5610591081018250")

    final List serviceRequests

    private Map propertiesMap = [pnr: new Pnr()]

    Map getPropertiesMap() { propertiesMap }

    /** This constructor is for setting default Ancillary Services Context Parameters. */
    ServiceRequestsContext(List<AssociatedServiceRequest> serviceRequests) {
        this.serviceRequests = serviceRequests
    }

    void setTestCaseContextMap() {
        propertiesMap['serviceRequests'] = serviceRequests
        propertiesMap['payment'] = payment
        LocalDateTime currentLocalDateTime = LocalDateTime.now(ZoneId.systemDefault())
        propertiesMap['currentLocalDateTime'] = currentLocalDateTime
        propertiesMap['sessionId'] = RequestHeader.getSID_PREFIX() + '-' + currentLocalDateTime.format(DateTimeFormatter.ofPattern('ddMMyy-HHmmssSSSSS'))
        propertiesMap['channel'] = channel
    }
}
