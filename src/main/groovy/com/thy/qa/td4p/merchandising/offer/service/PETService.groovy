package com.thy.qa.td4p.merchandising.offer.service

import com.thy.qa.td4p.merchandising.pet.PetType
import groovy.xml.slurpersupport.GPathResult

import static com.thy.qa.td4p.merchandising.offer.service.ServiceCategory.PETC

class PETService extends QuantitativeAncillaryService {
    final IntRange weightRange
    final ServiceCategory category

    PETService(GPathResult serviceNode) {
        super(serviceNode)
        category = ServiceCategory.valueOf(<EMAIL>().toUpperCase())
        GPathResult parameter = serviceNode.Specification.Parameter[1]
        weightRange = new IntRange(parameter.Value[0].toInteger(), parameter.Value[1].toInteger())
    }

    @Override
    void setQuantity(GPathResult serviceNode) {
        String family = serviceNode.Specification.Parameter[2].Value.text()
        PetType petType = category == PETC ? PetType.valueOf(family + "_" + "CABIN") : PetType.valueOf(family + "_" + "AIRCRAFT")
        setQuantity(petType.getWeight())
    }

    @Override
    String getSsrCode() { category == PETC ? 'PETC' : 'AVIH' }

    @Override
    ServiceCategory getServiceCategory() { category }

    @Override
    Closure toXml() { super.toXml() << segmentAssociation() << quantitative() }

    def equals(PETService obj) {
        obj.segmentIndexes == segmentIndexes && obj.passengerIndex == passengerIndex && obj.quantity == quantity
    }

    @Override
    String toString() {
        return "${category} service: passengerIndex=$passengerIndex, segments=$segmentIndexes, price=$price, discountPrice=$discountPrice, discountRate=$discountRate, currencyCode=$currencyCode"
    }
}
