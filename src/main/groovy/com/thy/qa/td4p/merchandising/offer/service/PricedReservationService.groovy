package com.thy.qa.td4p.merchandising.offer.service

import com.thy.qa.td4p.merchandising.offer.AncillaryItemType
import groovy.xml.slurpersupport.GPathResult

class PricedReservationService extends ChargeableService implements ReservationService {

    PricedReservationService(GPathResult serviceNode) {
        super(serviceNode)
        duration = parseDuration(serviceNode)
        setPriceDetails(serviceNode)
    }

    @Override
    ServiceCategory getServiceCategory() { ServiceCategory.OPT_PRICED }

    @Override
    AncillaryItemType getAncillaryItemType() { AncillaryItemType._04C }

    @Override
    String toString() {
        return "PricedReservationService{" + "passengerIndex=" + passengerIndex + ", discountRate=" + discountRate + ", price=" + price + ", discountPrice=" + discountPrice + ", currencyCode='" + currencyCode + '\'' + ", duration=" + duration + '}'
    }

    @Override
    boolean isChargeable() { true }
}
