package com.thy.qa.td4p.merchandising.offer.service

import groovy.xml.slurpersupport.GPathResult

class LoungeService extends AssociatedAncillaryService {
    LoungeKind loungeKind
    String port

    LoungeService(GPathResult serviceNode) {
        super(serviceNode)
        setPriceDetails(serviceNode)
        this.loungeKind = LoungeKind.valueOf(serviceNode.Specification.Parameter[1].Value.text().toUpperCase())
        this.port = serviceNode.Specification.Parameter[2].Value.text()
    }

    @Override
    String getSsrCode() { 'PDLG' }

    @Override
    ServiceCategory getServiceCategory() { ServiceCategory.LNG }

    @Override
    Closure toXml() {
        super.toXml() << segmentAssociation() << { ssrDescription 'LOUNGE ACCESS' }
    }

    @Override
    String toString() {
        return "${loungeKind} lounge access: passengerIndex=$passengerIndex, price=$price, discountPrice=$discountPrice, discountRate=$discountRate, currencyCode=$currencyCode"
    }

    def equals(LoungeService obj) {
        obj.segmentIndexes == segmentIndexes && obj.passengerIndex == passengerIndex && obj.loungeKind == loungeKind && obj.port == port
    }
}
