package com.thy.qa.td4p

import com.eviware.soapui.model.testsuite.TestRunContext
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.environment.TroyaEnvironment
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCombination
import com.thy.qa.td4p.payments.CreditCardPayment
import com.thy.qa.td4p.payments.Payment
import com.thy.qa.td4p.request.RequestHeader

import java.time.Duration
import java.time.LocalDateTime
import java.time.YearMonth
import java.time.ZoneId
import java.time.format.DateTimeFormatter

class InfantAdditionContext {
    Enum<Channel> channel = Channel.WEB3
    Payment payment = new CreditCardPayment(YearMonth.of(2030, 02), "123", "5610591081018250")

    private Duration infantAdditionDelay = Duration.ofSeconds(5)

    private Map propertiesMap = [pnr: new Pnr()]

    Map getPropertiesMap() { propertiesMap }

    private PassengerCombination passengerCombination

    /**
     * @param passengersCombination -> a list of INFANT passenger(s). ( Only Infant passenger type is supported for this context.) see {@link com.thy.qa.td4p.passenger.Passenger} class docs for more info.
     *
     * @since 1.3.7.0
     *
     */
    InfantAdditionContext(PassengerCombination passengerCombination) {
        this.passengerCombination = passengerCombination
    }

    /**
     * It is evaluated as time millis. If you set this to a value more than 60000 millis, it is set to 60000. Because any delay more than 60 seconds makes no sense.<br>
     * Feel free to set any type of {@link Duration} -> second, minute, time millis..<br>
     * Because it will be converted to time millis anyway.
     * @since 1.3.7.0
     */
    void setInfantAdditionDelay(Duration infantAdditionDelay) {
        this.infantAdditionDelay = infantAdditionDelay
    }

    void setTestCaseContextMap() {
        setInfantAdditionParameters()
        setMap()
        propertiesMap['channel'] = channel
    }

    void setContextParameters(TestRunContext context) {
        setTestCaseContextMap()
        propertiesMap.each { String key, def value -> context[key] = value }
    }

    private void setInfantAdditionParameters() {
        List<Passenger> passengers = passengerCombination.pax
        propertiesMap['seats'] = passengers.countBy { it.passengerCode.name() }*.value
        propertiesMap['passengerCodes'] = passengers*.passengerCode
        propertiesMap['names'] = passengers*.name
        propertiesMap['surnames'] = passengers*.surname
        propertiesMap['birthDates'] = passengers*.birthDate.collect { (it) ? it.toString() : '' }
        propertiesMap['tcNos'] = passengers*.tcNo
        propertiesMap['nationalities'] = passengers.collect { it.nationality.name() }
    }

    private void setMap() {
        propertiesMap['payment'] = payment

        LocalDateTime currentLocalDateTime = LocalDateTime.now(ZoneId.systemDefault())
        propertiesMap['currentLocalDateTime'] = currentLocalDateTime
        propertiesMap['sessionId'] = RequestHeader.getSID_PREFIX() + '-' + currentLocalDateTime.format(DateTimeFormatter.ofPattern('ddMMyy-HHmmssSSSSS'))

        propertiesMap['infantAdditionDelay'] = (infantAdditionDelay.compareTo(Duration.ofMinutes(1)) > 0) ? 60000 : infantAdditionDelay.toMillis()
    }
}