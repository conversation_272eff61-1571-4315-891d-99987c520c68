package com.thy.qa.td4p.cancellation

import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCase
import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCaseRunner
import com.eviware.soapui.model.testsuite.TestRunListener
import com.eviware.soapui.support.XmlHolder
import com.eviware.soapui.support.types.StringToObjectMap
import com.thy.qa.td4p.Pnr
import com.thy.qa.td4p.ProjectManager.ProjectType
import com.thy.qa.td4p.TestCaseImpl
import com.thy.qa.td4p.environment.CommonDCSEnvironment
import com.thy.qa.td4p.environment.MSServicesEnvironment
import com.thy.qa.td4p.listeners.TestStepListener

/**
 * The GetEmd class represents an entry to the application. Once it's initialized, Soapui project is loaded and any test case of which gets ready to be run.
 * <p>
 * The class GetEmd includes methods for generating EMD of types which is only RDL Emd for the moment. (More EMD Types will be supported in later versions.)
 *
 * @since 1.4.1.0
 *
 * <AUTHOR> AYTEKİN
 */
class Refund implements TestCaseImpl {

    private static ProjectType getProjectType() { ProjectType.TD4P }

    private Map propertiesMap = [pnr: new Pnr()]

    /** the runner object to retrieve test results */
    private WsdlTestCaseRunner runner = null

    /** the testCase object to be run */
    private WsdlTestCase testCase = null

    /** the testCaseName object to set test case name */
    private String testCaseName = ''

    private TestRunListener testStepListener = new TestStepListener()

    /**
     * refunds ticket(s) on the PNR with the specified option (as correlationID) defined in the RefundContext
     *
     * @param pnrNumber
     * @param surname
     * @param RefundContext refundContext
     *
     * @return Depending on the CorrelationID value, either returns a List of Emd Info(s) created from refunded tickets or returns a refunded PNR and Surname.
     *
     * @since 1.4.1.0
     */
    List<List<String>> refundTicketedPnr(String pnrNumber, String surname, RefundContext refundContext) {
        setTestCaseMap(refundContext)
        propertiesMap['pnr'].pnrNumber = pnrNumber
        propertiesMap['surname'] = surname
        setTestCaseName(pnrNumber)
        testCase = project.getTestSuiteAt(5).cloneTestCase(project.getTestSuiteAt(5).getTestCaseAt(0), testCaseName)
        cancelTickets(testCase)
    }

    private List<List<String>> cancelTickets(WsdlTestCase testCase) {
        testCase.addAsFirstTestRunListener(testStepListener)
        runTestCase(testCase)
        sendLogs()
        if (runner.getStatus().toString().equalsIgnoreCase('PASS')) {
            String pnrNumber = runner.getRunContext().expand('${#TestCase#pnr}')
            String surname = runner.getRunContext().expand('${#TestCase#surname}')
            String correlationID = runner.getRunContext().expand('${#TestCase#CorrelationID}')
            if (correlationID == "FROEMD" || correlationID == "RDLEMD") {
                List emdList = getEmdInfo()
                log.info('The List of Emd documents that is created after cancellation of tickets in PNR(' + pnrNumber + ') is : ' + emdList)
                return emdList
            } else {
                log.info('Ticket Cancellation completed with ' + correlationID + ' method!')
                return [pnrNumber, surname]
            }

        } else {
            return ''
        }
    }

    private void runTestCase(WsdlTestCase testCase) {
        log.info("propertiesMap is set as $propertiesMap")
        log.info("sessionid is $propertiesMap.sessionId for reporting any issue or reviewing service call logs.")
        runner = testCase.run(new StringToObjectMap(propertiesMap), false)
    }

    private void setTestCaseName(String pnrNumber) {
        testCaseName = (testCaseName) ? testCaseName + '-CancelTicket' : pnrNumber + '-CancelTicket'
    }

    private void setTestCaseMap(def testCaseContext) {
        testCaseContext.setTestCaseContextMap()
        this.propertiesMap = testCaseContext.getPropertiesMap()
    }

    /**
     * Used for getting surname info after generating pnr.
     * @return surname
     */
    String getSurname() {
        runner.getTestCase().getPropertyValue('surname')
    }

    /**
     * Used for getting test step's response after generating pnr.
     * @param name the name of the test step
     * @return response
     */
    String getTestStepResponse(String name) {
        runner.getRunContext().expand('${' + name + '#Response}')
    }

    /**
     * Used for getting test step's raw request after generating pnr.
     * @param name the name of the test step
     * @return raw request
     */
    String getTestStepRequest(String name) {
        runner.getRunContext().expand('${' + name + '#RawRequest}')
    }

    /**
     * Used for getting runner object for test run results.
     * @return runner
     */
    WsdlTestCaseRunner getRunner() {
        runner
    }

    /**
     * Used for getting all created emdNumbers info after any successful method call to generate pnr.
     * @return a list of emdNumbers, names and Surnames. Each item consists of ticket number, name and surname in return.
     *
     * @since 1.3.7.0
     */
    List<List<String>> getEmdInfo() {
        XmlHolder responseHolder = new XmlHolder(runner.getTestCase().getTestStepList()[-1].getPropertyValue('Response'))
        List names = responseHolder.getNodeValues('//*:cancelTicketResponse//*:AirTraveler//*:PersonName//*:GivenName')
        List surnames = responseHolder.getNodeValues('//*:cancelTicketResponse//*:AirTraveler//*:PersonName//*:Surname')
        List emdNumbers = responseHolder.getNodeValues('//*:cancelTicketResponse//*:refundOTAResponse//*:froEMDNumbers')
        return [emdNumbers, names, surnames].transpose()
    }

    /**
     * Used for getting the reason why the test case has failed.
     * @return failure reason
     */
    String getFailureReason() {
        runner.getReason()
    }

    /**
     * Used for setting DCS Services' environment of the project. If not set, environment will be wspreprod01.*/
    void setCommonDcsEnvironment(CommonDCSEnvironment environment) {
        project.setPropertyValue('commonDcsEnvironment', environment.name().toLowerCase())
    }

    /**
     * Used for setting M&S Services' environment of the project. If not set, it will be wscrmuat.*/
    void setMSServicesEnvironment(MSServicesEnvironment environment) {
        project.setPropertyValue('msServicesEnvironment', environment.name().toLowerCase())
    }

    /**
     * Used for setting soapui project's auth profile's username.*/
    void setAuthUsername(String username) {
        project.getAuthRepository().getEntry('ykmwstestuser').setUsername(username)
    }

    /**
     * Used for setting soapui project's auth profile's password.*/
    void setAuthPassword(String password) {
        project.getAuthRepository().getEntry('ykmwstestuser').setPassword(password)
    }
}