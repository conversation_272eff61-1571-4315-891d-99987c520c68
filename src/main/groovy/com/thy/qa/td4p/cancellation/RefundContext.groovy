package com.thy.qa.td4p.cancellation

import com.eviware.soapui.model.testsuite.TestRunContext
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.enums.CorrelationID
import com.thy.qa.td4p.enums.RefundReason
import com.thy.qa.td4p.enums.TransactionIdentifier
import com.thy.qa.td4p.environment.TroyaEnvironment
import com.thy.qa.td4p.payments.CreditCardPayment
import com.thy.qa.td4p.payments.Payment
import com.thy.qa.td4p.request.RequestHeader

import java.time.Duration
import java.time.LocalDateTime
import java.time.YearMonth
import java.time.ZoneId
import java.time.format.DateTimeFormatter

class RefundContext {
    Enum<Channel> channel = Channel.WEB3
    TroyaEnvironment troyaEnvironment = TroyaEnvironment.WEBT
    Enum<CorrelationID> correlationID = CorrelationID.OnlinePayback
    Enum<RefundReason> refundReason
    Enum<TransactionIdentifier> transactionIdentifier = TransactionIdentifier.VPOS
    Payment payment = new CreditCardPayment(YearMonth.of(2030, 02), "123", "5610591081018250")
    List msNos = ["002030758"]

    private Duration cancelTicketDelay = Duration.ofSeconds(5)

    private Map propertiesMap = [:]

    Map getPropertiesMap() {
        propertiesMap
    }

    RefundContext() {}

    /**
     * @param correlationID -> a preDefined type of parameter which defines what type of refund you are intended.
     *
     * @since 1.4.1.0
     *
     */
    RefundContext(Enum<CorrelationID> correlationID) {
        this.correlationID = correlationID
    }

    /**
     * @param correlationID -> a preDefined type of parameter which defines what type of refund you are intended.
     * @param refundReason -> a preDefined type of parameter which defines the refunds voluntary status.
     *
     * @since 1.4.1.0
     *
     */
    RefundContext(Enum<CorrelationID> correlationID, Enum<RefundReason> refundReason) {
        this.correlationID = correlationID
        this.refundReason = refundReason
    }

    /**
     * It is evaluated as time millis. If you set this to a value more than 60000 millis, it is set to 60000. Because any delay more than 60 seconds makes no sense.<br>
     * Feel free to set any type of {@link Duration} -> second, minute, time millis..<br>
     * Because it will be converted to time millis anyway.
     * @since 1.4.1.0
     */
    void setCancelTicketDelay(Duration cancelTicketDelay) {
        this.cancelTicketDelay = cancelTicketDelay
    }

    void setTestCaseContextMap() {
        setRefundParameters()
        setMap()
        propertiesMap['channel'] = channel
        propertiesMap['troyaEnvironment'] = troyaEnvironment
    }

    void setContextParameters(TestRunContext context) {
        setTestCaseContextMap()
        propertiesMap.each { String key, def value -> context[key] = value }
    }

    private void setRefundParameters() {
        propertiesMap['msNos'] = msNos
        propertiesMap['correlationID'] = correlationID
        propertiesMap['refundReason'] = refundReason
        if (correlationID == CorrelationID.OnlinePayback) {
            propertiesMap['transactionIdentifier'] = transactionIdentifier
        } else {
            propertiesMap['transactionIdentifier'] = TransactionIdentifier.NonVPOS
        }
    }

    private void setMap() {
        propertiesMap['payment'] = payment
        LocalDateTime currentLocalDateTime = LocalDateTime.now(ZoneId.systemDefault())
        propertiesMap['currentLocalDateTime'] = currentLocalDateTime
        propertiesMap['sessionId'] = RequestHeader.getSID_PREFIX() + '-' + currentLocalDateTime.format(DateTimeFormatter.ofPattern('ddMMyy-HHmmssSSSSS'))

        propertiesMap['cancelTicketDelay'] = (cancelTicketDelay.compareTo(Duration.ofMinutes(1)) > 0) ? 60000 : cancelTicketDelay.toMillis()
    }
}