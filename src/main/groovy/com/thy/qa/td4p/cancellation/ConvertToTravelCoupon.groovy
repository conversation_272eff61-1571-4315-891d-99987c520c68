package com.thy.qa.td4p.cancellation

import com.thy.qa.td4p.enums.CorrelationID

/**
 * The ConvertToTravelCoupon class represents an entry to the application.
 * <p>
 * The class ConvertToTravelCoupon includes methods for generating EMD of types which is only RDL Emd for the moment. (More EMD Types will be supported in later versions.)
 *
 * @since 1.4.1.0
 *
 * <AUTHOR> AYTEKİN
 */
class ConvertToTravelCoupon {

    /**
     * Creates an RDLEMD document for each ticket by refunding all tickets in the given PNR.
     *
     * @param pnrNumber
     * @param surname
     *
     * @return Returns a List of RDLEmdNumber-Surname value(s) created from refunded tickets.Each item in this list consists an RdlEmdNumber, Name and Surname value.
     *
     * @since 1.4.1.0
     */
    List<List<String>> convertToTravelCoupons(String pnrNumber, String surname) {
        RefundContext refundContext = new RefundContext()
        Refund refund = new Refund()
        refundContext.correlationID = CorrelationID.RDLEMD
        refund.refundTicketedPnr(pnr<PERSON><PERSON><PERSON>, surname, refundContext)
    }
}