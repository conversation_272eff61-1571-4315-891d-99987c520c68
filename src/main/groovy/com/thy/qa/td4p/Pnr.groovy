package com.thy.qa.td4p

import com.thy.qa.td4p.baseclasses.FlightSegment
import com.thy.qa.td4p.baseclasses.OriginDestinationOption
import com.thy.qa.td4p.merchandising.offer.service.AncillaryService
import com.thy.qa.td4p.merchandising.seat.SeatRequest
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.pnr.PnrType

import java.time.Duration

class Pnr {
    PnrType type = PnrType.CASH_TICKET
    String pnrNumber
    List<Passenger> passengers = []
    List<OriginDestinationOption> originDestinationOptions = []
    List<SeatRequest> selectedSeats = []
    List<AncillaryService> ancillaryServices = []
    BigDecimal totalAmount
    BigDecimal ancillaryFare
    String currencyCode
    List<String> ticketNumbers = []
    Duration timeLimit

    void setPassengers(List<Passenger> passengers) {
        this.passengers = passengers
        setPassengerIndexReferences()
    }

    List<FlightSegment> getFlightSegments() {
        originDestinationOptions.collectMany { it.flightSegments }
    }

    private void setPassengerIndexReferences() {
        List<Integer> passengerIndexes = [1]
        passengers.dropRight(1).eachWithIndex { passenger, index -> passengerIndexes << passengerIndexes.last() + (passenger.hasExtraSeat() ? 2 : 1)
        }
        passengerIndexes.eachWithIndex { int passengerIndexReference, int index -> passengers[index].setIndexReference(passengerIndexReference) }
    }

}
