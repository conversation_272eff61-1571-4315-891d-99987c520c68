package com.thy.qa.td4p

import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.enums.RoutingType
import com.thy.qa.td4p.merchandising.request.WithServiceRequests
import com.thy.qa.td4p.passenger.Passenger
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.PassengerCombination
import com.thy.qa.td4p.passenger.contact.ContactInfo
import com.thy.qa.td4p.passenger.contact.CountryCode
import com.thy.qa.td4p.passenger.contact.MobilePhoneNumber
import com.thy.qa.td4p.payments.CreditCardPayment
import com.thy.qa.td4p.payments.Payment
import com.thy.qa.td4p.request.RequestHeader

import java.time.LocalDateTime
import java.time.YearMonth
import java.time.ZoneId
import java.time.format.DateTimeFormatter

/**
 * channel -> WEB3 by default. If smartmobile pnr needed then set channel enum to 'SMARTMOBIL' <br/>
 * payment -> CREDITCARD with card info(YearMonth.of(2030, 02), "123", "****************") <br/>
 * promoCode -> '' <br/>
 * airlineCodes -> empty List -> If AJ flight is needed, then set this parameter to for example -> ['AJ']. <br/>
 * retryCount -> 5 -> For routes hard to find availability options, set this parameter to a value more than 5.*/
class TestCaseContext implements WithServiceRequests {

    public static final List<String> DOMESTIC_PORT_LIST = ['DIY', 'BGG', 'DLM', 'HTY', 'BJV', 'DNZ', 'OGU', 'KCM', 'KCO', 'ONQ', 'KFS', 'SXZ', 'MLX', 'SZF', 'IGD',
                                                           'VAN', 'VAS', 'MQM', 'XHQ', 'MSR', 'EDO', 'KSY', 'MZH', 'GNY', 'ISE', 'IST', 'COV', 'ADB', 'ADF', 'KYA',
                                                           'TJK', 'KZR', 'CKZ', 'NAV', 'ERC', 'AJI', 'ERZ', 'ESB', 'GZP', 'GZT', 'NKT', 'EZS', 'ASR', 'TZX', 'NOP',
                                                           'YEI', 'AYT', 'YKO', 'SAW', 'BAL', 'ECN']

    Enum<Channel> channel = Channel.WEB3
    Payment payment = new CreditCardPayment(YearMonth.of(2030, 02), '123', '****************')
    String promoCode = ''
    List<AirlineCode> airlineCodes = []

    private Map propertiesMap = [pnr: new Pnr()]

    private final RoutingType routingType
    private final List<Flight> flights
    private final PassengerCombination passengerCombination
    private ContactInfo contactInfo

    Map getPropertiesMap() { propertiesMap }

    /**
     * @param flights consists of Flight objects every one of which has
     *  departure city, airport city, stopover value and fare type.
     * @param passengerCombination a list of passenger. see {@link com.thy.qa.td4p.passenger.Passenger} class docs for more info.
     *
     * @since 1.3.2.0
     */
    TestCaseContext(RoutingType routingType, List<Flight> flights, PassengerCombination passengerCombination) {
        this.routingType = routingType
        this.flights = flights*.validate().sort()
        this.passengerCombination = passengerCombination
    }

    /**
     * Constructor for an adult passenger.
     * @param flights consists of Flight objects every one of which has
     * departure city, airport city, stopover value and fare type.
     * @since 1.3.2.0
     */
    TestCaseContext(RoutingType routingType, List<Flight> flights) {
        this(routingType, flights, new PassengerCombination([new Passenger(PassengerCode.ADULT)]))
    }

    /**
     * Used when different email and mobile phone number are needed for the contact person.
     * @throw org.codehaus.groovy.runtime.powerassert.PowerAssertionError if passenger index is not valid.
     * @param passengerIndex
     * @param email
     * @param mobilePhoneNumber
     */
    void setContactInfo(int passengerIndex, String email, MobilePhoneNumber mobilePhoneNumber) {
        Passenger passenger = passengerCombination.pax.get(passengerIndex - 1)
        assertContactPassengerCode(passenger.passengerCode)
        String fullName = passenger.name + ' ' + passenger.surname
        contactInfo = new ContactInfo(passengerIndex, fullName, email, mobilePhoneNumber)
    }

    /**
     * Contact info is not bound to a specific passenger. It is set for all passengers.
     * So passenger index would be empty.
     * @param email contact person's email
     * @param mobilePhoneNumber contact person's mobile phone number
     */
    void setContactInfo(String fullName, String email, MobilePhoneNumber mobilePhoneNumber) {
        contactInfo = new ContactInfo(0, fullName, email, mobilePhoneNumber)
    }

    void setTestCaseContextMap() {
        setParameters()
        setMap()
        propertiesMap['channel'] = channel
        def currencyCode = channel.value().get('currencyCode')
        if (currencyCode) propertiesMap['currencyCode'] = currencyCode
    }

    private void assertContactPassengerCode(PassengerCode passengerCode) {
        assert !(passengerCode in [PassengerCode.INFANT, PassengerCode.CHILD, PassengerCode.UNACCOMPANIED]), "Contact person can't be a $passengerCode."
    }

    private void setParameters() {
        propertiesMap['flights'] = flights
        propertiesMap['portCodes'] = [flights*.origin,
                                      flights*.destination].transpose().flatten()*.toUpperCase()
        propertiesMap['isOriginMultiAirport'] = flights*.isOriginMultiAirport
        propertiesMap['isDestinationMultiAirport'] = flights*.isDestinationMultiAirport
        Map channelInfo = channel.value()
        if (channel.isAgent()) {
            passengerCombination.pax.findAll { it.passengerCode != PassengerCode.INFANT }.each {
                if (it.email == null) it.setEmail(contactEmail())
                if (it.mobilePhoneNumber == null) it.setMobilePhoneNumber(contactMobilePhoneNumber())
            }
        }
        boolean foreignSalesOffice = (channelInfo.get('requestHeaderInfo').get('clientUsername') == 'QRES' && channelInfo.get('currencyCode') != 'TRY')
        RoutingType tripType = (domestic && !foreignSalesOffice && routingType == RoutingType.ROUNDTRIP) ? RoutingType.ONEWAY : routingType
        propertiesMap['routingType'] = tripType
        propertiesMap['stopovers'] = flights*.stopover
        propertiesMap['daysToFlights'] = flights*.dayToFlight
        propertiesMap['fareTypes'] = flights*.fareType
        List<Passenger> passengers = passengerCombination.pax
        propertiesMap['pnr'].passengers = passengers
        propertiesMap['serviceRequests'] = serviceRequests
        propertiesMap['seats'] = passengers.countBy { it.passengerCode }*.value
        propertiesMap['passengerCodes'] = passengers*.passengerCode
        propertiesMap['names'] = passengers*.name
        propertiesMap['surnames'] = passengers*.surname
        propertiesMap['emails'] = passengers*.email
        propertiesMap['mobilePhoneNumbers'] = passengers*.mobilePhoneNumber
        propertiesMap['contactInfo'] = contactInfo
        propertiesMap['birthDates'] = passengers*.birthDate.collect { (it) ? it.toString() : '' }
        propertiesMap['tcNos'] = passengers*.tcNo
        propertiesMap['msNos'] = passengers*.msNo
        passengers*.jetGencNo.with {
            if (it.any()) {
                propertiesMap['jetGencNos'] = it
            }
        }
        propertiesMap['nationalities'] = passengers.collect { it.nationality.name() }
    }

    private void setMap() {
        propertiesMap['payment'] = payment

        if (airlineCodes) {
            propertiesMap['airlineCodes'] = airlineCodes*.toString()
        }

        if (promoCode) {
            propertiesMap['promoCode'] = promoCode
        }

        LocalDateTime currentLocalDateTime = LocalDateTime.now(ZoneId.systemDefault())
        propertiesMap['currentLocalDateTime'] = currentLocalDateTime
        propertiesMap['sessionId'] = RequestHeader.getSID_PREFIX() + '-' + currentLocalDateTime.format(DateTimeFormatter.ofPattern('ddMMyy-HHmmssSSS'))
    }

    private boolean isDomestic() { propertiesMap['portCodes'].every { it in DOMESTIC_PORT_LIST } }

    static String contactEmail() { "<EMAIL>" }

    static MobilePhoneNumber contactMobilePhoneNumber() {
        new MobilePhoneNumber(CountryCode.forCode("TR"), 595, 6567084)
    }

}