package com.thy.qa.td4p.environment

enum MSServicesEnvironment implements TestEnvironment {
    WSKURUMSALTEST,
    WSCRMUAT,
    WSOTOTEST

    @Override
    String getEnvironmentValue() {
        switch (this) {
            case WSKURUMSALTEST -> 'https://wskurumsaltest.thy.com'
            case WSCRMUAT -> 'https://wscrmuat.thy.com'
            case WSOTOTEST -> 'https://wsototest.thy.com'
        }
    }

    @Override
    String getEnvironmentName() { 'msServicesEnvironment' }
}