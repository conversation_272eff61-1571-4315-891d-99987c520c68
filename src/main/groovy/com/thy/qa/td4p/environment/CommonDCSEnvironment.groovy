package com.thy.qa.td4p.environment

enum CommonDCSEnvironment implements TestEnvironment {
    WSDEV,
    WSPREPROD01,
    WSKURUMSALTEST01,
    WSOTOTEST

    @Override
    String getEnvironmentValue() {
        switch (this) {
            case WSDEV -> 'https://wsdev.thy.com'
            case WSPREPROD01 -> 'https://wspreprod01.thy.com'
            case WSKURUMSALTEST01 -> 'https://wskurumsaltest01.thy.com'
            case WSOTOTEST -> 'https://wsototest.thy.com'
        }
    }

    @Override
    String getEnvironmentName() { 'commonDcsEnvironment' }
}