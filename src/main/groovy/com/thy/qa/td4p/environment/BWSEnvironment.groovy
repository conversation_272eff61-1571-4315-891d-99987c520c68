package com.thy.qa.td4p.environment

enum BWSEnvironment implements TestEnvironment {
    WSKURUMSALTEST01,
    WSDEV01,
    WSPREPROD01,
    WSKURUMSALDEV01,
    WSOTOTEST,
    WSVIRTUAL01,
    WSVIRTUAL02

    @Override
    String getEnvironmentValue() {
        switch (this) {
            case WSKURUMSALTEST01 -> 'https://wskurumsaltest01.thy.com'
            case WSDEV01 -> 'https://wsdev01.thy.com'
            case WSPREPROD01 -> 'https://wspreprod01.thy.com'
            case WSKURUMSALDEV01 -> 'https://wskurumsaldev01.thy.com'
            case WSOTOTEST -> 'https://wsototest.thy.com'
            case WSVIRTUAL01 -> 'http://10.80.27.10:7200'
            case WSVIRTUAL02 -> 'http://10.80.27.11:7200'
        }
    }

    @Override
    String getEnvironmentName() { 'bwsEnvironment' }
}