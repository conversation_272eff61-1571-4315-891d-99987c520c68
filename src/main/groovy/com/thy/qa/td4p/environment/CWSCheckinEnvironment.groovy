package com.thy.qa.td4p.environment

enum CWSCheckinEnvironment implements TestEnvironment {
    WSPREPROD01,
    WSOTOTEST,
    WSKURUMSALTEST01

    @Override
    String getEnvironmentValue() {
        switch (this) {
            case WSPREPROD01 -> 'https://wspreprod01.thy.com'
            case WSOTOTEST -> 'https://wsototest.thy.com'
            case WSKURUMSALTEST01 -> 'https://wskurumsaltest01.thy.com'
        }
    }

    @Override
    String getEnvironmentName() { 'cwsCheckinEnvironment' }
}