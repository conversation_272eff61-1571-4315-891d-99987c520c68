package com.thy.qa.td4p.membership.listener

import com.eviware.soapui.impl.wsdl.teststeps.WsdlTestRequestStep
import com.eviware.soapui.model.testsuite.*
import com.eviware.soapui.support.XmlHolder
import groovy.transform.AutoImplement
import org.apache.logging.log4j.core.Logger

@AutoImplement
class RefreshMembershipTestRunListener implements TestRunListener, MemberOperationsRequestHeaderImpl {
    Logger log

    void beforeStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStep testStep) {
        XmlHolder requestHolder = new XmlHolder(testStep.getPropertyValue('Request'))
        requestHolder.setNodeValue('//*:requestHeader//*:clientTransactionId', getClientTransactionId())
        requestHolder.setNodeValue("//*:requestHeader//*:extraParameters[@key='ALCS_SYSTEM']/@value", context.troyaEnvironment)
        requestHolder.setNodeValue('//*:ExecuteTroyaEntriesOTARequest//*:entryList', 'CH*' + context.msNo.drop(2))
        testStep.setPropertyValue('Request', requestHolder.getPrettyXml())
    }

    void afterStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStepResult testStepResult) {
        WsdlTestRequestStep executeTroyaEntriesStep = context.getTestCase().getTestStepAt(0)
        if (executeTroyaEntriesStep.assertionStatus.toString() == 'PASS') {
            log.info(executeTroyaEntriesStep.getName() + " call is successful.")
            log.info('Refreshed : membership -> ' + context.msNo)
        } else {
            log.info(executeTroyaEntriesStep.getPropertyValue('Response'))
            if (context.retryCount) {
                log.info(executeTroyaEntriesStep.getName() + " step has failed. Rerunning.")
                context.retryCount -= 1
                testRunner.gotoStep(0)
            } else {
                log.error('Refreshing could not be done')
                testRunner.fail('Refreshing has failed after trying multiple times. See console log for more details.')
            }
        }
    }
}
