package com.thy.qa.td4p.membership

import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCase
import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCaseRunner
import com.eviware.soapui.support.types.StringToObjectMap
import com.thy.qa.td4p.ProjectManager.ProjectType
import com.thy.qa.td4p.TestCaseImpl
import com.thy.qa.td4p.configuration.Configurable
import com.thy.qa.td4p.membership.context.*
import com.thy.qa.td4p.membership.listener.*

import java.time.LocalDate
import java.time.ZoneId

/**
 * The MembershipInfo class represents the entry to the application. Once it's initialized, Soapui project is loaded and any test case of which gets ready to be run.<br>
 * <br>
 * The class MembershipInfo includes methods for membership creation or membership transactions by its type.<br>
 * <br>
 * Its structure heavily depends on running the test case of the loaded project.<br>
 *
 * @since *******
 *
 * <AUTHOR> BALCILAR
 */
class MSMembership implements TestCaseImpl, Configurable<MSMembership> {

    private static ProjectType getProjectType() { ProjectType.MILES_AND_SMILES }

    private WsdlTestCaseRunner runner = null

    /**
     * This method returns a boolean value. That means refreshing membership record has been successful/unsuccessful.
     * 		The reason for returning the result as a boolean is that the user is able to call this method again in case of failure.
     * @param msNo -> As always, it must consist of a prefix and a number. (For example -> "TK457420184")
     * @return true or false depending on whether refresh command has been successful.
     */
    boolean refreshMembership(String msNo) {
        Map propertiesMap = ['msNo': msNo] << toMap()
        log.info("propertiesMap is set as $propertiesMap")
        WsdlTestCase testCase = project.getTestSuiteAt(0).cloneTestCase(getProject().getTestSuiteAt(0).getTestCaseAt(0), 'refreshMembership - ' + propertiesMap.msNo)
        testCase.addAsFirstTestRunListener(new RefreshMembershipTestRunListener(log: log))
        runTestCase(testCase, propertiesMap)
        isTestCasePassed()
    }

    /**
     * This method returns a boolean value. That means loading miles has been successful/unsuccessful.
     * 		The reason for returning the result as a boolean is that the user is able to call this method again in case of failure.
     * @param msNo -> As always, it must consist of a prefix and a number. (For example -> "TK457420184")
     * @return true or false depending on whether loading miles has been successful.
     */
    boolean loadMiles(LoadMilesContext membershipContext) {
        String activityDate = LocalDate.now(ZoneId.systemDefault()).format("dd.MM.yyyy")
        int requiredMiles = membershipContext.requiredMiles
        Map propertyMap = ['msNo': membershipContext.msNo, 'activityDate': activityDate, 'requiredMiles': requiredMiles]
        propertyMap << toMap()
        log.info("properties -> $propertyMap")
        WsdlTestCase testCase = project.getTestSuiteAt(0).cloneTestCase(project.getTestSuiteAt(0).getTestCaseAt(1), 'loadMiles - ' + propertyMap.msNo)
        testCase.addAsFirstTestRunListener(new LoadMilesTestRunListener(log: log))
        runTestCase(testCase, propertyMap)
        isTestCasePassed()
    }

    /**
     * This method is used to create a member.
     * @param createMembershipContext
     * @return M&S no for created member
     */
    String createMembership(CreateMembershipContext createMembershipContext) {
        Map propertiesMap = createMembershipContext.getTestRunContextMap()
        propertiesMap << toMap()
        log.info("propertiesMap is set as $propertiesMap")
        WsdlTestCase testCase = project.getTestSuiteAt(0).cloneTestCase(getProject().getTestSuiteAt(0).getTestCaseAt(2), 'createMembership')
        testCase.addAsFirstTestRunListener(new CreateMembershipTestRunListener(log: log))
        runTestCase(testCase, propertiesMap)
        runner.runContext.expand('${memberOperations#Response#//*:memberOperationsResponse[1]/return[1]/memberProfileData[1]/memberId}') ?: ''
    }

    /**
     * This method is used to update member according to the desired type.
     * @param updateMembershipContext
     * @return true or false depending on whether updating member has been successful.
     */
    boolean updateMembership(UpdateMembershipContext updateMembershipContext) {
        Map propertiesMap = updateMembershipContext.getTestRunContextMap()
        propertiesMap << toMap()
        log.info("propertiesMap is set as $propertiesMap")
        WsdlTestCase testCase = project.getTestSuiteAt(0).cloneTestCase(getProject().getTestSuiteAt(0).getTestCaseAt(3), 'updateMembership')
        testCase.addAsFirstTestRunListener(new UpdateMembershipTestRunListener(log: log))
        runTestCase(testCase, propertiesMap)
        isTestCasePassed()
    }

    /**
     * This method is used to add companions
     * @param addCompanionsContext
     * @return true or false depending on whether adding companions has been successful.
     */
    boolean addCompanions(AddCompanionsContext addCompanionsContext) {
        Map propertiesMap = addCompanionsContext.getTestRunContextMap()
        propertiesMap << toMap()
        log.info("propertiesMap is set as $propertiesMap")
        WsdlTestCase testCase = project.getTestSuiteAt(0).cloneTestCase(getProject().getTestSuiteAt(0).getTestCaseAt(4), 'addCompanions')
        testCase.addAsFirstTestRunListener(new AddCompanionsTestRunListener(log: log))
        runTestCase(testCase, propertiesMap)
        isTestCasePassed()
    }

    /**
     * This method is used to prefer login options
     * @param loginPreferencesContext
     * @return true or false depending on whether preferring has been successful.
     */
    boolean updateLoginPreferences(LoginPreferencesContext loginPreferencesContext) {
        Map propertiesMap = loginPreferencesContext.getTestRunContextMap()
        propertiesMap << toMap()
        log.info("propertiesMap is set as $propertiesMap")
        WsdlTestCase testCase = project.getTestSuiteAt(0).cloneTestCase(getProject().getTestSuiteAt(0).getTestCaseAt(5), 'updateLoginPreferences')
        testCase.addAsFirstTestRunListener(new LoginPreferencesTestRunListener(log: log))
        runTestCase(testCase, propertiesMap)
        isTestCasePassed()
    }

    private void runTestCase(WsdlTestCase testCase, Map propertiesMap) {
        runner = testCase.run(new StringToObjectMap(propertiesMap), false)
        sendLogs()
    }

    private boolean isTestCasePassed() { runner.getStatus().toString().equalsIgnoreCase('PASS') }

    /**
     * Used for getting the reason why the test case has failed.
     * @return failure reason
     */
    String getFailureReason() { runner.getReason() }

    /**
     * Used for getting the run context of the current test case. You can use this runner to get web service call logs.*/
    WsdlTestCaseRunner getRunner() { return runner }

}