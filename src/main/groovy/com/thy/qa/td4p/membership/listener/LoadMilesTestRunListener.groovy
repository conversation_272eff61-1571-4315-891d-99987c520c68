package com.thy.qa.td4p.membership.listener

import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCase
import com.eviware.soapui.impl.wsdl.teststeps.WsdlTestRequestStep
import com.eviware.soapui.model.testsuite.*
import com.thy.qa.td4p.request.soap.MemberNonAirAccrualActivityRequest
import groovy.transform.AutoImplement
import org.apache.logging.log4j.core.Logger

import java.math.RoundingMode

@AutoImplement
class LoadMilesTestRunListener implements TestRunListener {
    Logger log

    private int milesToLoad

    @Override
    void beforeRun(TestCaseRunner testRunner, TestCaseRunContext context) {
        int additionalStepCount = context.requiredMiles.toBigDecimal().divide(MemberNonAirAccrualActivityRequest.MAX_MILES_PER_TRANSACTION.toBigDecimal(), RoundingMode.UP) - 1
        if (additionalStepCount) {
            WsdlTestCase testCase = testRunner.getTestCase()
            WsdlTestRequestStep memberNonAirAccrualActivitytStep = testCase.getTestStepAt(0)
            String name = memberNonAirAccrualActivitytStep.name
            additionalStepCount.times { testCase.cloneStep(memberNonAirAccrualActivitytStep, name + "-" + (it + 2)) }
        }
    }

    @Override
    void afterRun(TestCaseRunner testRunner, TestCaseRunContext context) {
        WsdlTestCase testCase = testRunner.getTestCase()
        if (testCase.getTestStepAt(0).getAssertionList().flatten()*.getStatus().every { it.toString() == "PASS" }) {
            if (testRunner.results*.status.any { it.toString() == "FAIL" }) {
                testRunner.status = TestRunner.Status.FINISHED
            }
        } else {
            int pass = testCase.getTestStepList().findAll { it.getAssertionStatus() == Assertable.AssertionStatus.VALID }.size()
            log.error("${context.requiredMiles - pass * MemberNonAirAccrualActivityRequest.MAX_MILES_PER_TRANSACTION} miles were not loaded successfully.")
        }
    }

    @Override
    void beforeStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStep testStep) {
        int maxMilesPerTransaction = MemberNonAirAccrualActivityRequest.MAX_MILES_PER_TRANSACTION
        int leftMiles = context.requiredMiles - context.currentStepIndex * maxMilesPerTransaction
        milesToLoad = leftMiles > maxMilesPerTransaction ? maxMilesPerTransaction : leftMiles
        MemberNonAirAccrualActivityRequest memberNonAirAccrualActivityRequest = new MemberNonAirAccrualActivityRequest(context.msNo, milesToLoad)
        log.info('memberNonAirAccrualActivity request serialized.')
        testStep.setPropertyValue('Request', memberNonAirAccrualActivityRequest.toString())
    }

    @Override
    void afterStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStepResult testStepResult) {
        WsdlTestRequestStep memberNonAirAccrualActivityStep = context.getCurrentStep()
        if (memberNonAirAccrualActivityStep.assertionStatus.toString() == 'PASS') {
            log.info('memberNonAirAccrualActivity call is successful.')
            log.info("$milesToLoad miles loaded.")
        } else {
            log.info(memberNonAirAccrualActivityStep.getPropertyValue('Response'))
            if (context.retryCount) {
                log.info("$memberNonAirAccrualActivityStep.name step has failed. Rerunning.")
                context.retryCount -= 1
                testRunner.gotoStep(context.currentStepIndex)
            } else {
                log.error('Loading miles could not be done')
                String name = memberNonAirAccrualActivityStep.getName()
                String errorMessage = context.expand("\${$name#Response#//*:memberNonAirAccrualActivityResponse[1]/return[1]/errorDescription[1]}")
                testRunner.fail(errorMessage ?: 'An unknown error occurred. See console log for details.')
            }
        }
    }

}
