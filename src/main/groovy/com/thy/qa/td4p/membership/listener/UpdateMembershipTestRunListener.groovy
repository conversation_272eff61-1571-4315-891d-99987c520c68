package com.thy.qa.td4p.membership.listener

import com.eviware.soapui.impl.wsdl.teststeps.WsdlTestRequestStep
import com.eviware.soapui.model.testsuite.*
import com.eviware.soapui.support.XmlHolder
import com.thy.qa.td4p.membership.DiscountType
import groovy.transform.AutoImplement
import org.apache.logging.log4j.core.Logger

@AutoImplement
class UpdateMembershipTestRunListener implements TestRunListener, MemberOperationsRequestHeaderImpl {
    Logger log

    void beforeStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStep testStep) {
        XmlHolder requestHolder = new XmlHolder(testStep.getPropertyValue('Request'))
        setMemberOperationsRequestHeader(context, requestHolder)
        DiscountType discountType = context.discountType
        requestHolder.with {
            setNodeValue("//*:memberProfileData//*:memberId", context.msNo)
            setNodeValue("//*:memberProfileData//*:ptcTypes", discountType.getAbbreviation())
        }
        testStep.setPropertyValue('Request', requestHolder.getPrettyXml())
    }

    void afterStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStepResult testStepResult) {
        WsdlTestRequestStep memberOperationsStep = context.getTestCase().getTestStepAt(0)
        if (memberOperationsStep.assertionStatus.toString() == 'PASS') {
            log.info(memberOperationsStep.getName() + " call is successful.")
            log.info("Updated membership : -> $context.msNo")
        } else {
            log.info(memberOperationsStep.getPropertyValue('Response'))
            if (context.retryCount) {
                log.info(memberOperationsStep.getName() + " step has failed. Rerunning.")
                context.retryCount -= 1
                testRunner.gotoStep(0)
            } else {
                log.error('Membership creation could not be done')
                String errorMessage = context.expand('${memberOperations#Response#//*:memberOperationsResponse[1]/*:responseHeader[1]/*:messages[1]/*:localizedDescription}')
                testRunner.fail(errorMessage ?: 'An unknown error occurred. See console log for details.')
            }
        }
    }
}
