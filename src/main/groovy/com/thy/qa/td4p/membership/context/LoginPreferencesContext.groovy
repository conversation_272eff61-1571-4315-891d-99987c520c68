package com.thy.qa.td4p.membership.context

import com.thy.qa.td4p.membership.LoginPreferenceType

/**
 * This class is used to prefer login options.
 */
class LoginPreferencesContext implements ChannelImpl {
    private String msNo
    private Map propertiesMap = [:]
    private List<LoginPreferenceType> loginPreferenceTypes = []

    /**
     * @param loginPreferenceTypes LoginPreferenceType enum value. compatible for multiple preferences
     */
    LoginPreferencesContext(String msNo, List<LoginPreferenceType> loginPreferenceTypes) {
        this.msNo = msNo
        this.loginPreferenceTypes = loginPreferenceTypes
    }

    Map getTestRunContextMap() {
        propertiesMap.msNo = msNo
        propertiesMap.preferencesList = loginPreferenceTypes*.value()
        propertiesMap.requestHeaderInfo = channel.value().requestHeaderInfo
        return propertiesMap
    }

}
