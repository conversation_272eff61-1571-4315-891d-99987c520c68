package com.thy.qa.td4p.membership.listener

import com.eviware.soapui.impl.wsdl.teststeps.WsdlTestRequestStep
import com.eviware.soapui.model.testsuite.*
import com.eviware.soapui.support.XmlHolder
import groovy.transform.AutoImplement
import org.apache.logging.log4j.core.Logger
import org.w3c.dom.Node

@AutoImplement
class LoginPreferencesTestRunListener implements TestRunListener, MemberOperationsRequestHeaderImpl {
    Logger log

    void beforeStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStep testStep) {
        XmlHolder requestHolder = new XmlHolder(testStep.getPropertyValue('Request'))
        setMemberOperationsRequestHeader(context, requestHolder)
        requestHolder.setNodeValue("//*:updateMemberLoginPrefrencesDetail//*:flyerId", context.msNo)
        Node parentNode = requestHolder.getDomNode("//*:updateMemberLoginPrefrencesDetail")
        Node childNode = requestHolder.getDomNode("//*:updateMemberLoginPrefrencesDetail//*:loginPrefrencesChangingList")
        List preferencesList = context.preferencesList
        requestHolder.removeDomNodes("//*:updateMemberLoginPrefrencesDetail//*:loginPrefrencesChangingList")
        preferencesList.size().times { parentNode.appendChild(childNode.cloneNode(true)) }
        preferencesList.eachWithIndex { it, int index -> requestHolder["//*:updateMemberLoginPrefrencesDetail//*:loginPrefrencesChangingList[" + (index + 1) + "]//*:code"] = it }
        testStep.setPropertyValue('Request', requestHolder.getPrettyXml())
    }

    void afterStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStepResult testStepResult) {
        WsdlTestRequestStep updateLoginPreferencesStep = context.getTestCase().getTestStepAt(0)
        XmlHolder responseHolder = new XmlHolder(updateLoginPreferencesStep.getPropertyValue('Response'))
        if (updateLoginPreferencesStep.assertionStatus.toString() == 'PASS') {
            log.info(updateLoginPreferencesStep.getName() + " call is successful.")
        } else {
            log.info(updateLoginPreferencesStep.getPropertyValue('Response'))
            if (context.retryCount) {
                log.info(updateLoginPreferencesStep.getName() + " step has failed. Rerunning.")
                context.retryCount -= 1
                List preferencesList = context.preferencesList
                context.preferencesList = []
                List messageCodeList = responseHolder.getNodeValues('//*:updateMemberLoginPrefrencesResponse[1]/*:responseHeader[1]/*:messages/@messageCode')
                messageCodeList.eachWithIndex { it, i ->
                    if (it == '**********') {
                        context.preferencesList.add(preferencesList[i])
                    }
                }
                testRunner.gotoStep(0)
            } else {
                log.error('Update preferences could not be done')
                List errorMessage = responseHolder.getNodeValues('//*:updateMemberLoginPrefrencesResponse[1]/*:responseHeader[1]/*:messages/*:localizedDescription')
                def asStringErrorMessage = errorMessage.join(", ")
                testRunner.fail(asStringErrorMessage ?: 'An unknown error occurred. See console log for details.')
            }
        }
    }
}
