package com.thy.qa.td4p.membership.context

import com.thy.qa.td4p.membership.companion.AdultCompanion
import com.thy.qa.td4p.membership.companion.ChildCompanion
import com.thy.qa.td4p.membership.companion.Companion
import com.thy.qa.td4p.membership.companion.InfantCompanion

import java.time.LocalDateTime
import java.time.LocalTime

class AddCompanionsContext implements ChannelImpl {
    private String msNo
    private List<Companion> companions = []
    private Map propertiesMap = [:]

    /**
     * To one of constructor int value(@param adult, child, ,infant) must be sent for adult, child, infant. There are subclasses that extend from the companion class.
     * You should use the companion class to create companion information. Objects from subclasses are produced as much as these values.
     * @param adult
     * @param child
     * @param infant
     */
    AddCompanionsContext(String msNo, int adult, int child, int infant) {
        this.msNo = msNo
        adult.times { companions << new AdultCompanion() }
        child.times { companions << new ChildCompanion() }
        infant.times { companions << new InfantCompanion() }
    }

    AddCompanionsContext(String msNo, List<Companion> companions) {
        this.msNo = msNo
        this.companions = companions
    }

    Map getTestRunContextMap() {
        propertiesMap.msNo = msNo
        propertiesMap.companionDetails = companions.collect {
            [it.gender, it.name, it.surname, LocalDateTime.of(it.birthDate, LocalTime.MIN).getDateTimeString(), it.emailAddress]
        }
        propertiesMap.requestHeaderInfo = channel.value().requestHeaderInfo
        return propertiesMap
    }

}
