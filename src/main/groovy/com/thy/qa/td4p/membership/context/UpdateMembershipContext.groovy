package com.thy.qa.td4p.membership.context

import com.thy.qa.td4p.membership.DiscountType

/**
 * You should use this class for updating a member. Member type must be sent to constructor method
 * @param memberType -> MemberType enum value.
 */
class UpdateMembershipContext implements ChannelImpl {
    private String msNo
    private DiscountType discountType
    private Map propertiesMap = [:]

    /**
     * This should be used to add discount for the membership
     * @param DiscountType enum value: TEACHER, STUDENT, DISABLED, VETERAN
     */
    UpdateMembershipContext(String msNo, DiscountType discountType) {
        this.msNo = msNo
        this.discountType = discountType
    }

    Map getTestRunContextMap() {
        propertiesMap.msNo = msNo
        propertiesMap.discountType = discountType
        propertiesMap.requestHeaderInfo = channel.value().requestHeaderInfo
        return propertiesMap
    }

}
