package com.thy.qa.td4p.membership

import com.thy.qa.td4p.enums.Gender

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.concurrent.ThreadLocalRandom

abstract class Profile {
    Gender gender = Gender.M
    String name = generateName()
    String middleName= generateName()
    String surname = 'MEMBER'
    String homePhoneNumber = '4664153836'
    String mobilePhoneNumber = '579' + (0..9).shuffled()[0..6].join()
    LocalDate birthDate = generateBirthDate()
    String emailAddress = generateEmailAddress()
    String pinNumber = '885522'

    abstract MSProfileType getPassengerType()

    void setBirthDate(LocalDate birthDate) {
        assert birthDate.until(LocalDate.now()).getYears() in ageRange, "The membership must be aged between ${ageRange.getFrom()} and ${ageRange.getTo()}"
        this.birthDate = birthDate
    }

    private String generateName() {
        getPassengerType().toString() + ('a'..'z').shuffled()[0..(ThreadLocalRandom.current().nextInt(2, 6))].join()
    }

    private LocalDate generateBirthDate() {
        LocalDate.now(ZoneId.systemDefault()).minusDays(((ageRange.getFrom() * 365 + 1)..(ageRange.getTo() * 365 - 1)).shuffled().first().toLong())
    }

    private String generateEmailAddress() {
        LocalDateTime now = LocalDateTime.now(ZoneId.systemDefault())
        (name + now.format(DateTimeFormatter.ofPattern('ddMMHHmmss')) + '@thy.com').toLowerCase()
    }

    private IntRange getAgeRange() { getPassengerType().getAgeRange() }
}
