package com.thy.qa.td4p.membership.listener

import com.eviware.soapui.impl.wsdl.teststeps.WsdlTestRequestStep
import com.eviware.soapui.model.testsuite.*
import com.eviware.soapui.support.XmlHolder
import groovy.transform.AutoImplement
import org.apache.logging.log4j.core.Logger
import org.w3c.dom.Node

@AutoImplement
class AddCompanionsTestRunListener implements TestRunListener, MemberOperationsRequestHeaderImpl {
    Logger log

    void beforeStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStep testStep) {
        XmlHolder requestHolder = new XmlHolder(testStep.getPropertyValue('Request'))
        setMemberOperationsRequestHeader(context, requestHolder)
        requestHolder.setNodeValue("//*:addNewCompanionDetails/*:ownerFlyerId", context.msNo)
        Node parentNode = requestHolder.getDomNode("//*:addNewCompanionDetails")
        Node childNode = requestHolder.getDomNode("//*:addNewCompanionDetails//*:travelCompanionRecordList")
        List companionDetails = context.companionDetails
        requestHolder.removeDomNodes("//*:addNewCompanionDetails//*:travelCompanionRecordList")
        companionDetails.size().times { parentNode.appendChild(childNode.cloneNode(true)) }
        companionDetails.eachWithIndex { List companionInfo, int index ->
            requestHolder["//*:addNewCompanionDetails//*:travelCompanionRecordList[" + (index + 1) + "]//*:gender"] = companionInfo[0]
            requestHolder["//*:addNewCompanionDetails//*:travelCompanionRecordList[" + (index + 1) + "]//*:name"] = companionInfo[1]
            requestHolder["//*:addNewCompanionDetails//*:travelCompanionRecordList[" + (index + 1) + "]//*:surname"] = companionInfo[2]
            requestHolder["//*:addNewCompanionDetails//*:travelCompanionRecordList[" + (index + 1) + "]//*:birthDate"] = companionInfo[3]
            requestHolder["//*:addNewCompanionDetails//*:travelCompanionRecordList[" + (index + 1) + "]//*:email"] = companionInfo[4]
        }
        testStep.setPropertyValue('Request', requestHolder.getPrettyXml())
    }

    void afterStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStepResult testStepResult) {
        WsdlTestRequestStep addNewCompanionStep = context.getTestCase().getTestStepAt(0)
        if (addNewCompanionStep.assertionStatus.toString() == 'PASS') {
            log.info(addNewCompanionStep.getName() + " call is successful.")
        } else {
            log.info(addNewCompanionStep.getPropertyValue('Response'))
            if (context.retryCount) {
                log.info(addNewCompanionStep.getName() + " step has failed. Rerunning.")
                context.retryCount -= 1
                testRunner.gotoStep(0)
            } else {
                log.error('Adding companions could not be done')
                testRunner.fail('An unknown error occurred. See console log for details.')
            }
        }
    }
}
