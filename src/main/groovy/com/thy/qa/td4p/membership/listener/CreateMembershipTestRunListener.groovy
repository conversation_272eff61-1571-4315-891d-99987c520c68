package com.thy.qa.td4p.membership.listener

import com.eviware.soapui.impl.wsdl.teststeps.WsdlTestRequestStep
import com.eviware.soapui.model.testsuite.*
import com.eviware.soapui.support.XmlHolder
import groovy.transform.AutoImplement
import org.apache.logging.log4j.core.Logger

import java.util.concurrent.ThreadLocalRandom

@AutoImplement
class CreateMembershipTestRunListener implements TestRunListener, MemberOperationsRequestHeaderImpl {
    Logger log

    void beforeStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStep testStep) {
        XmlHolder requestHolder = new XmlHolder(testStep.getPropertyValue('Request'))
        setMemberOperationsRequestHeader(context, requestHolder)
        requestHolder.with {
            setNodeValue("//*:memberProfileData/*:gender", context.gender)
            setNodeValue("//*:memberProfileData/*:name", context.name)
            setNodeValue("//*:memberProfileData/*:middleName", context.middleName)
            setNodeValue("//*:memberProfileData/*:surname", context.surname)
            setNodeValue("//*:memberProfileData/*:dateOfBirthDay", context.dateOfBirthDay)
            setNodeValue("//*:memberProfileData/*:dateOfBirthMonth", context.dateOfBirthMonth)
            setNodeValue("//*:memberProfileData/*:dateOfBirthYear", context.dateOfBirthYear)
            setNodeValue("//*:memberProfileData/*:emailAddress", context.emailAddress)
            setNodeValue("//*:memberProfileData/*:homePhone/*:phoneNumber", context.homePhoneNumber)
            setNodeValue("//*:memberProfileData/*:mobilePhone/*:phoneNumber", context.mobilePhoneNumber)
            setNodeValue("//*:memberProfileData/*:pinNumber", context.pinNumber)
        }
        testStep.setPropertyValue('Request', requestHolder.getPrettyXml())
    }

    void afterStep(TestCaseRunner testRunner, TestCaseRunContext context, TestStepResult testStepResult) {
        WsdlTestRequestStep memberOperationsStep = context.getTestCase().getTestStepAt(0)
        if (memberOperationsStep.assertionStatus.toString() == 'PASS') {
            log.info(memberOperationsStep.getName() + " call is successful.")
            String membership = context.expand('${memberOperations#Response#//*:memberOperationsResponse[1]/return[1]/memberProfileData[1]/memberId[1]}')
            log.info('Created membership : ' + membership)
        } else {
            log.info(memberOperationsStep.getPropertyValue('Response'))
            if (context.retryCount) {
                log.info(memberOperationsStep.getName() + " step has failed. Rerunning.")
                String messageCode = context.expand('${memberOperations#Response#//*:memberOperationsResponse[1]/responseHeader[1]/*:messages[1]/@messageCode}')
                if (messageCode in ['**********', 'MS040100DUB_1']) {
                    String name = (('a'..'z') + ('A'..'Z')).shuffled()[0..(ThreadLocalRandom.current().nextInt(3, 9))].join()
                    String homePhoneNumber = '4664153836'
                    String mobilePhoneNumber = '6' + (0..9).shuffled()[0..8].join()
                    context.name = name
                    context.homePhoneNumber = homePhoneNumber
                    context.mobilePhoneNumber = mobilePhoneNumber
                    context.emailAddress = name + context.surname + '@thy.com'
                }
                context.retryCount -= 1
                testRunner.gotoStep(0)
            } else {
                log.error('Membership creation could not be done')
                String errorMessage = context.expand('${memberOperations#Response#//*:memberOperationsResponse[1]/*:responseHeader[1]/*:messages[1]/*:localizedDescription}')
                testRunner.fail(errorMessage ?: 'An unknown error occurred. See console log for details.')
            }
        }
    }
}
