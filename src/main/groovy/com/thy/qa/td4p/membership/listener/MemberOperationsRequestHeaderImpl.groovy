package com.thy.qa.td4p.membership.listener

import com.eviware.soapui.model.testsuite.TestRunContext
import com.eviware.soapui.support.XmlHolder
import com.thy.qa.td4p.request.RequestHeader

import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

trait MemberOperationsRequestHeaderImpl {

    void setMemberOperationsRequestHeader(TestRunContext context, XmlHolder requestHolder) {
        if (context.requestHeaderInfo.clientUsername == "SMARTMOBIL") {
            requestHolder["//*:requestHeader//*:clientCode"] = "SMART_MOB"
            requestHolder["//*:requestHeader//*:clientUsername"] = "SMART_MOB"
            requestHolder["//*:requestHeader//*:channel"] = "MOBILE"
        }
        requestHolder.setNodeValue("//*:requestHeader//*:clientTransactionId", getClientTransactionId())
    }

    String getClientTransactionId() {
        RequestHeader.getCTID_PREFIX() + "-1-" + LocalDateTime.now(ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern("ddMMyy-HHmmssSSS"))
    }

}