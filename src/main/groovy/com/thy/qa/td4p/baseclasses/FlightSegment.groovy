package com.thy.qa.td4p.baseclasses

import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.BrandCode

import java.time.Duration
import java.time.LocalDateTime

class FlightSegment {
    String departureAirport
    String arrivalAirport
    AirlineCode airlineCode
    String flightNumber
    AirlineCode operatingAirlineCode
    LocalDateTime departureDateTime
    LocalDateTime arrivalDateTime
    String fareBasisCode
    String resBookDesigCode
    BrandCode brandCode
    Equipment equipment
    Duration duration
    boolean validConnectionIndicator
    int rph

    FlightSegment(Map<String, ?> map) {
        this.departureAirport = map.departureAirport
        this.arrivalAirport = map.arrivalAirport
        this.airlineCode = map.airlineCode
        this.flightNumber = map.flightNumber
        this.operatingAirlineCode = map.operatingAirlineCode
        this.departureDateTime = (map.departureDateTime instanceof String) ? LocalDateTime.parse(map.departureDateTime) : map.departureDateTime
        this.arrivalDateTime = (map.arrivalDateTime instanceof String) ? LocalDateTime.parse(map.arrivalDateTime) : map.arrivalDateTime
        this.fareBasisCode = map.fareBasisCode
        this.resBookDesigCode = map.resBookDesigCode
        this.brandCode = map.brandCode
        this.equipment = new Equipment(map.equipmentCode, map.equipmentName)
        this.validConnectionIndicator = map.validConnectionIndicator.toBoolean()
    }

    boolean isBusinessClassFlight() { resBookDesigCode == 'C' }

    @Override
    String toString() {
        StringBuilder stringBuilder = new StringBuilder('Flight Segment ->\n')
        [departureAirport    : departureAirport,
         arrivalAirport      : arrivalAirport,
         airlineCode         : airlineCode,
         flightNumber        : flightNumber,
         operatingAirlineCode: operatingAirlineCode,
         departureDateTime   : departureDateTime,
         arrivalDateTime     : arrivalDateTime,
         fareBasisCode       : fareBasisCode,
         resBookDesigCode    : resBookDesigCode,
         equipmentCode       : equipment.code,
         equipmentName       : equipment.name,
         brandCode           : (brandCode) ?: '',].each { key, value -> stringBuilder.append('\t\t').append(key).append(' : ').append(value).append('\n')
        }
        stringBuilder.toString()
    }
}
