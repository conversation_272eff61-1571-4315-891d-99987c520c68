package com.thy.qa.td4p.baseclasses

import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCase
import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCaseRunner
import com.eviware.soapui.impl.wsdl.testcase.WsdlTestRunContext
import com.eviware.soapui.impl.wsdl.teststeps.WsdlTestRequestStep
import org.apache.logging.log4j.core.Logger

class TearDownScript {
    final Logger log
    final WsdlTestRunContext context
    final WsdlTestCaseRunner testRunner
    final WsdlTestCase testCase

    TearDownScript(Logger log, WsdlTestRunContext context, WsdlTestCaseRunner testRunner, WsdlTestCase testCase) {
        this.log = log
        this.context = context
        this.testRunner = testRunner
        this.testCase = testCase
    }

    void setTestCaseAsFinished() {
        if (testCase.testStepList.findAll { it instanceof WsdlTestRequestStep && it.isDisabled() == false }*.getAssertionList().flatten()*.getStatus().every { it.toString() == "PASS" } && testRunner.results*.status.any { it.toString() == "FAIL" }) {
            testRunner.status = "FINISHED"
        }
    }
}
