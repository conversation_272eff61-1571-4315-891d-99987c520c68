package com.thy.qa.td4p.baseclasses

import com.eviware.soapui.impl.wsdl.testcase.WsdlTestRunContext
import com.eviware.soapui.impl.wsdl.teststeps.WsdlTestRequestStep
import com.eviware.soapui.model.testsuite.Assertable
import com.eviware.soapui.model.testsuite.TestStep
import groovy.transform.CompileDynamic
import groovy.transform.CompileStatic

@CompileStatic
class TestStepFinder {

    private final WsdlTestRunContext context
    private final List<TestStep> testSteps

    TestStepFinder(WsdlTestRunContext context) {
        this.context = context
        this.testSteps = context.getTestCase().getTestStepList()
    }

    WsdlTestRequestStep findSuccessfulStepOfType(def serviceMethodName) {
        findSuccessfulStepByOperationName(serviceMethodName)
    }

    List findSuccessfulStepsOfType(String serviceMethodName) {
        findSuccessfulStepsByOperationName(testSteps[0..context.getCurrentStepIndex()], serviceMethodName)
    }

    int countPreviousSuccessfulStepsOfType(String serviceMethodName) {
        findSuccessfulStepsByOperationName(testSteps[0..context.getCurrentStepIndex() - 1], serviceMethodName).size()
    }

    int findIndexOfSuccessfulStepOfType(String serviceMethodName) {
        (context.getCurrentStepIndex()..0).find {
            TestStep testStep = testSteps[it]
            checkTestStepType(testStep, serviceMethodName) && checkSuccessStatus((WsdlTestRequestStep) testStep)
        }
    }

    int findIndexOfStepOfType(String serviceMethodName) {
        (context.getCurrentStepIndex()..0).find {
            checkTestStepType(testSteps[it], serviceMethodName)
        }
    }

    private List findSuccessfulStepsByOperationName(List<TestStep> steps, String serviceMethodName) {
        steps.findAll {
            checkTestStepType(it, serviceMethodName) && checkSuccessStatus((WsdlTestRequestStep) it)
        }
    }

    @CompileDynamic
    private WsdlTestRequestStep findSuccessfulStepByOperationName(def serviceMethodName) {
        (WsdlTestRequestStep) testSteps[context.getCurrentStepIndex()..0].find {
            checkTestStepType(it, serviceMethodName) && checkSuccessStatus(it)
        }
    }

    @CompileDynamic
    private boolean checkTestStepType(TestStep testStep, def operationName) {
        testStep instanceof WsdlTestRequestStep && checkTestStepOperation(testStep, operationName)
    }

    private boolean checkTestStepOperation(WsdlTestRequestStep testStep, String serviceMethodName) {
        testStep.getOperation().name.contains(serviceMethodName)
    }

    private boolean checkTestStepOperation(WsdlTestRequestStep testStep, List serviceMethodNames) {
        testStep.getOperation().name in serviceMethodNames
    }

    private boolean checkSuccessStatus(WsdlTestRequestStep testStep) {
        testStep.getAssertionStatus() == Assertable.AssertionStatus.VALID
    }

}
