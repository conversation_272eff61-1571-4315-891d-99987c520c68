package com.thy.qa.td4p.baseclasses

import com.eviware.soapui.impl.wsdl.WsdlRequest
import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCase
import com.eviware.soapui.impl.wsdl.testcase.WsdlTestRunContext
import com.eviware.soapui.impl.wsdl.teststeps.WsdlTestRequestStep
import com.eviware.soapui.impl.wsdl.teststeps.registry.WsdlTestRequestStepFactory
import com.eviware.soapui.model.testsuite.TestAssertion
import org.apache.logging.log4j.core.Logger

class SetUpTestCase {
    private WsdlTestRunContext context
    private WsdlTestCase testCase
    private Logger log

    SetUpTestCase(WsdlTestRunContext context, WsdlTestCase testCase, Logger log) {
        this.context = context
        this.testCase = testCase
        this.log = log
    }

    void addGetMemberDetailsSteps() {
        if (context.msNos) {
            context.msNos.findAll().unique().size().times { int index ->
                WsdlRequest getMemberDetailsRequestStep = testCase.testSuite.project.getInterfaceByName('MemberDetailsServicePortBinding').getOperationByName('getMemberDetails').getRequestAt(0)
                WsdlTestRequestStep getMemberDetailsTestStep = testCase.insertTestStep(WsdlTestRequestStepFactory.createConfig(getMemberDetailsRequestStep, 'TD4P-getMemberDetails'), index)
                TestAssertion assertion = getMemberDetailsTestStep.addAssertion('Contains')
                assertion.token = '**********'
                assertion.name = 'Response Contains Assertion'
            }
        }
    }

    void addGetJGMembershipInfoSteps() {
        if (context.jetGencNos) {
            context.jetGencNos.findAll().unique().size().times { int index ->
                WsdlRequest getJGMembershipInfoRequestStep = testCase.testSuite.project.getInterfaceByName('JGServicePortBinding').getOperationByName('getJGmembershipInfo').getRequestAt(0)
                WsdlTestRequestStep getJGMembershipInfoTestStep = testCase.insertTestStep(WsdlTestRequestStepFactory.createConfig(getJGMembershipInfoRequestStep, 'TD4P-getJGmembershipInfo'), index)
                TestAssertion assertion = getJGMembershipInfoTestStep.addAssertion('Contains')
                assertion.token = 'SUCCESS'
                assertion.name = 'Response Contains Assertion'
            }
        }
    }

}
