package com.thy.qa.td4p.baseclasses

import groovy.transform.CompileStatic

@CompileStatic
class OriginDestinationOption {

    final List<FlightSegment> flightSegments

    OriginDestinationOption(List<FlightSegment> flightSegments) {
        this.flightSegments = flightSegments*.asType(FlightSegment)
    }

    @Override
    String toString() {
        List segmentInfo = flightSegments.indexed().collect { index, flightSegment -> '\t' + (index + 1) + '. ' + flightSegment.toString() }
        new StringBuilder().append('OriginDestinationOption -> \n').append(segmentInfo.join('\n')).toString()
    }
}
