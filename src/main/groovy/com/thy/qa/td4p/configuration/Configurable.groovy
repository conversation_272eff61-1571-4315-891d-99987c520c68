package com.thy.qa.td4p.configuration

import com.thy.qa.td4p.environment.TestEnvironment

trait Configurable<T> implements WithTestEnvironment<T>, Retryable<T> {
    Map<String, ?> toMap() {
        //It would have been WithTestEnvironment.super.getDEFAULT_ENVIRONMENTS(). But there is a bug in Groovy
        //See - https://github.com/apache/groovy/commit/3acb6395a802b8eb68481b104ff5b328178c4acb
        Map<String, TestEnvironment> map = getDEFAULT_ENVIRONMENTS() + getEnvironments()
        // transform the map to a map of environment values
        [*: map.collectEntries { key, value -> [key, value.environmentValue] }, retryCount: getRetryCount()]
    }
}