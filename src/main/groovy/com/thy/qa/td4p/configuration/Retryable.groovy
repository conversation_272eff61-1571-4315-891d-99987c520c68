package com.thy.qa.td4p.configuration

import groovy.transform.Synchronized

trait Retryable<T> {
    private int retryCount

    private static int DEFAULT_RETRY_COUNT = 5

    T retryCount(int retry) {
        retryCount = retry
        (T) this
    }

    @Synchronized
    static void setRetryCount(int count) {
        DEFAULT_RETRY_COUNT = count
    }

    int getRetryCount() { retryCount ?: DEFAULT_RETRY_COUNT }
}