package com.thy.qa.td4p.configuration

import com.thy.qa.td4p.environment.*
import groovy.transform.Synchronized

trait WithTestEnvironment<T> {
    final Map<String, TestEnvironment> environments = [:]

    private static final Map<String, TestEnvironment> DEFAULT_ENVIRONMENTS = [
            'bwsEnvironment'            : BWSEnvironment.WSOTOTEST,
            'awsEnvironment'            : AWSEnvironment.WSOTOTEST,
            'troyaEnvironment'          : TroyaEnvironment.AUTO,
            'commonDcsEnvironment'      : CommonDCSEnvironment.WSOTOTEST,
            'msServicesEnvironment'     : MSServicesEnvironment.WSCRMUAT,
            'checkinServicesEnvironment': CWSCheckinEnvironment.WSOTOTEST,
    ]

    T environment(TestEnvironment environment) {
        environments.put(environment.getEnvironmentName(), environment)
        (T) this
    }

    @Synchronized
    static void setEnvironment(TestEnvironment environment) {
        DEFAULT_ENVIRONMENTS.put(environment.getEnvironmentName(), environment)
    }

    static Map<String, TestEnvironment> getDEFAULT_ENVIRONMENTS() {
        DEFAULT_ENVIRONMENTS
    }

}