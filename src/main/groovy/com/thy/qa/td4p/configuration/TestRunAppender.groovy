package com.thy.qa.td4p.configuration

import org.apache.logging.log4j.core.*
import org.apache.logging.log4j.core.appender.AbstractAppender
import org.apache.logging.log4j.core.config.Property
import org.apache.logging.log4j.core.config.plugins.Plugin
import org.apache.logging.log4j.core.config.plugins.PluginAttribute
import org.apache.logging.log4j.core.config.plugins.PluginElement
import org.apache.logging.log4j.core.config.plugins.PluginFactory
import org.apache.logging.log4j.core.layout.PatternLayout

import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentLinkedQueue

@Plugin(name = "TestRunAppender", category = Core.CATEGORY_NAME, elementType = Appender.ELEMENT_TYPE)
class TestRunAppender extends AbstractAppender {

    private static final Map<String, ConcurrentLinkedQueue<LogEvent>> LOG_STORAGE = new ConcurrentHashMap<>()
    private static final ThreadLocal<String> CURRENT_TEST_RUN = new ThreadLocal<>()

    private static final PatternLayout LAYOUT = PatternLayout.newBuilder()
            .withPattern("%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level - %msg%n%throwable")
            .build()

    protected TestRunAppender(String name, Filter filter, Layout<? extends Serializable> layout) {
        super(name, filter, layout, true, Property.EMPTY_ARRAY)
    }

    @PluginFactory
    static TestRunAppender createAppender(@PluginAttribute("name") String name,
                                          @PluginElement("Filter") Filter filter) {
        new TestRunAppender(name, filter, LAYOUT)
    }

    @Override
    void append(LogEvent event) {
        String testRunId = CURRENT_TEST_RUN.get()
        if (testRunId) {
            LOG_STORAGE.computeIfAbsent(testRunId, k -> new ConcurrentLinkedQueue<>()).add(event.toImmutable())
        }
    }

    static List<String> getTestRunLogs(String testRunId) {
        ConcurrentLinkedQueue<LogEvent> logs = LOG_STORAGE.get(testRunId)
        if (!logs) {
            return ["No logs found for test run: " + testRunId]
        }
        return logs.collect(LAYOUT::toSerializable)
    }

    static String startNewTestRun() {
        String testRunId = UUID.randomUUID().toString()
        CURRENT_TEST_RUN.set(testRunId)
        return testRunId
    }

    static void clearTestRunLogs() {
        long epochSecond = Instant.now().minus(5, ChronoUnit.MINUTES).getEpochSecond()
        LOG_STORAGE.removeAll { key, value -> value[0].instant.getEpochSecond() < epochSecond }
    }
}