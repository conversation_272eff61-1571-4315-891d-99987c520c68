package com.thy.qa.td4p.payandfly

import com.thy.qa.td4p.Pnr
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.environment.TroyaEnvironment
import com.thy.qa.td4p.payments.CreditCardPayment
import com.thy.qa.td4p.payments.Payment
import com.thy.qa.td4p.pnr.Flow
import com.thy.qa.td4p.request.RequestHeader

import java.time.LocalDateTime
import java.time.YearMonth
import java.time.ZoneId
import java.time.format.DateTimeFormatter

class PayAndFlyContext {
    Enum<Channel> channel = Channel.WEB3
    Payment payment = new CreditCardPayment(YearMonth.of(2030, 02), '123', '5610591081018250')

    private Map propertiesMap = [pnr: new Pnr()]

    PayAndFlyContext(String pnrNumber, String surname) {
        propertiesMap['pnr'].pnrNumber = pnrNumber
        propertiesMap['surname'] = surname
    }

    Map getPropertiesMap() { propertiesMap }

    void setTestCaseContextMap() {
        propertiesMap['flow'] = Flow.PAY_AND_FLY
        propertiesMap['payment'] = payment
        LocalDateTime currentLocalDateTime = LocalDateTime.now(ZoneId.systemDefault())
        propertiesMap['currentLocalDateTime'] = currentLocalDateTime
        propertiesMap['sessionId'] = RequestHeader.getSID_PREFIX() + '-' + currentLocalDateTime.format(DateTimeFormatter.ofPattern('ddMMyy-HHmmssSSS'))
        propertiesMap['channel'] = channel
    }
}
