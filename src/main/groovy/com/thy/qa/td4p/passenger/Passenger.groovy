package com.thy.qa.td4p.passenger

import com.thy.qa.td4p.enums.Gender
import com.thy.qa.td4p.enums.Nationality
import com.thy.qa.td4p.passenger.contact.MobilePhoneNumber
import groovy.transform.AutoClone

import java.time.LocalDate

/**
 * You should use this class if you wish to specially set passenger's info.
 * <br/>
 * name -> optional parameter. If not set, passenger's first name is created at runtime. <br/>
 * surname -> optional parameter. If not set, passenger's surname is created at runtime. <br/>
 * middleName -> optional parameter. If not set, passenger's middleName is created at runtime. <br/>
 * birthDate -> optional parameter. Use this parameter carefully.
 *                      Because specified date must be at a proper range.
 *                      Otherwise, pnr won't be created due to pax type and age wrong error.
 *                      If not set, passenger's birthDate is created at runtime. <br/>
 * passengerCode -> PassengerCode enum value. Default -> ADULT <br/>
 * nationality -> Nationality enum value. Default -> TR <br/>
 * msNo -> passenger's M&S no. <br/>
 * jetGencNo -> passenger's Jet Genc no. (i.e. phone number) <br/>
 * tcNo -> 32851635384 <br/>
 *
 * @since 1.3.2.0
 */
@AutoClone
class Passenger {

    String msNo = ''

    String namePrefix = 'MR'
    String name
    String surname
    String middleName
    LocalDate birthDate
    PassengerCode passengerCode = PassengerCode.ADULT
    Gender gender
    Nationality nationality = Nationality.TR
    String tcNo = '39274984174'
    String email
    MobilePhoneNumber mobilePhoneNumber
    boolean accompaniedByInfant = false

    int indexReference

    private boolean hasExtraSeat = false

    def propertyMissing(String name) {
        if (name !in ['msNo', 'jetGencNo']) throw new MissingPropertyException(name, Passenger)
        return ''
    }

    /**
     * This should be used for creating a passenger of a specific type.*/
    Passenger(PassengerCode passengerCode) { this.passengerCode = passengerCode }

    /**
     * This should be used for creating passenger with M&S no.*/
    Passenger(PassengerCode passengerCode, String msNo) {
        this(passengerCode)
        setMsNo(msNo)
    }

    /**
     * @param msNo -> As always, it must consist of a prefix and a number. (For example -> "TK457420184")
     */
    void setMsNo(String msNo) { this.msNo = msNo }

    void setEmail(String email) {
        if (passengerCode == PassengerCode.INFANT) throw new IllegalArgumentException("Infant can't have an email.")
        this.email = email
    }

    void setMobilePhoneNumber(MobilePhoneNumber mobilePhoneNumber) {
        if (passengerCode == PassengerCode.INFANT) throw new IllegalArgumentException("Infant can't have a mobile phone number.")
        this.mobilePhoneNumber = mobilePhoneNumber
    }

    void setAccompaniedByInfant(boolean accompaniedByInfant) {
        if (passengerCode == PassengerCode.INFANT && accompaniedByInfant) throw new IllegalArgumentException("Infant can't accompany another infant.")
        //if (hasExtraSeat) throw new IllegalArgumentException("Accompanied passenger can't have an extra seat.")
        this.accompaniedByInfant = accompaniedByInfant
    }

    void addExtraSeat() {
        if (passengerCode == PassengerCode.INFANT) throw new IllegalArgumentException("Infant can't have an extra seat.")
        //if (accompaniedByInfant) throw new IllegalArgumentException("Accompanied passenger can't have an extra seat.")
        this.hasExtraSeat = true
    }

    boolean hasExtraSeat() { hasExtraSeat }

    @Override
    String toString() {
        return "Passenger{name='" + name + '\'' + ", middleName='" + middleName + ", surname='" + surname + '}'
    }
}
