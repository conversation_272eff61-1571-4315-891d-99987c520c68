package com.thy.qa.td4p.passenger.contact

import groovy.transform.CompileStatic
import groovy.transform.ImmutableOptions

@CompileStatic
@ImmutableOptions(knownImmutableClasses = CountryCode)
record MobilePhoneNumber(CountryCode countryCode, int areaCode, long phoneNumber) {

    static MobilePhoneNumber of(String mobilePhoneNumber) {
        String countryCodeText = mobilePhoneNumber.substring(0, 2)
        String areaCodeText = mobilePhoneNumber.substring(2, 6)
        String phoneNumberText = mobilePhoneNumber.substring(5)
        new MobilePhoneNumber(CountryCode.forCode(countryCodeText), Integer.parseInt(areaCodeText), Long.parseLong(phoneNumberText))
    }

    @Override
    String toString() {
        return countryCode.countryAccessCode + areaCode + phoneNumber
    }
}
