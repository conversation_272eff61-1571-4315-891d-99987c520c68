package com.thy.qa.td4p

import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCase
import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCaseRunner
import com.eviware.soapui.support.XmlHolder
import com.eviware.soapui.support.types.StringToObjectMap
import com.thy.qa.td4p.ProjectManager.ProjectType
import com.thy.qa.td4p.listeners.ParesTestStepListener

/**
 * The GetDocumentInfo class represents the entry to the application. Once it's initialized, Soapui project is loaded and any test case of which gets ready to be run.<br>
 * <br>
 * The class GetDocumentInfo includes a method for providing document's form of payment info by its type.<br>
 * <br>
 * Its structure heavily depends on running the test case of the loaded project and extracting the needed document info if the test case has passed.<br>
 *
 * @since 1.3.4.1
 *
 * <AUTHOR> BALCILAR
 */
class GetDocumentInfo implements TestCaseImpl {

    private static ProjectType getProjectType() { ProjectType.PARES }

    /**
     * Used for getting document info for hot verification test.
     * @param documentNumber
     * @param documentType -> It must be one of the following types : [TKTT,RFND,EMDA]
     * @return payment info
     * @since 1.3.4.1
     */
    List<String> getPaymentInformation(String documentNumber, String documentType) {
        WsdlTestCase testCase = project.getTestSuiteAt(0).cloneTestCase(project.getTestSuiteAt(0).getTestCaseAt(0), documentNumber + '-FOPInfo')

        testCase.addAsFirstTestRunListener(new ParesTestStepListener())

        WsdlTestCaseRunner runner = testCase.run(new StringToObjectMap(['documentNumber': documentNumber, 'documentType': documentType]), false)

        sendLogs()

        if (runner.getTestCase().getTestStepAt(0).assertionStatus.toString() == 'PASS') {
            String tif = runner.getRunContext().expand('${displayTicket#Response#//*:displayTicketResponse//*:K80//*:TKTRES//*:TIF}')

            XmlHolder responseHolder = new XmlHolder(tif)

            String pnrNumber = responseHolder.getNodeValue('//*:RCI//*:BLOCK//*:PnrReference')
            String currency = responseHolder.getNodeValue('//*:MON//*:BLOCK[*:AmountTypeQualifier = "T"]//*:IsoCurerencyCode')

            int fopIndex = (documentType in ['TKTT', 'EMDA']) ? 1 : 2

            String cc = responseHolder.getNodeValue('//*:FOP_K80[' + fopIndex + ']//*:BLOCK[*:OrderId]//*:FopType')
            String bank = responseHolder.getNodeValue('//*:FOP_K80[1]//*:BLOCK[*:OrderId]//*:FreeTextMiscFop').split('/').getAt(-1)
            String cardType = responseHolder.getNodeValue('//*:FOP_K80[' + fopIndex + ']//*:BLOCK[*:OrderId]//*:VendorCodeCc')
            String amount = responseHolder.getNodeValue('//*:FOP_K80[' + fopIndex + ']//*:BLOCK[*:OrderId]//*:FormOfPaymentAmount')
            String cardNumber = responseHolder.getNodeValue('//*:FOP_K80[' + fopIndex + ']//*:BLOCK[*:OrderId]//*:AccountNumberCc')
            String orderID = responseHolder.getNodeValue('//*:FOP_K80[' + fopIndex + ']//*:BLOCK[*:OrderId]//*:OrderId')
            String paxIndex = responseHolder.getNodeValue('//*:FOP_K80[' + fopIndex + ']//*:BLOCK[*:OrderId]//*:FreeTextMiscFop').split('/').getAt(0).drop(6)

            List documentInfo = [documentNumber,
                                 pnrNumber,
                                 cc,
                                 bank,
                                 cardType,
                                 amount,
                                 cardNumber,
                                 orderID,
                                 paxIndex,
                                 currency]
            log.info('Document info listed : ' + documentInfo)
            documentInfo
        } else {
            []
        }
    }
}