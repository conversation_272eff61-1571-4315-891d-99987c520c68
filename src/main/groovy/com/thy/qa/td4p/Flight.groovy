package com.thy.qa.td4p

import com.thy.qa.td4p.enums.FareClassCode
import com.thy.qa.td4p.enums.FareType
import groovy.transform.Sortable
import groovy.transform.builder.Builder
import groovy.transform.builder.SimpleStrategy

/**
 * This class is used for setting route info.<br>
 * Origin and destination must be valid location codes.<br>
 * Stopover, dayToFlight are 0 by default.<br>
 * FareType is ANY by default.<br>*/
@Builder(builderStrategy = SimpleStrategy, prefix = '')
@Sortable(includes = 'dayToFlight')
class Flight {
    String origin
    String destination
    Boolean isOriginMultiAirport
    Boolean isDestinationMultiAirport
    int dayToFlight
    int stopover
    FareType fareType = FareType.ANY
    List<FareClassCode> fareClassCodes = ListWithDefault.newInstance([FareClassCode.ANY], false, { FareClassCode.ANY })

    Flight validate() {
        assert origin, 'Origin is required'
        assert destination, 'Destination is required'
        assert dayToFlight >= 0, 'Day to flight must be greater than or equal to 0'
        assert stopover >= 0, 'Stopover must be greater than or equal to 0'
        assert fareClassCodes instanceof ListWithDefault || fareClassCodes.size() == stopover + 1, "Fare class codes must be equal to $stopover + 1"
        return this
    }

    @Override
    String toString() {
        "Flight{" + "origin='" + origin + '\'' + ", destination='" + destination + '\'' + ", dayToFlight=" + dayToFlight + ", stopover=" + stopover + ", fareType=" + fareType + ", fareClassCodes=" + fareClassCodes + '}'
    }
}
