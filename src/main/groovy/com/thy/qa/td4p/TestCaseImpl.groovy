package com.thy.qa.td4p

import com.eviware.soapui.SoapUI
import com.eviware.soapui.impl.wsdl.WsdlProject
import com.thy.qa.td4p.configuration.TestRunAppender
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.core.Logger

trait TestCaseImpl {
    private final String testRunId = TestRunAppender.startNewTestRun()

    private static final WsdlProject PROJECT = ProjectManager.getInstance().getProjectByType(getProjectType())
    private static final Logger log = LogManager.getLogger(SoapUI.class)

    private static final Logger sender = LogManager.getLogger("GrayLogSender")

    static WsdlProject getProject() { PROJECT }

    List<String> getLogs() { TestRunAppender.getTestRunLogs(testRunId) }

    Logger getLog() { log }

    void sendLogs() {
        sender.info(getLogs().join())
    }

}
