package com.thy.qa.td4p.dcsCheckin

import com.eviware.soapui.model.testsuite.TestRunContext
import com.thy.qa.td4p.enums.Channel
import com.thy.qa.td4p.environment.TroyaEnvironment
import com.thy.qa.td4p.payments.CreditCardPayment
import com.thy.qa.td4p.payments.Payment
import com.thy.qa.td4p.request.RequestHeader

import java.time.Duration
import java.time.LocalDateTime
import java.time.YearMonth
import java.time.ZoneId
import java.time.format.DateTimeFormatter

class CheckinContext {
    Enum<Channel> channel = Channel.WEB3
    TroyaEnvironment troyaEnvironment = TroyaEnvironment.WEBT
    Payment payment = new CreditCardPayment(YearMonth.of(2030, 02), "123", "5610591081018250")
    private Duration checkinDelay = Duration.ofSeconds(5)
    int apisPassengerIndex = 1
    int checkinFlightIndex = 1
    List apisFlightIndexes = [1]
    List checkinPassengerIndexes = [1]
    private Map propertiesMap = [:]

    Map getPropertiesMap() {
        propertiesMap
    }

    /**
     * This constructor is for checking in all passenger to flights that are open for checkin.
     */
    CheckinContext() {
    }

    /** This constructor is for checking in a single passenger to specified flight
     *
     * @param checkinPassengerIndexList must consist only Indexes of intended passengers.
     * @param checkinFlightIndex must be the indended flight Index.
     */
    CheckinContext(List checkinPassengerIndexList, int checkinFlightIndex) {
        this.checkinPassengerIndexes = checkinPassengerIndexList
        this.checkinFlightIndex = checkinFlightIndex
    }

    /** This constructor is for entering APIS Data for a passenger.
     *
     * @param apisPassengerIndex must be the intended passenger Index.
     * @param apisFlightIndexes must contain only APIS entry required flight indexes.
     */
    CheckinContext(int apisPassengerIndex, List apisFlightIndexes) {
        this.apisPassengerIndex = apisPassengerIndex
        this.apisFlightIndexes = apisFlightIndexes
    }

    /**
     * It is evaluated as time millis. If you set this to a value more than 60000 millis, it is set to 60000. Because any delay more than 60 seconds makes no sense.<br>
     * Feel free to set any type of {@link Duration} -> second, minute, time millis..<br>
     * Because it will be converted to time millis anyway.
     * @since 1.4.1.0
     */
    void setCheckinDelay(Duration checkinDelay) {
        this.checkinDelay = checkinDelay
    }

    void setTestCaseContextMap() {
        setParameters()
        setMap()
        propertiesMap['channel'] = channel
    }

    void setContextParameters(TestRunContext context) {
        setTestCaseContextMap()
        propertiesMap.each { String key, def value -> context[key] = value }
    }

    private void setParameters() {
        propertiesMap['checkinFlightIndex'] = checkinFlightIndex
        propertiesMap['checkinPassengerIndexes'] = checkinPassengerIndexes
        propertiesMap['apisFlightIndexes'] = apisFlightIndexes
        propertiesMap['apisPassengerIndex'] = apisPassengerIndex
    }

    private void setMap() {
        propertiesMap['troyaEnvironment'] = troyaEnvironment
        propertiesMap['payment'] = payment
        LocalDateTime currentLocalDateTime = LocalDateTime.now(ZoneId.systemDefault())
        propertiesMap['currentLocalDateTime'] = currentLocalDateTime
        propertiesMap['sessionId'] = RequestHeader.getSID_PREFIX() + '-' + currentLocalDateTime.format(DateTimeFormatter.ofPattern('ddMMyy-HHmmssSSSSS'))

        propertiesMap['checkinDelay'] = (checkinDelay.compareTo(Duration.ofMinutes(1)) > 0) ? 60000 : checkinDelay.toMillis()
    }
}