package com.thy.qa.td4p.dcsCheckin

import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCase
import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCaseRunner
import com.eviware.soapui.model.testsuite.TestRunListener
import com.eviware.soapui.support.XmlHolder
import com.eviware.soapui.support.types.StringToObjectMap
import com.thy.qa.td4p.GetPnr
import com.thy.qa.td4p.Pnr
import com.thy.qa.td4p.ProjectManager.ProjectType
import com.thy.qa.td4p.TestCaseImpl
import com.thy.qa.td4p.environment.CWSCheckinEnvironment
import com.thy.qa.td4p.listeners.TestStepListener
import com.thy.qa.td4p.merchandising.request.ServiceRequestsContext
import com.thy.qa.td4p.merchandising.request.seat.SeatPosition
import com.thy.qa.td4p.merchandising.request.seat.StandardSeat

/**
 * The CheckinPnr class represents an entry to the application. Once it's initialized, Soapui project is loaded and any test case of which gets ready to be run.
 *
 * @since 1.5.0.0
 *
 * <AUTHOR> AYTEKİN
 */
class CheckinPnr implements TestCaseImpl {
    private static ProjectType getProjectType() { ProjectType.TD4P }

    private Map propertiesMap = [pnr: new Pnr()]

    /** the runner object to retrieve test results */
    private WsdlTestCaseRunner runner = null

    /** the testCase object to be run */
    private WsdlTestCase testCase = null

    /** the testCaseName object to set test case name */
    private String testCaseName = ''

    private TestRunListener testStepListener = new TestStepListener()

    /**This method completes check-in process in order of SeatSelection , ApisDataEntering and CheckIn for all passsengers and all CheckIn-Open flights.
     *
     * @param pnrNumber
     * @param surname
     */
    void doCheckinForAllPassengersAndAllFlights(String pnrNumber, String surname) {
        String response = retrieveSearchPassengerResponse(pnrNumber, surname)
        XmlHolder responseHolder = new XmlHolder(response)
        List passengerRphs = responseHolder.getNodeValues("//*:searchPassengerResponse//*:PassengerInfoList//*:PassengerInfo/@ReservationIndex")
        List infantPassengersRphs = responseHolder.getNodeValues("//*:searchPassengerResponse//*:PassengerInfoList//*:PassengerInfo[@AdultRPH != '0']/@ReservationIndex")
        List nonInfantPassengerRphs = passengerRphs - infantPassengersRphs
        List travelingWithInfant = responseHolder.getNodeValues("//*:searchPassengerResponse//*:PassengerInfoList//*:PassengerInfo[@ReservationIndex = (" + nonInfantPassengerRphs.join(",") + ")]/@TravelingWithInfant")
        List openForCheckInFlightRphs = responseHolder.getNodeValues("//*:searchPassengerResponse//*:OriginDestinationList//*:Segment/@ReservationIndex")*.asType(Integer)
        List passengerFlightIndexes = [passengerRphs*.asType(Integer), openForCheckInFlightRphs]
        log.info("passengerFlightIndexes:" + passengerFlightIndexes)
        selectStandardSeatForAllPassengersAndAllFlights(pnrNumber, surname, [nonInfantPassengerRphs*.asType(Integer), openForCheckInFlightRphs], travelingWithInfant)
        enterApisDataForAllPassengersAndAllFlights(pnrNumber, surname, passengerFlightIndexes)
        completeCheckInForAllPassengersAndAllFlights(pnrNumber, surname, passengerFlightIndexes)
        log.info("Check in completed for Pnr:" + pnrNumber)
    }

    /**
     * This method enters Apis Data for given passenger and its connected Infant passenger if there is any Infant connected to given passenger.
     *
     *
     * @param pnrNumber
     * @param surname
     * @param checkinContext
     * @return pnr
     */
    void enterAPIDocs(String pnrNumber, String surname, CheckinContext checkinContext) {
        setTestCaseMap(checkinContext)
        propertiesMap['pnr'].pnrNumber = pnrNumber
        propertiesMap['surname'] = surname
        setTestCaseName("EnterApisData-" + pnrNumber + "-" + surname)
        testCase = project.getTestSuiteAt(2).cloneTestCase(project.getTestSuiteAt(2).getTestCaseAt(3), testCaseName)
        runTestCase(testCase)
    }

    /**
     * Makes Checkin for the given Pnr
     *
     * @param pnrNumber
     * @param surname
     *
     * @return Checked In pnr
     */
    void doCheckinPnr(String pnrNumber, String surname, CheckinContext checkinContext) {
        setTestCaseMap(checkinContext)
        propertiesMap['pnr'].pnrNumber = pnrNumber
        propertiesMap['surname'] = surname
        setTestCaseName("Checkin-" + pnrNumber)
        testCaseName = "Checkin-" + pnrNumber + "-" + surname
        testCase = project.getTestSuiteAt(2).cloneTestCase(project.getTestSuiteAt(2).getTestCaseAt(2), testCaseName)
        runTestCase(testCase)
    }

    /**This method is for selecting a standart seat for every given passenger in every given flights. This feature requires SeatSelection module. so methods some lines are commented until the module has released!
     *
     * @param pnrNumber
     * @param surname
     * @param passengerFlightIndexes should be a List containing parameters like this :  [ [ passengerIndexes ] , [ flightIndexes] ] .
     * @return seatSelectedPnr
     */
    private void selectStandardSeatForAllPassengersAndAllFlights(String pnrNumber, String surname, List<List> passengerFlightIndexes, List travelingWithInfant) {
        GetPnr getPnr = new GetPnr()
        log.info("Seat Selection for All Passenger for All Checkin-Open Flights started!")
        List requestedPassengerSeatCombinations = passengerFlightIndexes.combinations()
        List ancillaryServices = requestedPassengerSeatCombinations.indexed().collect { int index, List passengerFlightCombination ->
            StandardSeat standardSeat = new StandardSeat(passengerFlightCombination[0], passengerFlightCombination[1])
            if (travelingWithInfant[index] == "true") standardSeat.setSeatPosition(SeatPosition.WINDOW)
            return standardSeat
        }
        getPnr.purchaseAncillaryServices(pnrNumber, surname, new ServiceRequestsContext(ancillaryServices))
        log.info("Seat selection completed!")
    }

    private void enterApisDataForAllPassengersAndAllFlights(String pnrNumber, String surname, List<List> passengerFlightIndexes) {
        log.info("enterApisDataForAllPassengersAndAllFlights started !")
        List apisPassengerIndexes = passengerFlightIndexes[0]
        apisPassengerIndexes.each { int apisPassengerIndex ->
            CheckinContext checkInContextForApisData = new CheckinContext(apisPassengerIndex, passengerFlightIndexes[1])
            enterAPIDocs(pnrNumber, surname, checkInContextForApisData)
        }
        log.info("enterApisDataForAllPassengersAndAllFlights completed!")
    }

    private void completeCheckInForAllPassengersAndAllFlights(String seatSelectedPnr, String surname, List<List> passengerFlightIndexes) {
        log.info("completeCheckInForAllPassengersAndAllFlights started !")
        List checkInFlightIndexes = passengerFlightIndexes[1]
        checkInFlightIndexes.each { int checkInFlightIndex ->
            CheckinContext checkInContextForCheckin = new CheckinContext(passengerFlightIndexes[0], checkInFlightIndex)
            doCheckinPnr(seatSelectedPnr, surname, checkInContextForCheckin)
        }
        log.info("completeCheckInForAllPassengersAndAllFlights completed !")
    }

    private String retrieveSearchPassengerResponse(String pnrNumber, String surname) {
        propertiesMap['pnr'].pnrNumber = pnrNumber
        propertiesMap['surname'] = surname
        testCaseName = "SearchPassenger-" + pnrNumber
        testCase = project.getTestSuiteAt(7).cloneTestCase(project.getTestSuiteAt(7).getTestCaseAt(0), testCaseName)
        testCase.addAsFirstTestRunListener(testStepListener)
        log.info("propertiesMap is set as $propertiesMap")
        runner = testCase.run(new StringToObjectMap(propertiesMap), false)
        if (runner.getStatus().toString().equalsIgnoreCase('PASS')) {
            return runner.getTestCase().getTestStepAt(0).getPropertyValue('Response')
        } else {
            return ''
        }
    }

    private void runTestCase(WsdlTestCase testCase) {
        testCase.addAsFirstTestRunListener(testStepListener)
        log.info("propertiesMap is set as $propertiesMap")
        log.info("sessionid is $propertiesMap.sessionId for reporting any issue or reviewing service call logs.")
        runner = testCase.run(new StringToObjectMap(propertiesMap), false)
        sendLogs()
        if (runner.getStatus().toString().equalsIgnoreCase('PASS')) {
            log.info("Test Case is finished with status : SUCCESS")
        } else {
            runner.fail("Test Case is finished with status : FAILURE")
        }
    }

    private void setTestCaseName(String pnrNumber) {
        testCaseName = (testCaseName) ? testCaseName + '-Checkin' : pnrNumber + '-Checkin'
    }

    private void setTestCaseMap(def checkinContext) {
        checkinContext.setTestCaseContextMap()
        this.propertiesMap << checkinContext.getPropertiesMap()
    }

    /**
     * Used for getting surname info after generating pnr.
     * @return surname
     */
    String getSurname() { runner.getTestCase().getPropertyValue('surname') }

    /**
     * Used for getting test step's response after generating pnr.
     * @param name the name of the test step
     * @return response
     */
    String getTestStepResponse(String name) {
        runner.getRunContext().expand('${' + name + '#Response}')
    }

    /**
     * Used for getting test step's raw request after generating pnr.
     * @param name the name of the test step
     * @return raw request
     */
    String getTestStepRequest(String name) {
        runner.getRunContext().expand('${' + name + '#RawRequest}')
    }

    /**
     * Used for getting runner object for test run results.
     * @return runner
     */
    WsdlTestCaseRunner getRunner() { runner }

    /**
     * Used for getting the reason why the test case has failed.
     * @return failure reason
     */
    String getFailureReason() { runner.getReason() }

    /** Used for setting soapui project's environment. If not set, environment will be wspreprod01. */
    void setCWSCheckinEnvironment(CWSCheckinEnvironment environment) {
        project.setPropertyValue('checkinServicesEnvironment', environment.name().toLowerCase())
    }

    /** Used for setting soapui project's auth profile's username. */
    void setAuthUsername(String username) {
        project.getAuthRepository().getEntry('ykmwstestuser').setUsername(username)
    }

    /** Used for setting soapui project's auth profile's password. */
    void setAuthPassword(String password) {
        project.getAuthRepository().getEntry('ykmwstestuser').setPassword(password)
    }
}