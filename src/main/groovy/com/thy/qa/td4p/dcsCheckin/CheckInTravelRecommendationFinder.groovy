package com.thy.qa.td4p.dcsCheckin

import com.eviware.soapui.model.testsuite.TestCaseRunContext
import com.eviware.soapui.model.testsuite.TestStep
import com.thy.qa.td4p.baseclasses.FlightSegment
import com.thy.qa.td4p.baseclasses.OriginDestinationOption
import com.thy.qa.td4p.enums.FlightHour
import groovy.xml.XmlSlurper
import groovy.xml.XmlUtil
import groovy.xml.slurpersupport.GPathResult
import org.apache.logging.log4j.core.Logger

import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

class CheckInTravelRecommendationFinder {

    private TestCaseRunContext context
    private Logger log
    private List<Integer> checkInFlightIndexes
    private List<FlightHour> checkInFlightHours
    private List<LocalDateTime> localDateTimes
    private TestStep createFlight
    private TestStep getCheckInInformation
    private List segmentsOpenToCheckIn
    private List segmentsFailedForCheckIn
    private List segmentsOutOfFlightHours

    private Closure<List> flightSegmentInfo = { FlightSegment it -> [it.airlineCode, it.flightNumber, it.departureAirport, it.arrivalAirport, it.departureDateTime] }

    List getSegmentsOpenToCheckIn() { segmentsOpenToCheckIn }

    List getSegmentsFailedForCheckIn() { segmentsFailedForCheckIn }

    CheckInTravelRecommendationFinder(Map<String, ?> args) {
        args.each { key, value -> this.@"$key" = value }
        this.checkInFlightHours = context.flightHours[checkInFlightIndexes]
        this.createFlight = context.testCase.getTestStepByName('createFlight')
        this.getCheckInInformation = context.testCase.getTestStepByName('getCheckinInformation')
        this.segmentsOpenToCheckIn = context.segmentsOpenToCheckIn ?: []
        this.segmentsFailedForCheckIn = context.segmentsFailedForCheckIn ?: []
        this.segmentsOutOfFlightHours = checkInFlightIndexes.collect { [] }
    }

    List<OriginDestinationOption> findTravelRecommendations(List<OriginDestinationOption> travelRecommendations) {
        travelRecommendations.findAll { flight ->
            List flightSegments = flight.flightSegments
            if (flightSegmentInfo(flightSegments.first()) in segmentsOutOfFlightHours[0]) {
                return false
            }
            if (flightSegments.any { flightSegmentInfo(it) in segmentsFailedForCheckIn }) {
                return false
            }
            return canFlightBeCheckedIn(flight, 0)
        }
    }

    int findTravelRecommendationIndex(List<List<OriginDestinationOption>> travelRecommendations) {

        int result = travelRecommendations.findIndexOf { List<OriginDestinationOption> flights ->
            flights[checkInFlightIndexes].indexed().every { Integer index, OriginDestinationOption flight ->
                if (flightSegmentInfo(flight.flightSegments.first()) in segmentsOutOfFlightHours[index]) {
                    return false
                }
                if (flight.flightSegments.any { flightSegmentInfo(it) in segmentsFailedForCheckIn }) {
                    return false
                }
                return canFlightBeCheckedIn(flight, index)
            }
        }

        if (result == -1) {
            segmentsOutOfFlightHours.eachWithIndex { List segments, int i ->
                segments.each {
                    log.info("First flight segment's time for ${checkInFlightIndexes[i] + 1}. route -> $it was not suitable for checkIn.")
                }
            }
        }

        return result
    }

    private boolean canFlightBeCheckedIn(OriginDestinationOption flight, int flightIndex) {
        FlightSegment firstSegment = flight.flightSegments[0]
        int minuteToFlight = localDateTimes[flightIndex].until(firstSegment.departureDateTime, ChronoUnit.MINUTES)
        boolean flightTimeSuitable = isFlightTimeSuitableForCheckIn(checkInFlightHours[flightIndex], minuteToFlight)
        String flightInfo = "${firstSegment.airlineCode.toString()}${firstSegment.flightNumber} - $firstSegment.departureAirport"
        if (flightTimeSuitable) {
            log.info("Time to $flightInfo -> $minuteToFlight minutes is ok.")
        } else {
            segmentsOutOfFlightHours[flightIndex] << flightSegmentInfo(firstSegment)
        }
        return (flightTimeSuitable && isFlightOpenForCheckIn(flight))
    }

    private boolean isFlightTimeSuitableForCheckIn(FlightHour flightHour, int minutes) {
        if (flightHour == FlightHour.LESSTHAN6HOURS) {
            (minutes > 90 && minutes < 360)
        } else if (flightHour == FlightHour.MORETHAN3HOURS) {
            (minutes > 180 && minutes < 1440)
        } else if (flightHour == FlightHour.MORETHAN6HOURS) {
            (minutes > 360 && minutes < 1440)
        } else {
            (minutes > 90 && minutes < 1440)
        }
    }

    private boolean isFlightOpenForCheckIn(OriginDestinationOption flight) {
        List flightSegments = flight.flightSegments
        flightSegments.every {
            List segmentInfo = flightSegmentInfo(it)
            if (segmentInfo in segmentsOpenToCheckIn) {
                log.info("FlightSegment -> $segmentInfo is already open for checkin.")
                return true
            }
            log.info('Flight segment will be opened for checkIn -> ' + segmentInfo)
            setCreateFlightRequest(it)
            def result = runCreateFlight()
            if (result == true) {
                segmentsOpenToCheckIn << segmentInfo
                return true
            } else {
                if (result != "Dcs And Reservation Mismatch") {
                    setGetCheckInInformationRequest(it)
                    boolean getCheckInInformationResult = runGetCheckInInformation()
                    if (getCheckInInformationResult) {
                        segmentsOpenToCheckIn << segmentInfo
                    } else {
                        segmentsFailedForCheckIn << segmentInfo
                    }
                    return getCheckInInformationResult
                } else {
                    segmentsFailedForCheckIn << segmentInfo
                    return false
                }
            }
        }
    }

    private def runCreateFlight() {
        for (i in 1..3) {
            String status = createFlight.run(context.testRunner, context).getStatus().toString()
            if (status == "PASS") {
                log.info("createFlight call is successful.")
                return true
            } else {
                String errorMessage = context.expand('${createFlight#Response#//*:createFlightResponse[1]/CheckinInfo[1]/ErrorList[1]/Error[1]/@Message}')
                log.info "createFlight call has failed -> $errorMessage"
                if (errorMessage == "Dcs And Reservation Mismatch") {
                    return errorMessage
                }
            }
        }
        return false
    }

    private boolean runGetCheckInInformation() {
        for (i in 1..3) {
            String checkInStatus = getCheckInInformation.run(context.testRunner, context).getStatus().toString()
            if (checkInStatus == "PASS") {
                log.info("getCheckInInformation call is successful.")
                return true
            } else {
                String getCheckInInfoErrorMessage = context.expand('${getCheckinInformation#Response#//*:getCheckinInformationResponse[1]/FlightInfo[1]/ErrorList[1]/Error[1]/@Message}')
                log.info "getCheckInInformation call has failed -> $getCheckInInfoErrorMessage"
            }
        }
        return false
    }

    private void setCreateFlightRequest(FlightSegment flightSegment) {
        String request = createFlight.getPropertyValue('Request')
        GPathResult result = new XmlSlurper().parseText(request)
        Iterator iterator = result.depthFirst()
        GPathResult segment = iterator.find { it.name() == 'Segment' }
        setSegmentNode(segment, flightSegment)
        createFlight.setPropertyValue('Request', XmlUtil.serialize(result))
        log.info("createFlight request serialized.")
    }

    private void setGetCheckInInformationRequest(FlightSegment flightSegment) {
        String request = getCheckInInformation.getPropertyValue('Request')
        GPathResult result = new XmlSlurper().parseText(request)
        Iterator iterator = result.depthFirst()
        GPathResult segment = iterator.find { it.name() == 'Segment' }
        setSegmentNode(segment, flightSegment)
        getCheckInInformation.setPropertyValue('Request', XmlUtil.serialize(result))
    }

    private void setSegmentNode(GPathResult segment, FlightSegment flightSegment) {
        segment.@MarketingAirlineCode = flightSegment.airlineCode.toString()
        segment.@FlightNumber = flightSegment.flightNumber
        GPathResult departureInformation = segment.DepartureInformation
        departureInformation.@LocationCode = flightSegment.departureAirport
        departureInformation.@DepartureDate = flightSegment.departureDateTime.toLocalDate().toString()
    }

}
