<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:env="http://schemas.xmlsoap.org/soap/envelope/">
   <env:Header />
   <soapenv:Body>
      <ns5:retrieveReservationDetail xmlns:ns5="http://service.thy.com/" xmlns:ns2="http://www.opentravel.org/OTA/2003/05" xmlns:ns3="http://www.thy.com/ws/requestHeader" xmlns:ns4="http://www.thy.com/ws/responseHeader">
         <requestHeader>
            <ns3:clientCode>WEB3</ns3:clientCode>
            <ns3:clientUsername>WEB3</ns3:clientUsername>
            <ns3:channel>WEB</ns3:channel>
            <ns3:airlineCode>TK</ns3:airlineCode>
            <ns3:application>IBE</ns3:application>
            <ns3:clientTransactionId>failclientTransactionId</ns3:clientTransactionId>
            <ns3:languageCode>EN</ns3:languageCode>
            <ns3:agencyOfficeCode>ITT</ns3:agencyOfficeCode>
            <ns3:ton></ns3:ton>
            <ns3:userAgencyCode></ns3:userAgencyCode>
            <ns3:dutyCode></ns3:dutyCode>
            <ns3:agent></ns3:agent>
            <ns3:extraParameters key="LANGUAGE" value="TR" />
            <ns3:extraParameters key="SESSION_ID" value="failsessionId" />
            <ns3:extraParameters key="CLIENT_IP" value="" />
            <ns3:extraParameters key="ALCS_SYSTEM" value="WEBT"/>
         </requestHeader>
         <ns2:OTA_ReadRQ>
            <ns2:ReadRequests>
               <ns2:ReadRequest>
                  <ns2:UniqueID ID="" />
                  <ns2:Verification>
                     <ns2:PersonName>
                        <ns2:Surname></ns2:Surname>
                     </ns2:PersonName>
                  </ns2:Verification>
               </ns2:ReadRequest>
            </ns2:ReadRequests>
         </ns2:OTA_ReadRQ>
      </ns5:retrieveReservationDetail>
   </soapenv:Body>
</soapenv:Envelope>