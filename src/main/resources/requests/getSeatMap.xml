<S:Envelope
  xmlns:S="http://schemas.xmlsoap.org/soap/envelope/"
  xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/">
  <SOAP-ENV:Header/>
  <S:Body>
    <ns5:getSeatMap xmlns:ns2="http://www.opentravel.org/OTA/2003/05"
      xmlns:ns4="http://www.thy.com/ws/requestHeader" xmlns:ns5="http://service.thy.com/">
      <requestHeader xmlns="">
        <ns4:clientCode>TKMOBILE</ns4:clientCode>
        <ns4:clientUsername>SMARTMOBIL</ns4:clientUsername>
        <ns4:channel>MOBILE</ns4:channel>
        <ns4:airlineCode>TK</ns4:airlineCode>
        <ns4:application>SMARTMOBILE</ns4:application>
        <ns4:clientTransactionId></ns4:clientTransactionId>
        <ns4:languageCode>EN</ns4:languageCode>
        <ns4:agencyOfficeCode>ITT</ns4:agencyOfficeCode>
        <ns4:ton></ns4:ton>
        <ns4:userAgencyCode></ns4:userAgencyCode>
        <ns4:dutyCode></ns4:dutyCode>
        <ns4:agent></ns4:agent>
        <ns4:currency></ns4:currency>
        <ns4:extraParameters key="CLIENT_IP" value=""/>
        <ns4:extraParameters key="SESSION_ID" value=""/>
        <ns4:extraParameters key="ALCS_SYSTEM" value=""/>
      </requestHeader>
      <ns2:OTA_AirSeatMapRQ EchoToken="" TargetName="">
        <ns2:SeatMapRequests>
          <ns2:SeatMapRequest>
            <ns2:FlightSegmentInfo DepartureDateTime="" FlightNumber="" RPH="">
              <ns2:DepartureAirport LocationCode=""/>
              <ns2:ArrivalAirport LocationCode=""/>
              <ns2:OperatingAirline Code=""/>
            </ns2:FlightSegmentInfo>
            <ns2:SeatDetails SeatUpgradeInd="true">
              <ns2:CabinClass CabinType=""/>
            </ns2:SeatDetails>
          </ns2:SeatMapRequest>
        </ns2:SeatMapRequests>
        <ns2:BookingReferenceID ID="" Type="PNR"/>
      </ns2:OTA_AirSeatMapRQ>
    </ns5:getSeatMap>
  </S:Body>
</S:Envelope>