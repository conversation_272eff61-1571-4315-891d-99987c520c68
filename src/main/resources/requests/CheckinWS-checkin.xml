<soapenv:Envelope xmlns:env="http://schemas.xmlsoap.org/soap/envelope/" xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
   <env:Header/>
   <soapenv:Body xmlns:ns5="http://com.thy.dcs.checkin.services.ws/" xmlns:ns2="http://www.thy.com/ws/requestHeader" xmlns:ns4="http://www.thy.com/ws/responseHeader" xmlns:ns3="schema.doccheck.staralliance.com">
      <ns5:checkin>
         <requestHeader>
            <ns2:clientCode>TD4P</ns2:clientCode>
            <ns2:clientUsername>TD4PUSR</ns2:clientUsername>
            <ns2:channel>WEB</ns2:channel>
            <ns2:airlineCode>TK</ns2:airlineCode>
            <ns2:application>TD4PAPP</ns2:application>
            <ns2:agent>TD</ns2:agent>
            <ns2:clientTransactionId>failclientTransactionId</ns2:clientTransactionId>
            <ns2:languageCode>TR</ns2:languageCode>
         </requestHeader>
         <TripData PnrCode="">
            <PassengerInfoList>
               <PassengerInfo AdultRPH="" CreditCardControlRequired="false" Gender="" RPH="" TravelingWithInfant="">
                  <PassengerName>
                     <NamePrefix></NamePrefix>
                     <GivenName></GivenName>
                     <Surname></Surname>
                  </PassengerName>
                  <PassengerTypeCode></PassengerTypeCode>
               </PassengerInfo>
            </PassengerInfoList>
            <OriginDestinationList>
               <OriginDestination IsReturnFlight="false">
                  <Segment FlightNumber="" MarketingAirlineCode="TK" RPH="" isSegmentInterline="false">
                     <OperatingAirline Airline="TURKISH AIRLINES" Code="TK"/>
                     <DepartureInformation DepartureDate="" DepartureTime="" LocationCode=""/>
                     <ArrivalInformation ArrivalDate="" ArrivalTime="" LocationCode=""/>
                  </Segment>
               </OriginDestination>
            </OriginDestinationList>
            <PassengerFlightInfoList>
               <PassengerFlightInfo CabinCode="" FlightRPH="" IsStandBy="false" PassengerRPH="">
                  <PaxIdentifier SeatNumber=""/>
               </PassengerFlightInfo>
            </PassengerFlightInfoList>
         </TripData>
      </ns5:checkin>
   </soapenv:Body>
</soapenv:Envelope>