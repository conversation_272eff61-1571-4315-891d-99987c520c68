<soapenv:Envelope xmlns:env="http://schemas.xmlsoap.org/soap/envelope/" xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
   <env:Header/>
   <soapenv:Body>
      <ns4:getUpdatedAvailability xmlns:ns4="http://service.thy.com/" xmlns:ns2="http://www.thy.com/ws/requestHeader">
         <requestHeader>
            <ns2:clientCode>WEB3</ns2:clientCode>
            <ns2:clientUsername>WEB3</ns2:clientUsername>
            <ns2:channel>WEB</ns2:channel>
            <ns2:airlineCode>TK</ns2:airlineCode>
            <ns2:application></ns2:application>
            <ns2:clientTransactionId>failclientTransactionId</ns2:clientTransactionId>
            <ns2:languageCode>EN</ns2:languageCode>
            <ns2:agencyOfficeCode>ITT</ns2:agencyOfficeCode>
            <ns2:ton></ns2:ton>
            <ns2:userAgencyCode></ns2:userAgencyCode>
            <ns2:agencyCode></ns2:agencyCode>
            <ns2:currency></ns2:currency>
            <ns2:agencySubCode></ns2:agencySubCode>
            <ns2:dutyCode></ns2:dutyCode>
            <ns2:agent></ns2:agent>
            <ns2:extraParameters key="SESSION_ID" value="failsessionId"/>
            <ns2:extraParameters key="CLIENT_IP" value=""/>
            <ns2:extraParameters key="ENABLE_SOLD_OUT_FLIGHTS" value="false"/>
            <ns2:extraParameters key="ALCS_SYSTEM" value="WEBT"/>
            <ns2:extraParameters key="BrandedFares" value="T"/>
         </requestHeader>
         <ns4:BoundUpdateRequest>
            <jSessionId></jSessionId>
            <pageTicket></pageTicket>
            <recommendationList>
               <recommendationId></recommendationId>
               <outboundFlightId></outboundFlightId>
               <inboundFlightId></inboundFlightId>
            </recommendationList>
            <boundToUpdate>
               <boundId></boundId>
            </boundToUpdate>
            <cabinList>
               <cabin></cabin>
               <cabin></cabin>
            </cabinList>
         </ns4:BoundUpdateRequest>
      </ns4:getUpdatedAvailability>
   </soapenv:Body>
</soapenv:Envelope>