<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.thy.com/" xmlns:req="http://www.thy.com/ws/requestHeader" xmlns:ns="http://www.opentravel.org/OTA/2003/05">
   <soapenv:Header/>
   <soapenv:Body>
      <ser:finalizeTicket>
         <!--Optional:-->
         <requestHeader>
            <clientCode xmlns="http://www.thy.com/ws/requestHeader">WEB3</clientCode>
            <clientUsername xmlns="http://www.thy.com/ws/requestHeader">WEB3</clientUsername>
            <channel xmlns="http://www.thy.com/ws/requestHeader">BATCH</channel>
            <airlineCode xmlns="http://www.thy.com/ws/requestHeader">TK</airlineCode>
            <application xmlns="http://www.thy.com/ws/requestHeader">BATCH</application>
            <agent xmlns="http://www.thy.com/ws/requestHeader">agentAPI</agent>
            <clientTransactionId xmlns="http://www.thy.com/ws/requestHeader"></clientTransactionId>
            <languageCode xmlns="http://www.thy.com/ws/requestHeader">TR</languageCode>
            <extraParameters key="BATCHID" value="94550" xmlns="http://www.thy.com/ws/requestHeader"/>
            <extraParameters key="SESSION_ID" value="" xmlns="http://www.thy.com/ws/requestHeader"/>
            <extraParameters key="ALCS_SYSTEM" value="WEBT" xmlns="http://www.thy.com/ws/requestHeader"/>
         </requestHeader>
         <!--Optional:-->
         <ser:createTicketOTARequest>
            <OTA_AirDemandTicketRQ xmlns="http://www.opentravel.org/OTA/2003/05">
               <DemandTicketDetail>
                  <MessageFunction Function="E"/>
                  <BookingReferenceID ID=""/>
                  <PaymentInfo PaymentType="eftDone"/>
                  <PassengerName>
                     <Surname></Surname>
                  </PassengerName>
                  <TPA_Extensions>
                     <TK_ContactInfo>
                        <Name/>
                        <Surname/>
                        <PassengerIndex/>
                     </TK_ContactInfo>
                     <TK_RQIntentionType>
                        <Application>OTC</Application>
                        <Coverage/>
                     </TK_RQIntentionType>
                  </TPA_Extensions>
               </DemandTicketDetail>
            </OTA_AirDemandTicketRQ>
         </ser:createTicketOTARequest>
      </ser:finalizeTicket>
   </soapenv:Body>
</soapenv:Envelope>