<S:Envelope xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:S="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/">
    <SOAP-ENV:Header/>
    <S:Body>
        <ns4:getElectronicDocuments xmlns:ns2="http://www.opentravel.org/OTA/2003/05"
            xmlns:ns3="http://www.thy.com/ws/requestHeader"
            xmlns:ns4="http://service.thy.com/"
            xmlns:ns5="http://www.thy.com/ws/responseHeader">
            <requestHeader xmlns="">
                <ns3:clientCode>WEB3</ns3:clientCode>
                <ns3:clientUsername>WEB3</ns3:clientUsername>
                <ns3:channel>WEB</ns3:channel>
                <ns3:airlineCode>TK</ns3:airlineCode>
                <ns3:application>IBE</ns3:application>
                <ns3:agencyOfficeCode>ITT</ns3:agencyOfficeCode>
                <ns3:ton></ns3:ton>
                <ns3:userAgencyCode></ns3:userAgencyCode>
                <ns3:dutyCode></ns3:dutyCode>
                <ns3:agent></ns3:agent>
                <ns3:clientTransactionId></ns3:clientTransactionId>
                <ns3:languageCode>EN</ns3:languageCode>
                <ns3:extraParameters key="LANGUAGE" value="EN"/>
                <ns3:extraParameters key="SESSION_ID" value=""/>
                <ns3:extraParameters key="CLIENT_IP" value=""/>
                <ns3:extraParameters key="ALCS_SYSTEM" value="WEBT"/>
            </requestHeader>
            <ns2:OTAReadRQ>
                <ns2:ReadRequests>
                    <ns2:AirReadRequest>
                        <ns2:Name>
                            <ns2:Surname></ns2:Surname>
                        </ns2:Name>
                        <ns2:TicketNumber TicketType="eTicket" eTicketNumber=""/>
                    </ns2:AirReadRequest>
                </ns2:ReadRequests>
            </ns2:OTAReadRQ>
        </ns4:getElectronicDocuments>
    </S:Body>
</S:Envelope>