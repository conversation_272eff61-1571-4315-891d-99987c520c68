<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.thy.com/" xmlns:req="http://www.thy.com/ws/requestHeader">
   <soapenv:Header/>
   <soapenv:Body>
      <ser:eftPrepayment>
         <!--Optional:-->
         <requestHeader>
            <clientCode xmlns="http://www.thy.com/ws/requestHeader">WEB3</clientCode>
            <clientUsername xmlns="http://www.thy.com/ws/requestHeader">WEB3</clientUsername>
            <channel xmlns="http://www.thy.com/ws/requestHeader">BATCH</channel>
            <airlineCode xmlns="http://www.thy.com/ws/requestHeader">TK</airlineCode>
            <agent xmlns="http://www.thy.com/ws/requestHeader">agentAPI</agent>
            <agencyOfficeCode xmlns="http://www.thy.com/ws/requestHeader">ITT</agencyOfficeCode>
            <ton xmlns="http://www.thy.com/ws/requestHeader"></ton>
            <userAgencyCode xmlns="http://www.thy.com/ws/requestHeader"></userAgencyCode>
            <dutyCode xmlns="http://www.thy.com/ws/requestHeader"></dutyCode>
            <agent xmlns="http://www.thy.com/ws/requestHeader"></agent>
            <clientTransactionId xmlns="http://www.thy.com/ws/requestHeader"></clientTransactionId>
            <languageCode xmlns="http://www.thy.com/ws/requestHeader">TR</languageCode>
            <extraParameters key="SESSION_ID" value="" xmlns="http://www.thy.com/ws/requestHeader"/>
            <extraParameters key="ALCS_SYSTEM" value="WEBT" xmlns="http://www.thy.com/ws/requestHeader"/>
         </requestHeader>
         <ser:batchEftOTARequest>
            <eftXmlAmount></eftXmlAmount>
            <eftXmlSenderDescFirst></eftXmlSenderDescFirst>
            <eftXmlDescription></eftXmlDescription>
            <eftXmlEftSenderName>THY THY</eftXmlEftSenderName>
            <eftXmlEftDate></eftXmlEftDate>
            <eftXmlIsEFT>true</eftXmlIsEFT>
            <eftXmlIsHavale>false</eftXmlIsHavale>
            <eftXmlCorporateCustomerNo>*********</eftXmlCorporateCustomerNo>
            <eftXmlCorporateBranchCode>1255</eftXmlCorporateBranchCode>
            <eftXmlCorporateAccount>*********</eftXmlCorporateAccount>
            <eftXmlIsbankBranchCode>1255</eftXmlIsbankBranchCode>
            <eftXmlIsbankQueryNo></eftXmlIsbankQueryNo>
            <eftXmlOtherBankQueryNo>7296272</eftXmlOtherBankQueryNo>
         </ser:batchEftOTARequest>
      </ser:eftPrepayment>
   </soapenv:Body>
</soapenv:Envelope>