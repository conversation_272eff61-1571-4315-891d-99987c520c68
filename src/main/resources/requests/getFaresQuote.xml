<soapenv:Envelope xmlns:env="http://schemas.xmlsoap.org/soap/envelope/" xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
   <env:Header/>
   <soapenv:Body>
      <ns5:getFares xmlns:ns5="http://service.thy.com/" xmlns:ns2="http://www.opentravel.org/OTA/2003/05" xmlns:ns4="http://www.thy.com/ws/responseHeader" xmlns:ns3="http://www.thy.com/ws/requestHeader">
         <requestHeader>
            <ns3:clientCode>WEB3</ns3:clientCode>
            <ns3:clientUsername>WEB3</ns3:clientUsername>
            <ns3:channel>WEB</ns3:channel>
            <ns3:airlineCode>TK</ns3:airlineCode>
            <ns3:application>IBE</ns3:application>
            <ns3:clientTransactionId>failclientTransactionId</ns3:clientTransactionId>
            <ns3:languageCode>EN</ns3:languageCode>
            <ns3:agencyOfficeCode>ITT</ns3:agencyOfficeCode>
            <ns3:ton></ns3:ton>
            <ns3:userAgencyCode></ns3:userAgencyCode>
            <ns3:dutyCode></ns3:dutyCode>
            <ns3:agent></ns3:agent>
            <ns3:extraParameters key="LANGUAGE" value="TR"/>
            <ns3:extraParameters key="SESSION_ID" value="failsessionId"/>
            <ns3:extraParameters key="CLIENT_IP" value=""/>
            <ns3:extraParameters key="ALCS_SYSTEM" value="WEBT"/>
         </requestHeader>
         <ns2:OTA_AirBookRQ ResStatus="Quote">
            <ns2:PriceInfo>
               <ns2:FareInfos>
               </ns2:FareInfos>
            </ns2:PriceInfo>
            <ns2:BookingReferenceID ID=""/>
            <ns2:Offer>
               <ns2:Priced>
                  <ns2:ServiceFamily/>
                  <ns2:Pricing/>
                  <ns2:OtherServices CodeContext="no"/>
               </ns2:Priced>
            </ns2:Offer>
         </ns2:OTA_AirBookRQ>
      </ns5:getFares>
   </soapenv:Body>
</soapenv:Envelope>