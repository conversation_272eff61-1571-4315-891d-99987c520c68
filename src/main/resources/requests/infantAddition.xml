<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.thy.com/" xmlns:req="http://www.thy.com/ws/requestHeader" xmlns:ns="http://www.opentravel.org/OTA/2003/05">
   <soapenv:Header/>
   <soapenv:Body>
      <ser:infantAddition>
         <requestHeader>
            <req:clientCode>WEB3</req:clientCode>
            <req:clientUsername>WEB3</req:clientUsername>
            <req:channel>WEB</req:channel>
            <req:airlineCode>TK</req:airlineCode>
            <req:application>IBE</req:application>
            <req:languageCode>TR</req:languageCode>
            <req:agencyOfficeCode>ITT</req:agencyOfficeCode>
            <req:ton></req:ton>
            <req:userAgencyCode></req:userAgencyCode>
            <req:dutyCode></req:dutyCode>
            <req:agent></req:agent>
            <req:clientTransactionId>failclientTransactionId</req:clientTransactionId>
            <req:extraParameters key="CLIENT_IP" value="************"/>
            <req:extraParameters key="SESSION_ID" value="failsessionId"/>
            <req:extraParameters key="PAYMENT_TRACKING_ID" value=""/>
            <req:extraParameters key="PG_APPLICATION_MODE" value="STAGING"/>
         </requestHeader>
         <ser:infantAdditionOTARequest>
            <ns:OTA_AirBookRQ>
               <ns:TravelerInfo>
                  <ns:AirTraveler BirthDate="" PassengerTypeCode="INFANT" AccompaniedByInfantInd="false" Gender="">
                     <ns:ProfileRef>
                        <ns:UniqueID ID=""/>
                     </ns:ProfileRef>
                     <ns:PersonName>
                        <ns:NamePrefix></ns:NamePrefix>
                        <ns:GivenName></ns:GivenName>
                        <ns:Surname></ns:Surname>
                        <ns:Document DocID="" DocType="identityNumber"/>
                     </ns:PersonName>
                  </ns:AirTraveler>
               </ns:TravelerInfo>
               <ns:Fulfillment>
                  <ns:Name>
                     <ns:Surname></ns:Surname>
                  </ns:Name>
               </ns:Fulfillment>
               <ns:BookingReferenceID Type="PNR" ID=""/>
               <ns:TPA_Extensions>
                  <ns:TK_ContactInfo>
                     <ns:Name/>
                     <ns:Surname/>
                     <ns:PassengerIndex/>
                  </ns:TK_ContactInfo>
                  <ns:TK_RQIntentionType>
                     <ns:Application>OTC</ns:Application>
                     <ns:Coverage>infantAddition</ns:Coverage>
                  </ns:TK_RQIntentionType>
               </ns:TPA_Extensions>
               <ns:AirItinerary>
                  <ns:OriginDestinationOptions/>
               </ns:AirItinerary>
               <ns:POS/>
            </ns:OTA_AirBookRQ>
            <createTicketOTARequest>
               <ns:OTA_AirDemandTicketRQ>
                  <ns:DemandTicketDetail>
                     <ns:MessageFunction Function="E"/>
					 <ns:PaymentInfo Amount="" CurrencyCode="" DecimalPlaces="2" PaymentType="" Text="1">
						<ns:CreditCardInfo ExpireDate="" ExtendedPaymentCode="0" Remark="">
						   <ns:CardHolderNameDetails>
							  <ns:GivenName>ORCUN</ns:GivenName>
							  <ns:Surname>BALCILAR</ns:Surname>
						   </ns:CardHolderNameDetails>
						   <ns:Address>
							  <ns:AddressLine>1295 Charleston Rd</ns:AddressLine>
							  <ns:AddressLine>1295 Charleston Rd</ns:AddressLine>
							  <ns:CityName>Mountain View</ns:CityName>
							  <ns:PostalCode>94043</ns:PostalCode>
							  <ns:StateProv>CA</ns:StateProv>
							  <ns:CountryName>US</ns:CountryName>
						   </ns:Address>
						   <ns:Telephone PhoneNumber="5415667645"/>
						   <ns:Email><EMAIL></ns:Email>
						   <ns:CardNumber>
							  <ns:PlainText/>
						   </ns:CardNumber>
						   <ns:SeriesCode>
							  <ns:PlainText/>
						   </ns:SeriesCode>
						</ns:CreditCardInfo>
					 </ns:PaymentInfo>
                     <ns:TPA_Extensions>
                        <ns:TK_RQIntentionType>
                           <ns:Application>OTC</ns:Application>
                           <ns:Coverage>infantAddition</ns:Coverage>
                        </ns:TK_RQIntentionType>
                        <ns:TK_ContactInfo>
                           <ns:Name/>
                           <ns:Surname/>
                           <ns:PassengerIndex/>
                        </ns:TK_ContactInfo>
                     </ns:TPA_Extensions>
                  </ns:DemandTicketDetail>
               </ns:OTA_AirDemandTicketRQ>
            </createTicketOTARequest>
            <isReservation/>
         </ser:infantAdditionOTARequest>
      </ser:infantAddition>
   </soapenv:Body>
</soapenv:Envelope>