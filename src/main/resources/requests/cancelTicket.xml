<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
  xmlns:ser="http://service.thy.com/" xmlns:req="http://www.thy.com/ws/requestHeader"
  xmlns:ns="http://www.opentravel.org/OTA/2003/05">
  <soapenv:Header/>
  <soapenv:Body>
    <ser:cancelTicket>
      <requestHeader>
        <req:clientCode>WEB3</req:clientCode>
        <req:clientUsername>WEB3</req:clientUsername>
        <req:channel>WEB</req:channel>
        <req:airlineCode>TK</req:airlineCode>
        <req:application>IBE</req:application>
        <req:languageCode>TR</req:languageCode>
        <req:clientTransactionId>failclientTransactionId</req:clientTransactionId>
        <req:extraParameters key="CLIENT_IP" value="************"/>
        <req:extraParameters key="SESSION_ID" value="failsessionId"/>
        <req:extraParameters key="PAYMENT_TRACKING_ID" value=""/>
        <req:extraParameters key="PG_APPLICATION_MODE" value="STAGING"/>
      </requestHeader>
      <ser:cancelTicketOTARequest>
        <ns:OTA_CancelRQ CorrelationID="" TransactionIdentifier="">
          <ns:POS>
            <ns:Source PseudoCityCode="IST"/>
          </ns:POS>
          <ns:UniqueID Type="Reservation" ID="" Reason=""/>
          <ns:UniqueID Type="Ticket" ID="" Instance="1"/>
          <ns:Verification>
            <ns:PersonName>
              <ns:Surname></ns:Surname>
            </ns:PersonName>
            <ns:CustLoyalty MembershipID=""/>
          </ns:Verification>
          <ns:TPA_Extensions>
            <ns:TK_RQIntentionType>
              <ns:Application>OTC</ns:Application>
              <ns:Coverage/>
            </ns:TK_RQIntentionType>
          </ns:TPA_Extensions>
        </ns:OTA_CancelRQ>
        <createTicketOTARequest>
          <ns:OTA_AirDemandTicketRQ>
            <ns:DemandTicketDetail>
              <ns:MessageFunction Function="E"/>
              <ns:BookingReferenceID ID="" Type=""/>
              <ns:PaymentInfo Amount="" CurrencyCode="" DecimalPlaces="2" PaymentType="" Text="1">
                <ns:CreditCardInfo ExpireDate="" ExtendedPaymentCode="0" Remark="">
                  <ns:CardHolderNameDetails>
                    <ns:GivenName>ORCUN</ns:GivenName>
                    <ns:Surname>BALCILAR</ns:Surname>
                  </ns:CardHolderNameDetails>
                  <ns:Address>
                    <ns:AddressLine>1295 Charleston Rd</ns:AddressLine>
                    <ns:AddressLine>1295 Charleston Rd</ns:AddressLine>
                    <ns:CityName>Mountain View</ns:CityName>
                    <ns:PostalCode>94043</ns:PostalCode>
                    <ns:StateProv>CA</ns:StateProv>
                    <ns:CountryName>US</ns:CountryName>
                  </ns:Address>
                  <ns:Telephone PhoneNumber="5716650748"/>
                  <ns:Email><EMAIL></ns:Email>
                  <ns:CardNumber>
                    <ns:PlainText/>
                  </ns:CardNumber>
                  <ns:SeriesCode>
                    <ns:PlainText/>
                  </ns:SeriesCode>
                </ns:CreditCardInfo>
              </ns:PaymentInfo>
              <ns:PassengerName>
                <ns:Surname/>
              </ns:PassengerName>
            </ns:DemandTicketDetail>
          </ns:OTA_AirDemandTicketRQ>
          <clientBrowserDetails>
            <sessionId>4191.865647947054</sessionId>
            <acceptHeader>application/json</acceptHeader>
            <userAgentHeader>Computer, Microsoft Corporation, Windows 10, CHROME8, 85.0.4183.121
            </userAgentHeader>
            <windowsSize>250x400</windowsSize>
            <returnUrl>
              https://uat.turkishairlines.com/com.thy.web.online.ibs/ibs/payment/threeDSuccess?cId=a7848895-1bf7-4d57-82c6-94369f37f620
            </returnUrl>
          </clientBrowserDetails>
          <paymentFlowInfo>
            <multiplePayment>false</multiplePayment>
            <basket>false</basket>
          </paymentFlowInfo>
        </createTicketOTARequest>
        <ticketBasedMoneyTaxItems>
          <ticketNumber></ticketNumber>
          <money>
            <amount></amount>
            <currency>
              <code></code>
            </currency>
          </money>
          <money>
            <amount></amount>
            <currency>
              <code></code>
            </currency>
          </money>
          <penalty>
            <amount>
              <amount></amount>
              <currency>
                <code></code>
              </currency>
            </amount>
          </penalty>
          <troyaEntryList></troyaEntryList>
          <paymentByOrderIdList>
            <paymentOption></paymentOption>
            <orderId></orderId>
            <amount>
              <amount></amount>
              <currency>
                <code></code>
              </currency>
            </amount>
            <milesTaxAmount>
              <amount></amount>
            </milesTaxAmount>
          </paymentByOrderIdList>
        </ticketBasedMoneyTaxItems>
      </ser:cancelTicketOTARequest>
    </ser:cancelTicket>
  </soapenv:Body>
</soapenv:Envelope>