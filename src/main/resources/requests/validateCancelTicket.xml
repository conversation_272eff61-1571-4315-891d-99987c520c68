<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.thy.com/" xmlns:req="http://www.thy.com/ws/requestHeader" xmlns:ns="http://www.opentravel.org/OTA/2003/05">
   <soapenv:Header/>
   <soapenv:Body>
      <ser:validateCancelTicket>
         <requestHeader>
            <req:clientCode>WEB3</req:clientCode>
            <req:clientUsername>WEB3</req:clientUsername>
            <req:channel>WEB</req:channel>
            <req:airlineCode>TK</req:airlineCode>
            <req:application>IBE</req:application>
            <req:languageCode>TR</req:languageCode>
            <req:clientTransactionId>failclientTransactionId</req:clientTransactionId>
            <req:extraParameters key="CLIENT_IP" value="************"/>
            <req:extraParameters key="SESSION_ID" value="failsessionId"/>
            <req:extraParameters key="PG_APPLICATION_MODE" value="STAGING"/>
         </requestHeader>
         <ns:OTA_CancelRQ TransactionIdentifier="NonVPOS">
            <ns:POS>
               <ns:Source PseudoCityCode="IST"/>
            </ns:POS>
            <ns:UniqueID Type="Reservation" ID="" Reason=""/>
            <ns:UniqueID Type="Ticket" ID="" Instance="1"/>
            <ns:Verification>
               <ns:PersonName>
                  <ns:Surname>${#TestCase#Surname}</ns:Surname>
               </ns:PersonName>
               <ns:CustLoyalty MembershipID=""/>
            </ns:Verification>
         </ns:OTA_CancelRQ>
      </ser:validateCancelTicket>
      <ser:validateCancelReservation/>
   </soapenv:Body>
</soapenv:Envelope>