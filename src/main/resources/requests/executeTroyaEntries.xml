<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.thy.com/" xmlns:req="http://www.thy.com/ws/requestHeader">
   <soapenv:Header/>
   <soapenv:Body>
      <ser:executeTroyaEntries>
         <requestHeader>
            <req:clientCode>WEB3</req:clientCode>
            <req:clientUsername>WEB3</req:clientUsername>
            <req:channel>WEB</req:channel>
            <req:airlineCode>TK</req:airlineCode>
            <req:application>IBE</req:application>
            <req:clientTransactionId>failclientTransactionId</req:clientTransactionId>
            <req:languageCode>EN</req:languageCode>
            <req:agencyOfficeCode>ITT</req:agencyOfficeCode>
            <req:ton></req:ton>
            <req:userAgencyCode></req:userAgencyCode>
            <req:dutyCode></req:dutyCode>
            <req:agent></req:agent>
            <req:extraParameters key="LANGUAGE" value="TR" />
            <req:extraParameters key="SESSION_ID" value="failsessionId" />
            <req:extraParameters key="CLIENT_IP" value="" />
            <req:extraParameters key="ALCS_SYSTEM" value="WEBT"/>
         </requestHeader>
         <ser:ExecuteTroyaEntriesOTARequest>
            <entryList/>
         </ser:ExecuteTroyaEntriesOTARequest>
      </ser:executeTroyaEntries>
   </soapenv:Body>
</soapenv:Envelope>