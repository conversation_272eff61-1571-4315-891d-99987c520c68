<soapenv:Envelope xmlns:env="http://schemas.xmlsoap.org/soap/envelope/" xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
   <env:Header/>
   <soapenv:Body>
      <ns4:getAvailability xmlns:ns4="http://service.thy.com/" xmlns:ns5="http://www.thy.com/ws/responseHeader" xmlns:ns2="http://www.thy.com/ws/requestHeader" xmlns:ns3="http://www.opentravel.org/OTA/2003/05">
         <requestHeader>
            <ns2:clientCode>WEB3</ns2:clientCode>
            <ns2:clientUsername>WEB3</ns2:clientUsername>
            <ns2:channel>WEB</ns2:channel>
            <ns2:airlineCode>TK</ns2:airlineCode>
            <ns2:application></ns2:application>
            <ns2:clientTransactionId>failclientTransactionId</ns2:clientTransactionId>
            <ns2:languageCode>EN</ns2:languageCode>
            <ns2:agencyOfficeCode>ITT</ns2:agencyOfficeCode>
            <ns2:ton></ns2:ton>
            <ns2:userAgencyCode></ns2:userAgencyCode>
            <ns2:agencyCode></ns2:agencyCode>
            <ns2:currency></ns2:currency>
            <ns2:agencySubCode></ns2:agencySubCode>
            <ns2:dutyCode></ns2:dutyCode>
            <ns2:agent></ns2:agent>
            <ns2:extraParameters key="SESSION_ID" value="failsessionId"/>
            <ns2:extraParameters key="CLIENT_IP" value=""/>
            <ns2:extraParameters key="ENABLE_SOLD_OUT_FLIGHTS" value="false"/>
            <ns2:extraParameters key="ALCS_SYSTEM" value="WEBT"/>
         </requestHeader>
         <ns4:AvailabilityFilterRequest>
            <filterRequests>
               <filterByTime included="true" listed="false" order="1">
                  <windowAfter>P0Y0M0DT1H0M0S</windowAfter>
               </filterByTime>
            </filterRequests>
         </ns4:AvailabilityFilterRequest>
         <ns3:OTA_AirAvailRQ>
            <ns3:ProcessingInfo AvailabilityIndicator="true" BaseFaresOnlyIndicator="true" ReducedDataIndicator="false" TargetSource="BrandedFares"/>
            <ns3:TravelPreferences>
               <ns3:FlightTypePref RoutingType=""/>
            </ns3:TravelPreferences>
            <ns3:TravelerInfoSummary>
               <ns3:AirTravelerAvail>
               </ns3:AirTravelerAvail>
            </ns3:TravelerInfoSummary>
         </ns3:OTA_AirAvailRQ>
         <ns4:AvailabilityExtraParameters displayType="2" fakePrices="false" logAllDFQRequests="false" mcoAllowed="false" runWithCacheData="false"/>
      </ns4:getAvailability>
   </soapenv:Body>
</soapenv:Envelope>