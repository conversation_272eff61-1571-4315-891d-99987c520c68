<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
   <soapenv:Header/>
   <soapenv:Body>
      <ns5:getFares xmlns:ns2="http://www.thy.com/ws/requestHeader" xmlns:ns5="http://service.thy.com/" xmlns:ns3="http://www.opentravel.org/OTA/2003/05">
         <requestHeader>
            <ns2:clientCode>WEB3</ns2:clientCode>
            <ns2:clientUsername>WEB3</ns2:clientUsername>
            <ns2:channel>WEB</ns2:channel>
            <ns2:airlineCode>TK</ns2:airlineCode>
            <ns2:application>IBE</ns2:application>
            <ns2:clientTransactionId>failclientTransactionId</ns2:clientTransactionId>
            <ns2:languageCode>EN</ns2:languageCode>
            <ns2:agencyOfficeCode>ITT</ns2:agencyOfficeCode>
            <ns2:ton></ns2:ton>
            <ns2:userAgencyCode></ns2:userAgencyCode>
            <ns2:dutyCode></ns2:dutyCode>
            <ns2:agent></ns2:agent>
            <ns2:extraParameters key="LANGUAGE" value="TR"/>
            <ns2:extraParameters key="SESSION_ID" value="failsessionId"/>
            <ns2:extraParameters key="CLIENT_IP" value=""/>
            <ns2:extraParameters key="ALCS_SYSTEM" value="WEBT"/>
         </requestHeader>
         <ns3:OTA_AirBookRQ ResStatus="Book">
            <ns3:AirItinerary>
               <ns3:OriginDestinationOptions>
               </ns3:OriginDestinationOptions>
            </ns3:AirItinerary>
            <ns3:PriceInfo>
               <ns3:FareInfos>
               </ns3:FareInfos>
            </ns3:PriceInfo>
            <ns3:TravelerInfo>
            </ns3:TravelerInfo>
         </ns3:OTA_AirBookRQ>
      </ns5:getFares>
   </soapenv:Body>
</soapenv:Envelope>