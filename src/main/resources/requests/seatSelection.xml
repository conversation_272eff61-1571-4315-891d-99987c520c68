<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
  xmlns:ser="http://service.thy.com/" xmlns:req="http://www.thy.com/ws/requestHeader"
  xmlns:ns="http://www.opentravel.org/OTA/2003/05">
  <soapenv:Header/>
  <soapenv:Body>
    <ser:seatSelection>
      <requestHeader>
        <req:clientCode>WEB3</req:clientCode>
        <req:clientUsername>WEB3</req:clientUsername>
        <req:channel>WEB</req:channel>
        <req:airlineCode>TK</req:airlineCode>
        <req:application>IBE</req:application>
        <req:clientTransactionId>failclientTransactionId</req:clientTransactionId>
        <req:languageCode>TR</req:languageCode>
        <req:agencyOfficeCode>ITT</req:agencyOfficeCode>
        <req:ton></req:ton>
        <req:userAgencyCode></req:userAgencyCode>
        <req:dutyCode></req:dutyCode>
        <req:agent></req:agent>
        <req:currency></req:currency>
        <req:extraParameters key="CLIENT_IP" value=""/>
        <req:extraParameters key="SESSION_ID" value="failsessionId"/>
        <req:extraParameters key="ALCS_SYSTEM" value=""/>
      </requestHeader>
      <ns:OTA_AirBookRQ>
        <ns:TravelerInfo>
          <ns:SpecialReqDetails>
            <ns:SeatRequests>
            </ns:SeatRequests>
          </ns:SpecialReqDetails>
        </ns:TravelerInfo>
        <ns:Fulfillment>
          <ns:Name>
            <ns:Surname></ns:Surname>
          </ns:Name>
        </ns:Fulfillment>
        <ns:BookingReferenceID ID=""/>
      </ns:OTA_AirBookRQ>
    </ser:seatSelection>
  </soapenv:Body>
</soapenv:Envelope>