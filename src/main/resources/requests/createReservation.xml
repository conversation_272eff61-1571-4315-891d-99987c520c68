<soapenv:Envelope xmlns:env="http://schemas.xmlsoap.org/soap/envelope/" xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
    <env:Header/>
    <soapenv:Body>
        <ns5:createReservation xmlns:ns5="http://service.thy.com/" xmlns:ns2="http://www.opentravel.org/OTA/2003/05" xmlns:ns4="http://www.thy.com/ws/responseHeader" xmlns:ns3="http://www.thy.com/ws/requestHeader">
            <requestHeader>
                <ns3:clientCode>WEB3</ns3:clientCode>
                <ns3:clientUsername>WEB3</ns3:clientUsername>
                <ns3:channel>WEB</ns3:channel>
                <ns3:airlineCode>TK</ns3:airlineCode>
                <ns3:application>IBE</ns3:application>
                <ns3:clientTransactionId>failclientTransactionId</ns3:clientTransactionId>
                <ns3:languageCode>EN</ns3:languageCode>
                <ns3:agencyOfficeCode>ITT</ns3:agencyOfficeCode>
                <ns3:ton></ns3:ton>
                <ns3:userAgencyCode></ns3:userAgencyCode>
                <ns3:dutyCode></ns3:dutyCode>
                <ns3:agent></ns3:agent>
                <ns3:extraParameters key="LANGUAGE" value="TR"/>
                <ns3:extraParameters key="SESSION_ID" value="failsessionId"/>
                <ns3:extraParameters key="CLIENT_IP" value=""/>
                <ns3:extraParameters key="ALCS_SYSTEM" value="WEBT"/>
            </requestHeader>
            <ns2:OTA_AirBookRQ>
                <ns2:Fulfillment>
                    <ns2:Name>
                        <ns2:Surname></ns2:Surname>
                        <ns2:Document DocID="" DocType="MERCH_OFFERID"/>
                    </ns2:Name>
                    <ns2:Receipt DistribType="manuelTimeLimit"/>
                </ns2:Fulfillment>
                <ns2:BookingReferenceID ID=""/>
                <ns2:TPA_Extensions>
                    <ns2:TK_RQIntentionType>
                        <ns2:Application>OTC</ns2:Application>
                        <ns2:Coverage>reservation</ns2:Coverage>
                    </ns2:TK_RQIntentionType>
                </ns2:TPA_Extensions>
            </ns2:OTA_AirBookRQ>
        </ns5:createReservation>
    </soapenv:Body>
</soapenv:Envelope>