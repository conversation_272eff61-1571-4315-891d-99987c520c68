<soapenv:Envelope xmlns:env="http://schemas.xmlsoap.org/soap/envelope/" xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
   <env:Header/>
   <soapenv:Body>
      <ns5:prePayment xmlns:ns5="http://service.thy.com/" xmlns:ns2="http://www.opentravel.org/OTA/2003/05" xmlns:ns4="http://www.thy.com/ws/responseHeader" xmlns:ns3="http://www.thy.com/ws/requestHeader">
         <requestHeader>
            <ns3:clientCode>WEB3</ns3:clientCode>
            <ns3:clientUsername>WEB3</ns3:clientUsername>
            <ns3:channel>WEB</ns3:channel>
            <ns3:airlineCode>TK</ns3:airlineCode>
            <ns3:application>IBE</ns3:application>
            <ns3:clientTransactionId>failclientTransactionId</ns3:clientTransactionId>
            <ns3:languageCode>EN</ns3:languageCode>
            <ns3:agencyOfficeCode>ITT</ns3:agencyOfficeCode>
            <ns3:ton></ns3:ton>
            <ns3:userAgencyCode></ns3:userAgencyCode>
            <ns3:dutyCode></ns3:dutyCode>
            <ns3:agent></ns3:agent>
            <ns3:extraParameters key="LANGUAGE" value="TR"/>
            <ns3:extraParameters key="SESSION_ID" value="failsessionId"/>
            <ns3:extraParameters key="CLIENT_IP" value=""/>
            <ns3:extraParameters key="ALCS_SYSTEM" value="WEBT"/>
            <ns3:extraParameters key="PG_APPLICATION_MODE" value="STAGING"/>
         </requestHeader>
         <ns5:processPaymentOTARequest>
            <ns2:OTA_AirDemandTicketRQ>
               <ns2:DemandTicketDetail>
                  <ns2:MessageFunction Function="E"/>
                  <ns2:BookingReferenceID ID=""/>
                  <ns2:PassengerName>
                     <ns2:Surname/>
                  </ns2:PassengerName>
                  <ns2:TPA_Extensions>
                     <ns2:TK_RQIntentionType>
                        <ns2:Application>OTC</ns2:Application>
                     </ns2:TK_RQIntentionType>
                  </ns2:TPA_Extensions>
               </ns2:DemandTicketDetail>
            </ns2:OTA_AirDemandTicketRQ>
            <threeDSecureInfo>
               <threeDSecureItems>
                  <threeDSecureItem>
                     <threeDSecureInfo>
                        <entry>
                           <key>okUrl</key>
                           <value>https://uat.turkishairlines.com/com.thy.web.online.ibs/ibs/payment/threeDSuccess?cId=6366a106-34ed-4b76-b5d6-cfb59f0f31a9</value>
                        </entry>
                        <entry>
                           <key>failUrl</key>
                           <value>https://uat.turkishairlines.com/com.thy.web.online.ibs/ibs/payment/threeDFail?cId=6366a106-34ed-4b76-b5d6-cfb59f0f31a9</value>
                        </entry>
                     </threeDSecureInfo>
                  </threeDSecureItem>
               </threeDSecureItems>
            </threeDSecureInfo>
            <clientBrowserDetails>
               <sessionId>92.10396861657921</sessionId>
               <acceptHeader>application/json</acceptHeader>
               <userAgentHeader>Computer, Microsoft Corporation, Windows 10, CHROME, 78.0.3904.87</userAgentHeader>
               <windowsSize>250x400</windowsSize>
               <returnUrl>https://uat.turkishairlines.com/com.thy.web.online.ibs/ibs/payment/threeDSuccess?cId=6366a106-34ed-4b76-b5d6-cfb59f0f31a9</returnUrl>
            </clientBrowserDetails>
            <paymentFlowInfo>
               <multiplePayment>false</multiplePayment>
               <basket>false</basket>
            </paymentFlowInfo>
         </ns5:processPaymentOTARequest>
      </ns5:prePayment>
   </soapenv:Body>
</soapenv:Envelope>