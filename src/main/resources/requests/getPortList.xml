<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
  <soapenv:Header/>
  <soapenv:Body>
    <ns5:getPortList xmlns:ns2="http://www.thy.com/ws/requestHeader"
      xmlns:ns5="http://service.thy.com/">
      <requestHeader>
        <ns2:clientCode>WEB3</ns2:clientCode>
        <ns2:clientUsername>WEB3</ns2:clientUsername>
        <ns2:channel>WEB</ns2:channel>
        <ns2:airlineCode>TK</ns2:airlineCode>
        <ns2:application>IBE</ns2:application>
        <ns2:agencyOfficeCode>ITT</ns2:agencyOfficeCode>
        <ns2:ton></ns2:ton>
        <ns2:userAgencyCode></ns2:userAgencyCode>
        <ns2:dutyCode></ns2:dutyCode>
        <ns2:agent></ns2:agent>
        <ns2:clientTransactionId>failclientTransactionId</ns2:clientTransactionId>
        <ns2:languageCode>EN</ns2:languageCode>
        <ns2:extraParameters key="LANGUAGE" value="TR"/>
        <ns2:extraParameters key="SESSION_ID" value="failsessionId"/>
        <ns2:extraParameters key="CLIENT_IP" value=""/>
      </requestHeader>
      <ns5:getPortsOTARequest>
        <AirlineCode>TK</AirlineCode>
      </ns5:getPortsOTARequest>
    </ns5:getPortList>
  </soapenv:Body>
</soapenv:Envelope>