<S:Envelope
  xmlns:S="http://schemas.xmlsoap.org/soap/envelope/"
  xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/">
  <SOAP-ENV:Header/>
  <S:Body>
    <ns5:addInvoluntaryIndicator xmlns:ns2="http://www.opentravel.org/OTA/2003/05"
      xmlns:ns4="http://www.thy.com/ws/requestHeader" xmlns:ns5="http://service.thy.com/">
      <requestHeader xmlns="">
        <ns4:clientCode>WEB3</ns4:clientCode>
        <ns4:clientUsername>WEB3</ns4:clientUsername>
        <ns4:channel>WEB</ns4:channel>
        <ns4:airlineCode>TK</ns4:airlineCode>
        <ns4:application>IBE</ns4:application>
        <ns4:agencyOfficeCode>ITT</ns4:agencyOfficeCode>
        <ns4:ton></ns4:ton>
        <ns4:userAgencyCode></ns4:userAgencyCode>
        <ns4:dutyCode></ns4:dutyCode>
        <ns4:agent></ns4:agent>
        <ns4:clientTransactionId>failclientTransactionId</ns4:clientTransactionId>
        <ns4:languageCode>EN</ns4:languageCode>
        <ns4:extraParameters key="CLIENT_IP" value=""/>
        <ns4:extraParameters key="SESSION_ID" value="failsessionId"/>
        <ns4:extraParameters key="ALCS_SYSTEM" value="WEBT"/>
      </requestHeader>
      <ns5:InvoluntaryIndicatorOTARequest>
        <ns2:OTA_AirBookModifyRQ>
          <ns2:AirBookModifyRQ>
          </ns2:AirBookModifyRQ>
        </ns2:OTA_AirBookModifyRQ>
      </ns5:InvoluntaryIndicatorOTARequest>
    </ns5:addInvoluntaryIndicator>
  </S:Body>
</S:Envelope>