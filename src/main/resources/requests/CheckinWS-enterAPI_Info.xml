<S:Envelope xmlns:env="http://schemas.xmlsoap.org/soap/envelope/" xmlns:S="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/">
   <SOAP-ENV:Header/>
   <S:Body xmlns:ns3="http://www.thy.com/ws/responseHeader" xmlns:ns2="http://www.thy.com/ws/requestHeader" xmlns:ns4="schema.doccheck.staralliance.com" xmlns:ns5="http://com.thy.dcs.checkin.services.ws/">
      <ns5:enterAPI_Info>
         <requestHeader>
            <ns2:clientCode>TD4P</ns2:clientCode>
            <ns2:clientUsername>TD4PUSR</ns2:clientUsername>
            <ns2:channel>WEB</ns2:channel>
            <ns2:airlineCode>TK</ns2:airlineCode>
            <ns2:application>TD4PAPP</ns2:application>
            <ns2:agent>TD</ns2:agent>
            <ns2:clientTransactionId>failclientTransactionId</ns2:clientTransactionId>
            <ns2:languageCode>TR</ns2:languageCode>
         </requestHeader>
         <TripData PnrCode="">
            <PassengerInfoList>
               <PassengerInfo AdultRPH="" CreditCardControlRequired="false" Gender="" RPH="" TravelingWithInfant="">
                  <PassengerName>
                     <NamePrefix></NamePrefix>
                     <GivenName></GivenName>
                     <Surname></Surname>
                  </PassengerName>
                  <PassengerTypeCode></PassengerTypeCode>
               </PassengerInfo>
            </PassengerInfoList>
            <OriginDestinationList>
               <OriginDestination IsReturnFlight="false">
                  <Segment CabinClassCode="" FlightNumber="" MarketingAirlineCode="" RPH="" SeatPurchasingCompleted="false" isSegmentInterline="false">
                     <DepartureInformation CountryCode="TR" DepartureDate="" DepartureDay="" DepartureTime="" LocationCode="">
                        <LocalTime date="" time=""/>
                     </DepartureInformation>
                     <ArrivalInformation ArrivalDate="" ArrivalTime="" CountryCode="" LocationCode=""/>
                     <AdditionalInformation IsAnadoluJetFlight="true" IsApisFlight="true" IsHomePrintedBagTagEnabled="true" IsMobileBoardingPassSignEnabled="false"/>
                  </Segment>
               </OriginDestination>
            </OriginDestinationList>
            <PassengerFlightInfoList>
               <PassengerFlightInfo CabinCode="" FlightRPH="" IsStandBy="false" PassengerRPH="">
                  <PaxIdentifier DCS_SequenceNumber="" SeatNumber=""/>
                  <API_Info Birthday="" CountryOfIssuance="TUR" CountryOfResidence="TUR" DocType="" Gender="" Name="" Nationality="TUR" PassportNo="" Surname=""/>
                  <BookingInfo BrandCode="" ChargeableSeatType="" HasChargeableExitSeat="" HasChargeableSeat="" IsGroupPnr="false" HesCode="" HesCodeIdLast3Digit="" HesCodeExpiryDate="" HesCodeStatus="" ResBookDesigCode="" SeatEMDSSRCode="" SeatSelectionAllowed="false" Status="" Type="COMMERCIAL">
                     <Ticket CouponNumber="" TicketNumber="" TicketStatus="CONFIRMED" TicketType="ETK"/>
                     <FareBase BaggageAllowance="" FareBase=""/>
                  </BookingInfo>
                  <IdInformation TCKIdNumber=""/>
               </PassengerFlightInfo>
            </PassengerFlightInfoList>
         </TripData>
      </ns5:enterAPI_Info>
   </S:Body>
</S:Envelope>