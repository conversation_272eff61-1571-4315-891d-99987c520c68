<soapenv:Envelope xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"
  xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
  <env:Header/>
  <soapenv:Body>
    <ns5:reissue xmlns:ns5="http://service.thy.com/"
      xmlns:ns3="http://www.opentravel.org/OTA/2003/05"
      xmlns:ns2="http://www.thy.com/ws/requestHeader">
      <requestHeader>
        <ns2:clientCode>WEB3</ns2:clientCode>
        <ns2:clientUsername>WEB3</ns2:clientUsername>
        <ns2:channel>WEB</ns2:channel>
        <ns2:airlineCode>TK</ns2:airlineCode>
        <ns2:application>IBE</ns2:application>
        <ns2:clientTransactionId>failclientTransactionId</ns2:clientTransactionId>
        <ns2:languageCode>EN</ns2:languageCode>
        <ns2:agencyOfficeCode>ITT</ns2:agencyOfficeCode>
        <ns2:ton></ns2:ton>
        <ns2:userAgencyCode></ns2:userAgencyCode>
        <ns2:dutyCode></ns2:dutyCode>
        <ns2:agent></ns2:agent>
        <ns2:extraParameters key="LANGUAGE" value="EN"/>
        <ns2:extraParameters key="SESSION_ID" value="failsessionId"/>
        <ns2:extraParameters key="CLIENT_IP" value=""/>
        <ns2:extraParameters key="ALCS_SYSTEM" value="WEBT"/>
        <ns2:extraParameters key="PG_APPLICATION_MODE" value="STAGING"/>
        <ns2:extraParameters key="PAYMENT_TRACKING_ID" value=""/>
      </requestHeader>
      <ns5:ReissueOTARequest>
        <createTicketOTARequest>
          <ns3:OTA_AirDemandTicketRQ>
            <ns3:DemandTicketDetail>
              <ns3:MessageFunction Function="E"/>
              <ns3:BookingReferenceID ID="" Type=""/>
              <ns3:PaymentInfo Amount="" CurrencyCode="" DecimalPlaces="2" PaymentType="" Text="1">
                <ns3:CreditCardInfo ExpireDate="" ExtendedPaymentCode="0" Remark="">
                  <ns3:CardHolderNameDetails>
                    <ns3:GivenName>ORCUN</ns3:GivenName>
                    <ns3:Surname>BALCILAR</ns3:Surname>
                  </ns3:CardHolderNameDetails>
                  <ns3:Address>
                    <ns3:AddressLine>1295 Charleston Rd</ns3:AddressLine>
                    <ns3:AddressLine>1295 Charleston Rd</ns3:AddressLine>
                    <ns3:CityName>Mountain View</ns3:CityName>
                    <ns3:PostalCode>94043</ns3:PostalCode>
                    <ns3:StateProv>CA</ns3:StateProv>
                    <ns3:CountryName>US</ns3:CountryName>
                  </ns3:Address>
                  <ns3:Telephone PhoneNumber="5716650748"/>
                  <ns3:Email><EMAIL></ns3:Email>
                  <ns3:CardNumber>
                    <ns3:PlainText/>
                  </ns3:CardNumber>
                  <ns3:SeriesCode>
                    <ns3:PlainText/>
                  </ns3:SeriesCode>
                </ns3:CreditCardInfo>
              </ns3:PaymentInfo>
              <ns3:PassengerName>
                <ns3:Surname/>
              </ns3:PassengerName>
              <ns3:TPA_Extensions>
                <ns3:TK_RQIntentionType>
                  <ns3:Application>OTC</ns3:Application>
                  <ns3:Coverage>reissue</ns3:Coverage>
                </ns3:TK_RQIntentionType>
              </ns3:TPA_Extensions>
            </ns3:DemandTicketDetail>
          </ns3:OTA_AirDemandTicketRQ>
          <clientBrowserDetails>
            <sessionId>4191.865647947054</sessionId>
            <acceptHeader>application/json</acceptHeader>
            <userAgentHeader>Computer, Microsoft Corporation, Windows 10, CHROME8, 85.0.4183.121
            </userAgentHeader>
            <windowsSize>250x400</windowsSize>
            <returnUrl>
              https://uat.turkishairlines.com/com.thy.web.online.ibs/ibs/payment/threeDSuccess?cId=a7848895-1bf7-4d57-82c6-94369f37f620
            </returnUrl>
          </clientBrowserDetails>
          <paymentFlowInfo>
            <multiplePayment>false</multiplePayment>
            <basket>false</basket>
          </paymentFlowInfo>
        </createTicketOTARequest>
      </ns5:ReissueOTARequest>
    </ns5:reissue>
  </soapenv:Body>
</soapenv:Envelope>