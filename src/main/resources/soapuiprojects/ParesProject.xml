<?xml version="1.0" encoding="UTF-8"?>
<con:soapui-project id="85e46833-acdf-4fb5-b2e2-2184456c2470" created="3.7.0" activeEnvironment="Default environment" name="Pares" resourceRoot="" updated="3.9.1 2021-07-21T11:56:24Z" encryptionMode="Not encrypted" xmlns:con="http://eviware.com/soapui/config">
  <con:settings/>
  <con:interface xsi:type="con:WsdlInterface" id="de10bc98-0697-4f33-a8f3-e2f77517471d" wsaVersion="NONE" name="TkresSoapXmlServicePortBinding" type="wsdl" bindingName="{http://ws.ea.edf.tk.com/}TkresSoapXmlServicePortBinding" soapVersion="1_1" anonymous="optional" definition="https://wskurumsaltest.thy.com/edf-ea-ws/TkresSoapXmlService?WSDL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <con:settings>
      <con:setting id="5cef9bbe-291e-456f-8bff-b183e0e26c59fileName">TkresSoapXmlServicePortBinding</con:setting>
      <con:setting id="51936515-3e2c-4ded-bb46-c8449e72ab3bfileName">TkresSoapXmlServicePortBinding</con:setting>
    </con:settings>
    <con:definitionCache type="TEXT" rootPart="https://wskurumsaltest.thy.com/edf-ea-ws/TkresSoapXmlService?WSDL">
      <con:part>
        <con:url>https://wskurumsaltest.thy.com/edf-ea-ws/TkresSoapXmlService?WSDL</con:url>
        <con:content><![CDATA[<!--Published by JAX-WS RI (http://jax-ws.java.net). RI's version is JAX-WS RI 2.2.11-b150616.1732 svn-revision#a247ba216861f2c0baac9a3657c5690bce0c744d.-->
<!--Generated by JAX-WS RI (http://jax-ws.java.net). RI's version is JAX-WS RI 2.2.11-b150616.1732 svn-revision#a247ba216861f2c0baac9a3657c5690bce0c744d.-->
<definitions targetNamespace="http://ws.ea.edf.tk.com/" name="TkresSoapXmlService" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:wsp="http://www.w3.org/ns/ws-policy" xmlns:wsp1_2="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://ws.ea.edf.tk.com/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.xmlsoap.org/wsdl/">
  <types>
    <xsd:schema>
      <xsd:import namespace="http://ws.ea.edf.tk.com/" schemaLocation="https://wskurumsaltest.thy.com/edf-ea-ws/TkresSoapXmlService?xsd=1"/>
    </xsd:schema>
    <xsd:schema>
      <xsd:import namespace="http://www.turkishairlines.com/schema/rrt0tk/v0100/tktres" schemaLocation="https://wskurumsaltest.thy.com/edf-ea-ws/TkresSoapXmlService?xsd=2"/>
    </xsd:schema>
    <xsd:schema>
      <xsd:import namespace="http://www.turkishairlines.com/schema/rrt0tk/v0100/segment" schemaLocation="https://wskurumsaltest.thy.com/edf-ea-ws/TkresSoapXmlService?xsd=3"/>
    </xsd:schema>
  </types>
  <message name="getTicketByID">
    <part name="parameters" element="tns:getTicketByID"/>
  </message>
  <message name="getTicketByIDResponse">
    <part name="parameters" element="tns:getTicketByIDResponse"/>
  </message>
  <message name="displayTicket">
    <part name="parameters" element="tns:displayTicket"/>
  </message>
  <message name="displayTicketResponse">
    <part name="parameters" element="tns:displayTicketResponse"/>
  </message>
  <message name="displayTickets">
    <part name="parameters" element="tns:displayTickets"/>
  </message>
  <message name="displayTicketsResponse">
    <part name="parameters" element="tns:displayTicketsResponse"/>
  </message>
  <message name="getTicketsByOrderId">
    <part name="parameters" element="tns:getTicketsByOrderId"/>
  </message>
  <message name="getTicketsByOrderIdResponse">
    <part name="parameters" element="tns:getTicketsByOrderIdResponse"/>
  </message>
  <message name="getTicketsByTicketNumberAndIssueDate">
    <part name="parameters" element="tns:getTicketsByTicketNumberAndIssueDate"/>
  </message>
  <message name="getTicketsByTicketNumberAndIssueDateResponse">
    <part name="parameters" element="tns:getTicketsByTicketNumberAndIssueDateResponse"/>
  </message>
  <message name="getTicketsByPNR">
    <part name="parameters" element="tns:getTicketsByPNR"/>
  </message>
  <message name="getTicketsByPNRResponse">
    <part name="parameters" element="tns:getTicketsByPNRResponse"/>
  </message>
  <message name="getTicketsByNameSurname">
    <part name="parameters" element="tns:getTicketsByNameSurname"/>
  </message>
  <message name="getTicketsByNameSurnameResponse">
    <part name="parameters" element="tns:getTicketsByNameSurnameResponse"/>
  </message>
  <message name="getTicketsByEmdNumber">
    <part name="parameters" element="tns:getTicketsByEmdNumber"/>
  </message>
  <message name="getTicketsByEmdNumberResponse">
    <part name="parameters" element="tns:getTicketsByEmdNumberResponse"/>
  </message>
  <message name="getTicketsByCreditCardNumber">
    <part name="parameters" element="tns:getTicketsByCreditCardNumber"/>
  </message>
  <message name="getTicketsByCreditCardNumberResponse">
    <part name="parameters" element="tns:getTicketsByCreditCardNumberResponse"/>
  </message>
  <message name="getTicketsByFFPInfo">
    <part name="parameters" element="tns:getTicketsByFFPInfo"/>
  </message>
  <message name="getTicketsByFFPInfoResponse">
    <part name="parameters" element="tns:getTicketsByFFPInfoResponse"/>
  </message>
  <message name="getTicketsByFFPInfoAndFlightDate">
    <part name="parameters" element="tns:getTicketsByFFPInfoAndFlightDate"/>
  </message>
  <message name="getTicketsByFFPInfoAndFlightDateResponse">
    <part name="parameters" element="tns:getTicketsByFFPInfoAndFlightDateResponse"/>
  </message>
  <message name="getTicketsByFlightDetails">
    <part name="parameters" element="tns:getTicketsByFlightDetails"/>
  </message>
  <message name="getTicketsByFlightDetailsResponse">
    <part name="parameters" element="tns:getTicketsByFlightDetailsResponse"/>
  </message>
  <message name="getTicketsByFlightInfo">
    <part name="parameters" element="tns:getTicketsByFlightInfo"/>
  </message>
  <message name="getTicketsByFlightInfoResponse">
    <part name="parameters" element="tns:getTicketsByFlightInfoResponse"/>
  </message>
  <message name="getTroyaDisplayString">
    <part name="parameters" element="tns:getTroyaDisplayString"/>
  </message>
  <message name="getTroyaDisplayStringResponse">
    <part name="parameters" element="tns:getTroyaDisplayStringResponse"/>
  </message>
  <message name="getHistoricallyConnectedTickets">
    <part name="parameters" element="tns:getHistoricallyConnectedTickets"/>
  </message>
  <message name="getHistoricallyConnectedTicketsResponse">
    <part name="parameters" element="tns:getHistoricallyConnectedTicketsResponse"/>
  </message>
  <message name="getOldTickets">
    <part name="parameters" element="tns:getOldTickets"/>
  </message>
  <message name="getOldTicketsResponse">
    <part name="parameters" element="tns:getOldTicketsResponse"/>
  </message>
  <message name="getTicketsByValidatorInTimeRange">
    <part name="parameters" element="tns:getTicketsByValidatorInTimeRange"/>
  </message>
  <message name="getTicketsByValidatorInTimeRangeResponse">
    <part name="parameters" element="tns:getTicketsByValidatorInTimeRangeResponse"/>
  </message>
  <message name="hotTransform">
    <part name="parameters" element="tns:hotTransform"/>
  </message>
  <message name="hotTransformResponse">
    <part name="parameters" element="tns:hotTransformResponse"/>
  </message>
  <message name="postConstruct">
    <part name="parameters" element="tns:postConstruct"/>
  </message>
  <message name="postConstructResponse">
    <part name="parameters" element="tns:postConstructResponse"/>
  </message>
  <portType name="TkresSoapXmlService">
    <operation name="getTicketByID">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketByIDRequest" message="tns:getTicketByID"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketByIDResponse" message="tns:getTicketByIDResponse"/>
    </operation>
    <operation name="displayTicket">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/displayTicketRequest" message="tns:displayTicket"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/displayTicketResponse" message="tns:displayTicketResponse"/>
    </operation>
    <operation name="displayTickets">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/displayTicketsRequest" message="tns:displayTickets"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/displayTicketsResponse" message="tns:displayTicketsResponse"/>
    </operation>
    <operation name="getTicketsByOrderId">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByOrderIdRequest" message="tns:getTicketsByOrderId"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByOrderIdResponse" message="tns:getTicketsByOrderIdResponse"/>
    </operation>
    <operation name="getTicketsByTicketNumberAndIssueDate">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByTicketNumberAndIssueDateRequest" message="tns:getTicketsByTicketNumberAndIssueDate"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByTicketNumberAndIssueDateResponse" message="tns:getTicketsByTicketNumberAndIssueDateResponse"/>
    </operation>
    <operation name="getTicketsByPNR">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByPNRRequest" message="tns:getTicketsByPNR"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByPNRResponse" message="tns:getTicketsByPNRResponse"/>
    </operation>
    <operation name="getTicketsByNameSurname">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByNameSurnameRequest" message="tns:getTicketsByNameSurname"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByNameSurnameResponse" message="tns:getTicketsByNameSurnameResponse"/>
    </operation>
    <operation name="getTicketsByEmdNumber">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByEmdNumberRequest" message="tns:getTicketsByEmdNumber"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByEmdNumberResponse" message="tns:getTicketsByEmdNumberResponse"/>
    </operation>
    <operation name="getTicketsByCreditCardNumber">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByCreditCardNumberRequest" message="tns:getTicketsByCreditCardNumber"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByCreditCardNumberResponse" message="tns:getTicketsByCreditCardNumberResponse"/>
    </operation>
    <operation name="getTicketsByFFPInfo">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByFFPInfoRequest" message="tns:getTicketsByFFPInfo"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByFFPInfoResponse" message="tns:getTicketsByFFPInfoResponse"/>
    </operation>
    <operation name="getTicketsByFFPInfoAndFlightDate">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByFFPInfoAndFlightDateRequest" message="tns:getTicketsByFFPInfoAndFlightDate"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByFFPInfoAndFlightDateResponse" message="tns:getTicketsByFFPInfoAndFlightDateResponse"/>
    </operation>
    <operation name="getTicketsByFlightDetails">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByFlightDetailsRequest" message="tns:getTicketsByFlightDetails"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByFlightDetailsResponse" message="tns:getTicketsByFlightDetailsResponse"/>
    </operation>
    <operation name="getTicketsByFlightInfo">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByFlightInfoRequest" message="tns:getTicketsByFlightInfo"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByFlightInfoResponse" message="tns:getTicketsByFlightInfoResponse"/>
    </operation>
    <operation name="getTroyaDisplayString">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTroyaDisplayStringRequest" message="tns:getTroyaDisplayString"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTroyaDisplayStringResponse" message="tns:getTroyaDisplayStringResponse"/>
    </operation>
    <operation name="getHistoricallyConnectedTickets">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getHistoricallyConnectedTicketsRequest" message="tns:getHistoricallyConnectedTickets"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getHistoricallyConnectedTicketsResponse" message="tns:getHistoricallyConnectedTicketsResponse"/>
    </operation>
    <operation name="getOldTickets">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getOldTicketsRequest" message="tns:getOldTickets"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getOldTicketsResponse" message="tns:getOldTicketsResponse"/>
    </operation>
    <operation name="getTicketsByValidatorInTimeRange">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByValidatorInTimeRangeRequest" message="tns:getTicketsByValidatorInTimeRange"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/getTicketsByValidatorInTimeRangeResponse" message="tns:getTicketsByValidatorInTimeRangeResponse"/>
    </operation>
    <operation name="hotTransform">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/hotTransformRequest" message="tns:hotTransform"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/hotTransformResponse" message="tns:hotTransformResponse"/>
    </operation>
    <operation name="postConstruct">
      <input wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/postConstructRequest" message="tns:postConstruct"/>
      <output wsam:Action="http://ws.ea.edf.tk.com/TkresSoapXmlService/postConstructResponse" message="tns:postConstructResponse"/>
    </operation>
  </portType>
  <binding name="TkresSoapXmlServicePortBinding" type="tns:TkresSoapXmlService">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="document"/>
    <operation name="getTicketByID">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="displayTicket">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="displayTickets">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="getTicketsByOrderId">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="getTicketsByTicketNumberAndIssueDate">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="getTicketsByPNR">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="getTicketsByNameSurname">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="getTicketsByEmdNumber">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="getTicketsByCreditCardNumber">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="getTicketsByFFPInfo">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="getTicketsByFFPInfoAndFlightDate">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="getTicketsByFlightDetails">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="getTicketsByFlightInfo">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="getTroyaDisplayString">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="getHistoricallyConnectedTickets">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="getOldTickets">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="getTicketsByValidatorInTimeRange">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="hotTransform">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="postConstruct">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
  </binding>
  <service name="TkresSoapXmlService">
    <port name="TkresSoapXmlServicePort" binding="tns:TkresSoapXmlServicePortBinding">
      <soap:address location="https://wskurumsaltest.thy.com/edf-ea-ws/TkresSoapXmlService"/>
    </port>
  </service>
</definitions>]]></con:content>
        <con:type>http://schemas.xmlsoap.org/wsdl/</con:type>
      </con:part>
      <con:part>
        <con:url>https://wskurumsaltest.thy.com/edf-ea-ws/TkresSoapXmlService?xsd=1</con:url>
        <con:content><![CDATA[<!--Published by JAX-WS RI (http://jax-ws.java.net). RI's version is JAX-WS RI 2.2.11-b150616.1732 svn-revision#a247ba216861f2c0baac9a3657c5690bce0c744d.-->
<xsd:schema targetNamespace="http://ws.ea.edf.tk.com/" xmlns:ns0="http://ws.ea.edf.tk.com/" xmlns:ns1="http://www.turkishairlines.com/schema/rrt0tk/v0100/tktres" xmlns:ns2="http://www.turkishairlines.com/schema/rrt0tk/v0100/segment" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xsd:import schemaLocation="https://wskurumsaltest.thy.com/edf-ea-ws/TkresSoapXmlService?xsd=2" namespace="http://www.turkishairlines.com/schema/rrt0tk/v0100/tktres"/>
  <xsd:import schemaLocation="https://wskurumsaltest.thy.com/edf-ea-ws/TkresSoapXmlService?xsd=3" namespace="http://www.turkishairlines.com/schema/rrt0tk/v0100/segment"/>
  <xsd:complexType name="getHistoricallyConnectedTickets">
    <xsd:sequence>
      <xsd:element name="ticketNumber" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="environment" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getHistoricallyConnectedTicketsResponse">
    <xsd:sequence>
      <xsd:element name="return" type="ns0:historicallyConnectedTicketsResponse" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getOldTickets">
    <xsd:sequence>
      <xsd:element name="ticketNumber" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getOldTicketsResponse">
    <xsd:sequence>
      <xsd:element ref="ns2:Ticket" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketByID">
    <xsd:sequence>
      <xsd:element name="id" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketByIDResponse">
    <xsd:sequence>
      <xsd:element name="return" type="ns0:responseBean" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="displayTicket">
    <xsd:sequence>
      <xsd:element name="ticketNumber" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="displayTicketResponse">
    <xsd:sequence>
      <xsd:element name="return" type="ns0:responseBean" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByCreditCardNumber">
    <xsd:sequence>
      <xsd:element name="creditCardNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="flightDate" type="xsd:string" minOccurs="0"/>
      <xsd:element name="passengerSurname" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByCreditCardNumberResponse">
    <xsd:sequence>
      <xsd:element name="return" type="ns0:multipleResponseBean" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByEmdNumber">
    <xsd:sequence>
      <xsd:element name="emdNumber" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByEmdNumberResponse">
    <xsd:sequence>
      <xsd:element name="return" type="ns0:multipleResponseBean" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByFFPInfo">
    <xsd:sequence>
      <xsd:element name="companyId" type="xsd:string" minOccurs="0"/>
      <xsd:element name="frequentTravellerNumber" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByFFPInfoAndFlightDate">
    <xsd:sequence>
      <xsd:element name="companyId" type="xsd:string" minOccurs="0"/>
      <xsd:element name="frequentTravellerNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="flightDate" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByFFPInfoAndFlightDateResponse">
    <xsd:sequence>
      <xsd:element name="return" type="ns0:multipleResponseBean" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByFFPInfoResponse">
    <xsd:sequence>
      <xsd:element name="return" type="ns0:multipleResponseBean" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByFlightDetails">
    <xsd:sequence>
      <xsd:element name="origin" type="xsd:string" minOccurs="0"/>
      <xsd:element name="destination" type="xsd:string" minOccurs="0"/>
      <xsd:element name="flightDate" type="xsd:string" minOccurs="0"/>
      <xsd:element name="passengerSurname" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByFlightDetailsResponse">
    <xsd:sequence>
      <xsd:element name="return" type="ns0:multipleResponseBean" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByFlightInfo">
    <xsd:sequence>
      <xsd:element name="origin" type="xsd:string" minOccurs="0"/>
      <xsd:element name="destination" type="xsd:string" minOccurs="0"/>
      <xsd:element name="flightDate" type="xsd:string" minOccurs="0"/>
      <xsd:element name="passengerSurname" type="xsd:string" minOccurs="0"/>
      <xsd:element name="carrierCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="flightNumber" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByFlightInfoResponse">
    <xsd:sequence>
      <xsd:element name="return" type="ns0:multipleResponseBean" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByNameSurname">
    <xsd:sequence>
      <xsd:element name="name" type="xsd:string" minOccurs="0"/>
      <xsd:element name="surname" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByNameSurnameResponse">
    <xsd:sequence>
      <xsd:element name="return" type="ns0:multipleResponseBean" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByOrderId">
    <xsd:sequence>
      <xsd:element name="orderId" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByOrderIdResponse">
    <xsd:sequence>
      <xsd:element name="return" type="ns0:multipleResponseBean" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByPNR">
    <xsd:sequence>
      <xsd:element name="pnr" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByPNRResponse">
    <xsd:sequence>
      <xsd:element name="return" type="ns0:multipleResponseBean" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="displayTickets">
    <xsd:sequence>
      <xsd:element name="ticketNumber" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByTicketNumberAndIssueDate">
    <xsd:sequence>
      <xsd:element name="ticketNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="issueDate" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByTicketNumberAndIssueDateResponse">
    <xsd:sequence>
      <xsd:element name="return" type="ns0:responseBean" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="displayTicketsResponse">
    <xsd:sequence>
      <xsd:element name="return" type="ns0:multipleResponseBean" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByValidatorInTimeRange">
    <xsd:sequence>
      <xsd:element name="validator" type="xsd:string" minOccurs="0"/>
      <xsd:element name="startDateTime" type="xsd:string" minOccurs="0"/>
      <xsd:element name="endDateTime" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTicketsByValidatorInTimeRangeResponse">
    <xsd:sequence>
      <xsd:element name="return" type="ns0:ETicketResponseBean" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTroyaDisplayString">
    <xsd:sequence>
      <xsd:element name="ticketNumber" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getTroyaDisplayStringResponse">
    <xsd:sequence>
      <xsd:element name="return" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="hotTransform">
    <xsd:sequence>
      <xsd:element name="ids" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="lineindex" type="xsd:long"/>
      <xsd:element name="itemindex" type="xsd:long"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="hotTransformResponse">
    <xsd:sequence>
      <xsd:element name="return" type="ns0:responseBean" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="postConstruct"/>
  <xsd:complexType name="postConstructResponse"/>
  <xsd:complexType name="historicallyConnectedTicketsResponse">
    <xsd:complexContent>
      <xsd:extension base="ns0:rBean">
        <xsd:sequence>
          <xsd:element name="tickets" type="ns0:RequestedTicket" minOccurs="0" maxOccurs="unbounded" nillable="true"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="rBean">
    <xsd:sequence>
      <xsd:element name="reason" type="xsd:string" minOccurs="0"/>
      <xsd:element name="requestDateTime" type="xsd:dateTime" minOccurs="0"/>
      <xsd:element name="responseDateTime" type="xsd:dateTime" minOccurs="0"/>
      <xsd:element name="resultCode" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="RequestedTicket">
    <xsd:sequence>
      <xsd:element name="oldTicket" type="ns0:OldTicket" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
    <xsd:attribute name="TicketNumber" type="xsd:string"/>
  </xsd:complexType>
  <xsd:complexType name="OldTicket">
    <xsd:sequence>
      <xsd:element name="Coupon" type="ns0:Coupon" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="ConjunctionTicket" type="ns0:ConjunctionTicket" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
    <xsd:attribute name="TicketNumber" type="xsd:string"/>
    <xsd:attribute name="IssueDate" type="xsd:string"/>
    <xsd:attribute name="TicketOrder" type="xsd:int"/>
  </xsd:complexType>
  <xsd:complexType name="Coupon">
    <xsd:sequence>
      <xsd:element name="OriginCity" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DestinationCity" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CarrierCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="FlightNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DepartureDate" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DepartureTime" type="xsd:string" minOccurs="0"/>
      <xsd:element name="BookingDesignatorCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CabinDesignatorCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="FareBasis" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CouponStatus" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="CouponOrder" type="xsd:string"/>
  </xsd:complexType>
  <xsd:complexType name="ConjunctionTicket">
    <xsd:sequence>
      <xsd:element name="Coupon" type="ns0:Coupon" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
    <xsd:attribute name="TicketNumber" type="xsd:string"/>
    <xsd:attribute name="IssueDate" type="xsd:string"/>
  </xsd:complexType>
  <xsd:complexType name="responseBean">
    <xsd:complexContent>
      <xsd:extension base="ns0:rBean">
        <xsd:sequence>
          <xsd:element name="trunk" type="ns1:Trunk" minOccurs="0"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="multipleResponseBean">
    <xsd:complexContent>
      <xsd:extension base="ns0:rBean">
        <xsd:sequence>
          <xsd:element name="trunk" type="ns1:Trunk" minOccurs="0"/>
          <xsd:element name="trunks" type="ns1:Trunk" minOccurs="0" maxOccurs="unbounded" nillable="true"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="ETicketResponseBean">
    <xsd:sequence>
      <xsd:element name="header" type="ns0:Header" minOccurs="0"/>
      <xsd:element name="body" type="ns0:Body" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="Header">
    <xsd:sequence>
      <xsd:element name="resultCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="reason" type="xsd:string" minOccurs="0"/>
      <xsd:element name="requestDateTime" type="xsd:dateTime" minOccurs="0"/>
      <xsd:element name="responseDateTime" type="xsd:dateTime" minOccurs="0"/>
      <xsd:element name="runtimeMilliseconds" type="xsd:long" minOccurs="0"/>
      <xsd:element name="documentCount" type="xsd:int"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="Body">
    <xsd:sequence>
      <xsd:element name="tickets" minOccurs="0">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="ticket" type="ns0:Ticket" minOccurs="0" maxOccurs="unbounded"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="Ticket">
    <xsd:sequence>
      <xsd:element name="basics" type="ns0:Basics" minOccurs="0"/>
      <xsd:element name="details" type="ns0:Details" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="Basics">
    <xsd:sequence>
      <xsd:element name="ticketNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="issueDate" type="xsd:string" minOccurs="0"/>
      <xsd:element name="pnr" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="Details">
    <xsd:sequence>
      <xsd:element name="document" type="ns1:Trunk" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="getHistoricallyConnectedTickets" type="ns0:getHistoricallyConnectedTickets"/>
  <xsd:element name="getHistoricallyConnectedTicketsResponse" type="ns0:getHistoricallyConnectedTicketsResponse"/>
  <xsd:element name="getOldTickets" type="ns0:getOldTickets"/>
  <xsd:element name="getOldTicketsResponse" type="ns0:getOldTicketsResponse"/>
  <xsd:element name="getTicketByID" type="ns0:getTicketByID"/>
  <xsd:element name="getTicketByIDResponse" type="ns0:getTicketByIDResponse"/>
  <xsd:element name="displayTicket" type="ns0:displayTicket"/>
  <xsd:element name="displayTicketResponse" type="ns0:displayTicketResponse"/>
  <xsd:element name="getTicketsByCreditCardNumber" type="ns0:getTicketsByCreditCardNumber"/>
  <xsd:element name="getTicketsByCreditCardNumberResponse" type="ns0:getTicketsByCreditCardNumberResponse"/>
  <xsd:element name="getTicketsByEmdNumber" type="ns0:getTicketsByEmdNumber"/>
  <xsd:element name="getTicketsByEmdNumberResponse" type="ns0:getTicketsByEmdNumberResponse"/>
  <xsd:element name="getTicketsByFFPInfo" type="ns0:getTicketsByFFPInfo"/>
  <xsd:element name="getTicketsByFFPInfoAndFlightDate" type="ns0:getTicketsByFFPInfoAndFlightDate"/>
  <xsd:element name="getTicketsByFFPInfoAndFlightDateResponse" type="ns0:getTicketsByFFPInfoAndFlightDateResponse"/>
  <xsd:element name="getTicketsByFFPInfoResponse" type="ns0:getTicketsByFFPInfoResponse"/>
  <xsd:element name="getTicketsByFlightDetails" type="ns0:getTicketsByFlightDetails"/>
  <xsd:element name="getTicketsByFlightDetailsResponse" type="ns0:getTicketsByFlightDetailsResponse"/>
  <xsd:element name="getTicketsByFlightInfo" type="ns0:getTicketsByFlightInfo"/>
  <xsd:element name="getTicketsByFlightInfoResponse" type="ns0:getTicketsByFlightInfoResponse"/>
  <xsd:element name="getTicketsByNameSurname" type="ns0:getTicketsByNameSurname"/>
  <xsd:element name="getTicketsByNameSurnameResponse" type="ns0:getTicketsByNameSurnameResponse"/>
  <xsd:element name="getTicketsByOrderId" type="ns0:getTicketsByOrderId"/>
  <xsd:element name="getTicketsByOrderIdResponse" type="ns0:getTicketsByOrderIdResponse"/>
  <xsd:element name="getTicketsByPNR" type="ns0:getTicketsByPNR"/>
  <xsd:element name="getTicketsByPNRResponse" type="ns0:getTicketsByPNRResponse"/>
  <xsd:element name="displayTickets" type="ns0:displayTickets"/>
  <xsd:element name="getTicketsByTicketNumberAndIssueDate" type="ns0:getTicketsByTicketNumberAndIssueDate"/>
  <xsd:element name="getTicketsByTicketNumberAndIssueDateResponse" type="ns0:getTicketsByTicketNumberAndIssueDateResponse"/>
  <xsd:element name="displayTicketsResponse" type="ns0:displayTicketsResponse"/>
  <xsd:element name="getTicketsByValidatorInTimeRange" type="ns0:getTicketsByValidatorInTimeRange"/>
  <xsd:element name="getTicketsByValidatorInTimeRangeResponse" type="ns0:getTicketsByValidatorInTimeRangeResponse"/>
  <xsd:element name="getTroyaDisplayString" type="ns0:getTroyaDisplayString"/>
  <xsd:element name="getTroyaDisplayStringResponse" type="ns0:getTroyaDisplayStringResponse"/>
  <xsd:element name="hotTransform" type="ns0:hotTransform"/>
  <xsd:element name="hotTransformResponse" type="ns0:hotTransformResponse"/>
  <xsd:element name="postConstruct" type="ns0:postConstruct"/>
  <xsd:element name="postConstructResponse" type="ns0:postConstructResponse"/>
  <xsd:element name="eTicketResponseBean" type="ns0:ETicketResponseBean"/>
</xsd:schema>]]></con:content>
        <con:type>http://www.w3.org/2001/XMLSchema</con:type>
      </con:part>
      <con:part>
        <con:url>https://wskurumsaltest.thy.com/edf-ea-ws/TkresSoapXmlService?xsd=2</con:url>
        <con:content><![CDATA[<!--Published by JAX-WS RI (http://jax-ws.java.net). RI's version is JAX-WS RI 2.2.11-b150616.1732 svn-revision#a247ba216861f2c0baac9a3657c5690bce0c744d.-->
<xsd:schema targetNamespace="http://www.turkishairlines.com/schema/rrt0tk/v0100/tktres" elementFormDefault="qualified" xmlns:ns0="http://www.turkishairlines.com/schema/rrt0tk/v0100/tktres" xmlns:ns1="http://www.turkishairlines.com/schema/rrt0tk/v0100/segment" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xsd:import schemaLocation="https://wskurumsaltest.thy.com/edf-ea-ws/TkresSoapXmlService?xsd=3" namespace="http://www.turkishairlines.com/schema/rrt0tk/v0100/segment"/>
  <xsd:complexType name="Trunk">
    <xsd:sequence>
      <xsd:element name="Header" type="ns0:Header" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="K20" type="ns0:K20" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="K30" type="ns0:K30" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="K35" type="ns0:K35" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="K40" type="ns0:K40" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="K50" type="ns0:K50" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="K80" type="ns0:K80" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="KC0" type="ns0:KC0" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="Header">
    <xsd:sequence>
      <xsd:element name="TicketNumber" type="xsd:string"/>
      <xsd:element name="IssueDate" type="xsd:string" minOccurs="0"/>
      <xsd:element name="ProcessDateTime" type="xsd:date"/>
      <xsd:element name="DocumentDateTime" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DocumentType" type="xsd:string"/>
      <xsd:element name="JobIndicator" type="xsd:string" minOccurs="0"/>
      <xsd:element name="LiveTicket" type="xsd:int" minOccurs="0"/>
      <xsd:element name="SystemId" type="xsd:string" minOccurs="0"/>
      <xsd:element name="ExtraFields" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="FNAME" type="xsd:string"/>
            <xsd:element name="FVALUE" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="K20">
    <xsd:sequence>
      <xsd:element name="CountOfTimesInvoicePrinted" type="xsd:int" minOccurs="0"/>
      <xsd:element name="CountOfTimesItineraryRecieptPrinted" type="xsd:int" minOccurs="0"/>
      <xsd:element name="IndicatorByte1" type="xsd:int" minOccurs="0"/>
      <xsd:element name="IndicatorByte2" type="xsd:int" minOccurs="0"/>
      <xsd:element name="NewTicketNumberInCaseOfExchange" type="xsd:string" minOccurs="0"/>
      <xsd:element name="NewTicketTypeInCaseOfExchange" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PackedElectronicTicketNumber" type="xsd:long" minOccurs="0"/>
      <xsd:element name="PurgeDate" type="xsd:string" minOccurs="0"/>
      <xsd:element name="Spare" type="xsd:string" minOccurs="0"/>
      <xsd:element name="UpdateCounterIncrementedEachTime" type="xsd:int" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="K30">
    <xsd:sequence>
      <xsd:element name="FOP_K30" type="ns1:FOP_K30" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="K35">
    <xsd:sequence>
      <xsd:element name="INF" type="ns1:INF" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="K40">
    <xsd:sequence>
      <xsd:element name="LrecIdentifier" type="xsd:string" minOccurs="0"/>
      <xsd:element name="MessageFunctionCoded1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="MessageFunctionCoded2" type="xsd:string" minOccurs="0"/>
      <xsd:element name="TransactionDate" type="xsd:string" minOccurs="0"/>
      <xsd:element name="Indicator1" type="xsd:int" minOccurs="0"/>
      <xsd:element name="Indicator2" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="K50">
    <xsd:sequence>
      <xsd:element name="TKD" type="ns1:TKD" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="K80">
    <xsd:sequence>
      <xsd:element name="TKTRES" type="ns0:TKTRES" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="TKTRES">
    <xsd:sequence>
      <xsd:element name="MSG" type="ns1:MSG" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="ORG" type="ns1:ORG" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="TA2" type="ns1:TA2" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="TAI" type="ns1:TAI" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="RCI" type="ns1:RCI" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="MON" type="ns1:MON" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="PTK" type="ns1:PTK" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="ODI" type="ns1:ODI" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="ATI" type="ns1:ATI" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="FOP_K80" type="ns1:FOP_K80" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="EQN" type="ns1:EQN" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="TXD" type="ns1:TXD" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="TAX" type="ns1:TAX" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="TX2" type="ns1:TX2" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="CPN" type="ns1:CPN" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="CP2" type="ns1:CP2" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="DID" type="ns1:DID" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="IFT" type="ns1:IFT" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="FAR" type="ns1:FAR" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="FQU" type="ns1:FQU" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="TKT" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:complexContent>
            <xsd:extension base="ns1:TKT">
              <xsd:sequence>
                <xsd:element name="ORG" type="ns1:ORG" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="CPN" type="ns1:CPN" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="CP2" type="ns1:CP2" minOccurs="0" maxOccurs="unbounded"/>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="TK2" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:complexContent>
            <xsd:extension base="ns1:TK2">
              <xsd:sequence>
                <xsd:element name="ORG" type="ns1:ORG" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="CPN" type="ns1:CPN" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="CP2" type="ns1:CP2" minOccurs="0" maxOccurs="unbounded"/>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="TIF" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:complexContent>
            <xsd:extension base="ns1:TIF">
              <xsd:sequence>
                <xsd:element name="TA2" type="ns1:TA2" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="TAI" type="ns1:TAI" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="RCI" type="ns1:RCI" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="MON" type="ns1:MON" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="FOP_K80" type="ns1:FOP_K80" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="PTK" type="ns1:PTK" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="ODI" type="ns1:ODI" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="FTI" type="ns1:FTI" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="ATI" type="ns1:ATI" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="ORG" type="ns1:ORG" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="EQN" type="ns1:EQN" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="TXD" type="ns1:TXD" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="TAX" type="ns1:TAX" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="TX2" type="ns1:TX2" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="DID" type="ns1:DID" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="IFT" type="ns1:IFT" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="CRI" type="ns1:CRI" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="FAR" type="ns1:FAR" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="FQU" type="ns1:FQU" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="PTS" type="ns1:PTS" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="TKT" minOccurs="0" maxOccurs="unbounded">
                  <xsd:complexType>
                    <xsd:complexContent>
                      <xsd:extension base="ns1:TKT">
                        <xsd:sequence>
                          <xsd:element name="ORG" type="ns1:ORG" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="DAT" type="ns1:DAT" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="IFT" type="ns1:IFT" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="PTK" type="ns1:PTK" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="CRI" type="ns1:CRI" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="CPN" minOccurs="0" maxOccurs="unbounded">
                            <xsd:complexType>
                              <xsd:complexContent>
                                <xsd:extension base="ns1:CPN">
                                  <xsd:sequence>
                                    <xsd:element name="TVL" type="ns1:TVL" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="RCI" type="ns1:RCI" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="RPI" type="ns1:RPI" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="PTS" type="ns1:PTS" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="EBD" type="ns1:EBD" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="FTI" type="ns1:FTI" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="DAT" type="ns1:DAT" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="ORG" type="ns1:ORG" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="IFT" type="ns1:IFT" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="PTK" type="ns1:PTK" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="FAR" type="ns1:FAR" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="CVR" type="ns1:CVR" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="APD" type="ns1:APD" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="SSR" type="ns1:SSR" minOccurs="0" maxOccurs="unbounded"/>
                                  </xsd:sequence>
                                </xsd:extension>
                              </xsd:complexContent>
                            </xsd:complexType>
                          </xsd:element>
                          <xsd:element name="CP2" minOccurs="0" maxOccurs="unbounded">
                            <xsd:complexType>
                              <xsd:complexContent>
                                <xsd:extension base="ns1:CP2">
                                  <xsd:sequence>
                                    <xsd:element name="TVL" type="ns1:TVL" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="RCI" type="ns1:RCI" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="RPI" type="ns1:RPI" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="PTS" type="ns1:PTS" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="EBD" type="ns1:EBD" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="FTI" type="ns1:FTI" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="DAT" type="ns1:DAT" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="ORG" type="ns1:ORG" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="IFT" type="ns1:IFT" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="PTK" type="ns1:PTK" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="FAR" type="ns1:FAR" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="CVR" type="ns1:CVR" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="APD" type="ns1:APD" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="SSR" type="ns1:SSR" minOccurs="0" maxOccurs="unbounded"/>
                                  </xsd:sequence>
                                </xsd:extension>
                              </xsd:complexContent>
                            </xsd:complexType>
                          </xsd:element>
                        </xsd:sequence>
                      </xsd:extension>
                    </xsd:complexContent>
                  </xsd:complexType>
                </xsd:element>
                <xsd:element name="TK2" minOccurs="0" maxOccurs="unbounded">
                  <xsd:complexType>
                    <xsd:complexContent>
                      <xsd:extension base="ns1:TK2">
                        <xsd:sequence>
                          <xsd:element name="ORG" type="ns1:ORG" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="DAT" type="ns1:DAT" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="IFT" type="ns1:IFT" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="PTK" type="ns1:PTK" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="CRI" type="ns1:CRI" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="CPN" minOccurs="0" maxOccurs="unbounded">
                            <xsd:complexType>
                              <xsd:complexContent>
                                <xsd:extension base="ns1:CPN">
                                  <xsd:sequence>
                                    <xsd:element name="TVL" type="ns1:TVL" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="RCI" type="ns1:RCI" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="RPI" type="ns1:RPI" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="PTS" type="ns1:PTS" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="EBD" type="ns1:EBD" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="FTI" type="ns1:FTI" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="DAT" type="ns1:DAT" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="ORG" type="ns1:ORG" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="IFT" type="ns1:IFT" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="PTK" type="ns1:PTK" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="FAR" type="ns1:FAR" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="CVR" type="ns1:CVR" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="APD" type="ns1:APD" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="SSR" type="ns1:SSR" minOccurs="0" maxOccurs="unbounded"/>
                                  </xsd:sequence>
                                </xsd:extension>
                              </xsd:complexContent>
                            </xsd:complexType>
                          </xsd:element>
                          <xsd:element name="CP2" minOccurs="0" maxOccurs="unbounded">
                            <xsd:complexType>
                              <xsd:complexContent>
                                <xsd:extension base="ns1:CP2">
                                  <xsd:sequence>
                                    <xsd:element name="TVL" type="ns1:TVL" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="RCI" type="ns1:RCI" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="RPI" type="ns1:RPI" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="PTS" type="ns1:PTS" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="EBD" type="ns1:EBD" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="FTI" type="ns1:FTI" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="DAT" type="ns1:DAT" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="ORG" type="ns1:ORG" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="IFT" type="ns1:IFT" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="PTK" type="ns1:PTK" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="FAR" type="ns1:FAR" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="CVR" type="ns1:CVR" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="APD" type="ns1:APD" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="SSR" type="ns1:SSR" minOccurs="0" maxOccurs="unbounded"/>
                                  </xsd:sequence>
                                </xsd:extension>
                              </xsd:complexContent>
                            </xsd:complexType>
                          </xsd:element>
                        </xsd:sequence>
                      </xsd:extension>
                    </xsd:complexContent>
                  </xsd:complexType>
                </xsd:element>
                <xsd:element name="FCI" minOccurs="0" maxOccurs="unbounded">
                  <xsd:complexType>
                    <xsd:complexContent>
                      <xsd:extension base="ns1:FCI">
                        <xsd:sequence>
                          <xsd:element name="EQN" minOccurs="0" maxOccurs="unbounded">
                            <xsd:complexType>
                              <xsd:complexContent>
                                <xsd:extension base="ns1:EQN">
                                  <xsd:sequence>
                                    <xsd:element name="ITM" minOccurs="0" maxOccurs="unbounded">
                                      <xsd:complexType>
                                        <xsd:complexContent>
                                          <xsd:extension base="ns1:ITM">
                                            <xsd:sequence>
                                              <xsd:element name="ADT" minOccurs="0" maxOccurs="unbounded">
                                                <xsd:complexType>
                                                  <xsd:complexContent>
                                                    <xsd:extension base="ns1:ADT">
                                                      <xsd:sequence>
                                                        <xsd:element name="TVL" type="ns1:TVL" minOccurs="0" maxOccurs="unbounded"/>
                                                      </xsd:sequence>
                                                    </xsd:extension>
                                                  </xsd:complexContent>
                                                </xsd:complexType>
                                              </xsd:element>
                                              <xsd:element name="MON" type="ns1:MON" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="PTS" type="ns1:PTS" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="FCC" type="ns1:FCC" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="FRU" type="ns1:FRU" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="PTK" type="ns1:PTK" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="FAR" type="ns1:FAR" minOccurs="0" maxOccurs="unbounded"/>
                                            </xsd:sequence>
                                          </xsd:extension>
                                        </xsd:complexContent>
                                      </xsd:complexType>
                                    </xsd:element>
                                  </xsd:sequence>
                                </xsd:extension>
                              </xsd:complexContent>
                            </xsd:complexType>
                          </xsd:element>
                          <xsd:element name="MON" type="ns1:MON" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="TXD" type="ns1:TXD" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="TAX" type="ns1:TAX" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="TX2" type="ns1:TX2" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="CVR" type="ns1:CVR" minOccurs="0" maxOccurs="unbounded"/>
                        </xsd:sequence>
                      </xsd:extension>
                    </xsd:complexContent>
                  </xsd:complexType>
                </xsd:element>
                <xsd:element name="STX" minOccurs="0" maxOccurs="unbounded">
                  <xsd:complexType>
                    <xsd:complexContent>
                      <xsd:extension base="ns1:STX">
                        <xsd:sequence>
                          <xsd:element name="TVL" type="ns1:TVL" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="PTK" type="ns1:PTK" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="TKT" type="ns1:TKT" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="TK2" type="ns1:TK2" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="CPN" type="ns1:CPN" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="CP2" type="ns1:CP2" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="PTS" type="ns1:PTS" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="RTG" type="ns1:RTG" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="APD" type="ns1:APD" minOccurs="0" maxOccurs="unbounded"/>
                        </xsd:sequence>
                      </xsd:extension>
                    </xsd:complexContent>
                  </xsd:complexType>
                </xsd:element>
                <xsd:element name="TPD" minOccurs="0" maxOccurs="unbounded">
                  <xsd:complexType>
                    <xsd:complexContent>
                      <xsd:extension base="ns1:TPD">
                        <xsd:sequence>
                          <xsd:element name="SPI" type="ns1:SPI" minOccurs="0" maxOccurs="unbounded"/>
                        </xsd:sequence>
                      </xsd:extension>
                    </xsd:complexContent>
                  </xsd:complexType>
                </xsd:element>
                <xsd:element name="ABI" minOccurs="0" maxOccurs="unbounded">
                  <xsd:complexType>
                    <xsd:complexContent>
                      <xsd:extension base="ns1:ABI">
                        <xsd:sequence>
                          <xsd:element name="DID" type="ns1:DID" minOccurs="0" maxOccurs="unbounded"/>
                        </xsd:sequence>
                      </xsd:extension>
                    </xsd:complexContent>
                  </xsd:complexType>
                </xsd:element>
                <xsd:element name="SDT" minOccurs="0" maxOccurs="unbounded">
                  <xsd:complexType>
                    <xsd:complexContent>
                      <xsd:extension base="ns1:SDT">
                        <xsd:sequence>
                          <xsd:element name="SPI" maxOccurs="unbounded">
                            <xsd:complexType>
                              <xsd:complexContent>
                                <xsd:extension base="ns1:SPI">
                                  <xsd:sequence>
                                    <xsd:element name="MON" type="ns1:MON" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="FOP_K80" type="ns1:FOP_K80" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="TXD" type="ns1:TXD" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="TAX" type="ns1:TAX" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="TX2" type="ns1:TX2" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="PTK" type="ns1:PTK" minOccurs="0" maxOccurs="unbounded"/>
                                    <xsd:element name="ITM" minOccurs="0" maxOccurs="unbounded">
                                      <xsd:complexType>
                                        <xsd:complexContent>
                                          <xsd:extension base="ns1:ITM">
                                            <xsd:sequence>
                                              <xsd:element name="FRU" type="ns1:FRU" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="PTK" type="ns1:PTK" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="FAR" type="ns1:FAR" minOccurs="0" maxOccurs="unbounded"/>
                                            </xsd:sequence>
                                          </xsd:extension>
                                        </xsd:complexContent>
                                      </xsd:complexType>
                                    </xsd:element>
                                  </xsd:sequence>
                                </xsd:extension>
                              </xsd:complexContent>
                            </xsd:complexType>
                          </xsd:element>
                        </xsd:sequence>
                      </xsd:extension>
                    </xsd:complexContent>
                  </xsd:complexType>
                </xsd:element>
                <xsd:element name="PRT" minOccurs="0" maxOccurs="unbounded">
                  <xsd:complexType>
                    <xsd:complexContent>
                      <xsd:extension base="ns1:PRT">
                        <xsd:sequence>
                          <xsd:element name="DAT" type="ns1:DAT" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="TKT" maxOccurs="unbounded">
                            <xsd:complexType>
                              <xsd:complexContent>
                                <xsd:extension base="ns1:TKT">
                                  <xsd:sequence>
                                    <xsd:element name="CPN" maxOccurs="unbounded">
                                      <xsd:complexType>
                                        <xsd:complexContent>
                                          <xsd:extension base="ns1:CPN">
                                            <xsd:sequence>
                                              <xsd:element name="TXD" type="ns1:TXD" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="TAX" type="ns1:TAX" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="TX2" type="ns1:TX2" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="EQI" type="ns1:EQI" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="TII" minOccurs="0" maxOccurs="unbounded">
                                                <xsd:complexType>
                                                  <xsd:complexContent>
                                                    <xsd:extension base="ns1:TII">
                                                      <xsd:sequence>
                                                        <xsd:element name="EQI" type="ns1:EQI" minOccurs="0" maxOccurs="unbounded"/>
                                                      </xsd:sequence>
                                                    </xsd:extension>
                                                  </xsd:complexContent>
                                                </xsd:complexType>
                                              </xsd:element>
                                            </xsd:sequence>
                                          </xsd:extension>
                                        </xsd:complexContent>
                                      </xsd:complexType>
                                    </xsd:element>
                                    <xsd:element name="CP2" maxOccurs="unbounded">
                                      <xsd:complexType>
                                        <xsd:complexContent>
                                          <xsd:extension base="ns1:CP2">
                                            <xsd:sequence>
                                              <xsd:element name="TXD" type="ns1:TXD" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="TAX" type="ns1:TAX" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="TX2" type="ns1:TX2" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="EQI" type="ns1:EQI" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="TII" minOccurs="0" maxOccurs="unbounded">
                                                <xsd:complexType>
                                                  <xsd:complexContent>
                                                    <xsd:extension base="ns1:TII">
                                                      <xsd:sequence>
                                                        <xsd:element name="EQI" type="ns1:EQI" minOccurs="0" maxOccurs="unbounded"/>
                                                      </xsd:sequence>
                                                    </xsd:extension>
                                                  </xsd:complexContent>
                                                </xsd:complexType>
                                              </xsd:element>
                                            </xsd:sequence>
                                          </xsd:extension>
                                        </xsd:complexContent>
                                      </xsd:complexType>
                                    </xsd:element>
                                  </xsd:sequence>
                                </xsd:extension>
                              </xsd:complexContent>
                            </xsd:complexType>
                          </xsd:element>
                          <xsd:element name="TK2" maxOccurs="unbounded">
                            <xsd:complexType>
                              <xsd:complexContent>
                                <xsd:extension base="ns1:TK2">
                                  <xsd:sequence>
                                    <xsd:element name="CPN" maxOccurs="unbounded">
                                      <xsd:complexType>
                                        <xsd:complexContent>
                                          <xsd:extension base="ns1:CPN">
                                            <xsd:sequence>
                                              <xsd:element name="TXD" type="ns1:TXD" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="TAX" type="ns1:TAX" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="TX2" type="ns1:TX2" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="EQI" type="ns1:EQI" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="TII" minOccurs="0" maxOccurs="unbounded">
                                                <xsd:complexType>
                                                  <xsd:complexContent>
                                                    <xsd:extension base="ns1:TII">
                                                      <xsd:sequence>
                                                        <xsd:element name="EQI" type="ns1:EQI" minOccurs="0" maxOccurs="unbounded"/>
                                                      </xsd:sequence>
                                                    </xsd:extension>
                                                  </xsd:complexContent>
                                                </xsd:complexType>
                                              </xsd:element>
                                            </xsd:sequence>
                                          </xsd:extension>
                                        </xsd:complexContent>
                                      </xsd:complexType>
                                    </xsd:element>
                                    <xsd:element name="CP2" maxOccurs="unbounded">
                                      <xsd:complexType>
                                        <xsd:complexContent>
                                          <xsd:extension base="ns1:CP2">
                                            <xsd:sequence>
                                              <xsd:element name="TXD" type="ns1:TXD" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="TAX" type="ns1:TAX" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="TX2" type="ns1:TX2" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="EQI" type="ns1:EQI" minOccurs="0" maxOccurs="unbounded"/>
                                              <xsd:element name="TII" minOccurs="0" maxOccurs="unbounded">
                                                <xsd:complexType>
                                                  <xsd:complexContent>
                                                    <xsd:extension base="ns1:TII">
                                                      <xsd:sequence>
                                                        <xsd:element name="EQI" type="ns1:EQI" minOccurs="0" maxOccurs="unbounded"/>
                                                      </xsd:sequence>
                                                    </xsd:extension>
                                                  </xsd:complexContent>
                                                </xsd:complexType>
                                              </xsd:element>
                                            </xsd:sequence>
                                          </xsd:extension>
                                        </xsd:complexContent>
                                      </xsd:complexType>
                                    </xsd:element>
                                  </xsd:sequence>
                                </xsd:extension>
                              </xsd:complexContent>
                            </xsd:complexType>
                          </xsd:element>
                        </xsd:sequence>
                      </xsd:extension>
                    </xsd:complexContent>
                  </xsd:complexType>
                </xsd:element>
                <xsd:element name="REF" minOccurs="0" maxOccurs="unbounded">
                  <xsd:complexType>
                    <xsd:complexContent>
                      <xsd:extension base="ns1:REF">
                        <xsd:sequence>
                          <xsd:element name="MON" type="ns1:MON" minOccurs="0" maxOccurs="unbounded"/>
                          <xsd:element name="CVR" type="ns1:CVR" minOccurs="0" maxOccurs="unbounded"/>
                        </xsd:sequence>
                      </xsd:extension>
                    </xsd:complexContent>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="KC0">
    <xsd:sequence>
      <xsd:element name="TKTRES" type="ns0:TKTRES" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:element name="trunk" type="ns0:Trunk"/>
</xsd:schema>]]></con:content>
        <con:type>http://www.w3.org/2001/XMLSchema</con:type>
      </con:part>
      <con:part>
        <con:url>https://wskurumsaltest.thy.com/edf-ea-ws/TkresSoapXmlService?xsd=3</con:url>
        <con:content><![CDATA[<!--Published by JAX-WS RI (http://jax-ws.java.net). RI's version is JAX-WS RI 2.2.11-b150616.1732 svn-revision#a247ba216861f2c0baac9a3657c5690bce0c744d.-->
<xsd:schema targetNamespace="http://www.turkishairlines.com/schema/rrt0tk/v0100/segment" elementFormDefault="qualified" xmlns:ns0="http://www.turkishairlines.com/schema/rrt0tk/v0100/segment" xmlns:ns1="http://ws.ea.edf.tk.com/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xsd:import schemaLocation="https://wskurumsaltest.thy.com/edf-ea-ws/TkresSoapXmlService?xsd=1" namespace="http://ws.ea.edf.tk.com/"/>
  <xsd:complexType name="FOP_K30">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="OldTicketDocumentNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SpareForExpansion" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DocumentType" type="xsd:string" minOccurs="0"/>
      <xsd:element name="TotNumberOfBookletsIssued" type="xsd:string" minOccurs="0"/>
      <xsd:element name="IssueDate" type="xsd:string" minOccurs="0"/>
      <xsd:element name="GeneralIndicator" type="xsd:string" minOccurs="0"/>
      <xsd:element name="BitInfo1" type="xsd:int" minOccurs="0"/>
      <xsd:element name="BitInfo2" type="xsd:int" minOccurs="0"/>
      <xsd:element name="BitInfo3" type="xsd:int" minOccurs="0"/>
      <xsd:element name="ConjunctionTktNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="Spare" type="xsd:string" minOccurs="0"/>
      <xsd:element name="BLOCK" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="FopType" type="xsd:string" minOccurs="0"/>
            <xsd:element name="IndicatesOldOrNewFop" type="xsd:string" minOccurs="0"/>
            <xsd:element name="FormOfPaymentAmount" type="xsd:string" minOccurs="0"/>
            <xsd:element name="VendorCodeCc" type="xsd:string" minOccurs="0"/>
            <xsd:element name="AccountNumberCc" type="xsd:string" minOccurs="0"/>
            <xsd:element name="ExpirationDateMmyy" type="xsd:string" minOccurs="0"/>
            <xsd:element name="ApprovalCode" type="xsd:string" minOccurs="0"/>
            <xsd:element name="SourceOfApprovalCode" type="xsd:string" minOccurs="0"/>
            <xsd:element name="AuthorizedAmount" type="xsd:string" minOccurs="0"/>
            <xsd:element name="AddressVerificationCode" type="xsd:string" minOccurs="0"/>
            <xsd:element name="CustomerFileReference" type="xsd:string" minOccurs="0"/>
            <xsd:element name="ExtendedPaymentCode" type="xsd:string" minOccurs="0"/>
            <xsd:element name="FreeTextMiscFop" type="xsd:string" minOccurs="0"/>
            <xsd:element name="CcCorporateContract" type="xsd:string" minOccurs="0"/>
            <xsd:element name="CcTransactionInfo" type="xsd:string" minOccurs="0"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="INF">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="BLOCK" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="FreeTextCode" type="xsd:string"/>
            <xsd:element name="SpareForExpansion" type="xsd:string"/>
            <xsd:element name="FreeTextArea" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="VariableFreeTextPart" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="TKD">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="TicketNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="FlownCouponNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CarrierCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="RouteNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="RecordDate" type="xsd:string" minOccurs="0"/>
      <xsd:element name="BrdPoint" type="xsd:string" minOccurs="0"/>
      <xsd:element name="OffPoint" type="xsd:string" minOccurs="0"/>
      <xsd:element name="MarketingCarrierCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="MarketingRouteNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="IntermediateStops" type="xsd:long" minOccurs="0"/>
      <xsd:element name="IntermediateAirportCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="IninearySeq" type="xsd:long" minOccurs="0"/>
      <xsd:element name="FlownCouponDateOfIssue" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="MSG">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="IdentifiesTheRequestedAction1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="IdentifiesTheRequestedAction2" type="xsd:long" minOccurs="0"/>
      <xsd:element name="ResponseType" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="ORG">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="AirlineCrsCodeOfSendingSystem" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CityCodeOfTheSystem" type="xsd:string" minOccurs="0"/>
      <xsd:element name="IataNumberOfTheAgentIssuingTkt" type="xsd:long" minOccurs="0"/>
      <xsd:element name="AgencyPseudoCityEtc" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CityCodeOfTheAgencyLocation" type="xsd:string" minOccurs="0"/>
      <xsd:element name="AirlineCodeOfOriginatingSystem" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CityCodeOfOriginatingSystem" type="xsd:string" minOccurs="0"/>
      <xsd:element name="TravelAgentTAirlineAErspE" type="xsd:string" minOccurs="0"/>
      <xsd:element name="IsoCountryCodeOfIssuingAgent" type="xsd:string" minOccurs="0"/>
      <xsd:element name="IdOfAgentInitiatingTheRequest" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="TA2">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="NumericAirlineCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="BLOCK" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="AgentIdentification" type="xsd:string"/>
            <xsd:element name="AgentType1" type="xsd:string"/>
            <xsd:element name="AgentType2" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="TAI">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="BLOCK" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="AgentIdentification" type="xsd:string"/>
            <xsd:element name="AgentType" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="NumericAirlineCode" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="RCI">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="BLOCK" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="AirlineCode" type="xsd:string"/>
            <xsd:element name="PnrReference" type="xsd:string"/>
            <xsd:element name="ReservationControlType" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="MON">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="BLOCK" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="AmountTypeQualifier" type="xsd:string"/>
            <xsd:element name="Amount" type="xsd:string"/>
            <xsd:element name="IsoCurerencyCode" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="PTK">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="PriceTypeQualifier" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="DateOfIssue" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="ODI">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="Origin" type="xsd:string" minOccurs="0"/>
      <xsd:element name="Destination" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="ATI">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="TourCode" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="FOP_K80">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="BLOCK" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="FopType" type="xsd:string"/>
            <xsd:element name="IndicatesOldOrNewFop" type="xsd:string"/>
            <xsd:element name="FormOfPaymentAmount" type="xsd:string"/>
            <xsd:element name="VendorCodeCc" type="xsd:string"/>
            <xsd:element name="AccountNumberCc" type="xsd:string"/>
            <xsd:element name="ExpirationDateMmyy" type="xsd:string"/>
            <xsd:element name="ApprovalCode" type="xsd:string"/>
            <xsd:element name="SourceOfApprovalCode" type="xsd:string"/>
            <xsd:element name="AuthorizedAmount" type="xsd:string"/>
            <xsd:element name="AddressVerificationCode" type="xsd:string"/>
            <xsd:element name="CustomerFileReference" type="xsd:string"/>
            <xsd:element name="ExtendedPaymentCode" type="xsd:string"/>
            <xsd:element name="FreeTextMiscFop" type="xsd:string"/>
            <xsd:element name="CcCorporateContract" type="xsd:string"/>
            <xsd:element name="CcTransactionInfo" type="xsd:string"/>
            <xsd:element name="OrderId" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="EQN">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="BLOCK" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="TotalNumberOfTicketDocuments" type="xsd:long"/>
            <xsd:element name="NumberOfUnitsQualifierTdOrTf" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="ITM">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="ItemNumberIdentification" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="ItemNumber" type="xsd:string"/>
            <xsd:element name="ItemNumberType" type="xsd:string"/>
            <xsd:element name="CodeListQualifier" type="xsd:string"/>
            <xsd:element name="CodeListResponsibleAgency" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="ItemNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="ItemNumberType" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CodeListQualifier" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CodeListResponsibleAgency" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="FRU">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="RulesPartIdentification" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="RulePartIdentification" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="RateTrafficClassIdentity" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CompanyIdentification1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CompanyIdentification2" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="FAR">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="NumberOfUnitsQualifier1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="Quantity" type="xsd:string" minOccurs="0"/>
      <xsd:element name="NumberOfUnitsQualifier2" type="xsd:string" minOccurs="0"/>
      <xsd:element name="Percentage" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CountryCoded" type="xsd:string" minOccurs="0"/>
      <xsd:element name="FareClassificationType" type="xsd:string" minOccurs="0"/>
      <xsd:element name="IdentityNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PricingGroup" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="RateTariffClass" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="ADT">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="MessageFunctionCoded" type="xsd:string" minOccurs="0"/>
      <xsd:element name="ReferenceQualifier" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SequenceNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="References" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="SequenceNumber" type="xsd:string"/>
            <xsd:element name="ReferenceQualifiers" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="TVL">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="DepartureDate" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DepartureTimeHhmm" type="xsd:string" minOccurs="0"/>
      <xsd:element name="OriginCityAirportCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DestinationCityAirport" type="xsd:string" minOccurs="0"/>
      <xsd:element name="MarketingCarrierCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="OperatingCarrierCodeFlightNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="ArrTimeHhmm" type="xsd:string" minOccurs="0"/>
      <xsd:element name="FlightNumberOrOpen" type="xsd:string" minOccurs="0"/>
      <xsd:element name="BookingDesignator" type="xsd:string" minOccurs="0"/>
      <xsd:element name="BookingDesignatorModifier" type="xsd:string" minOccurs="0"/>
      <xsd:element name="StopoverCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CabinClass" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SegmentNumber" type="xsd:int" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="PTS">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="FareBasisTicketDesignator" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CurrentOrOriginalFareBasis" type="xsd:string" minOccurs="0"/>
      <xsd:element name="RateTariffClass1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DataIndicator1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="MonetaryAmount" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PriceTypeQualifier" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SpecialConditionCodedRfic" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SpecialConditionCodedRficsc" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SpecialConditionCoded" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DutyTaxFeeCategoryCoded1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DutyTaxFeeCategoryCoded2" type="xsd:string" minOccurs="0"/>
      <xsd:element name="RateTariffClass2" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DataIndicator2" type="xsd:string" minOccurs="0"/>
      <xsd:element name="RateTariffClass3" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DataIndicator3" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="FCC">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="ChargeCategoryCoded" type="xsd:string" minOccurs="0"/>
      <xsd:element name="MonetaryAmount" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PlaceLocIdentification1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PlaceLocIdentification2" type="xsd:string" minOccurs="0"/>
      <xsd:element name="Percentage" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="TXD">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="BLOCK" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="DutyTaxFeeRate" type="xsd:string"/>
            <xsd:element name="DutyTaxFeeType" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="DutyTaxFeeCtegory" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="TAX">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="BLOCK" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="DutyTaxFeeRate" type="xsd:string"/>
            <xsd:element name="DutyTaxFeeType" type="xsd:string"/>
            <xsd:element name="TaxCurrency" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="DutyTaxFeeCtegory" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="TX2">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="DutyTaxFeeCtegory" type="xsd:string" minOccurs="0"/>
      <xsd:element name="BLOCK" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="DutyTaxFeeRate1" type="xsd:string"/>
            <xsd:element name="DutyTaxFeeType1" type="xsd:string"/>
            <xsd:element name="DutyTaxFeeRate2" type="xsd:string"/>
            <xsd:element name="TaxFiledIsoCurrencyCode" type="xsd:string"/>
            <xsd:element name="DutyTaxFeeType2" type="xsd:string"/>
            <xsd:element name="FiledCurrencyRate" type="xsd:string"/>
            <xsd:element name="TaxQualifier" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="CPN">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="CouponNumber1Of4" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CouponStatus" type="xsd:string" minOccurs="0"/>
      <xsd:element name="AirlineWithControlOfCoupon" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CouponLevelIndicators" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CouponIndicator" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CouponValue" type="xsd:long" minOccurs="0"/>
      <xsd:element name="ExchangeMedia" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SettlementAuthorizationCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SubCity" type="xsd:string" minOccurs="0"/>
      <xsd:element name="InvoluntaryIndicator" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PreviousStatusCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CheckedInCarrierCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CheckedInRouteNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CheckedInFlightDate" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CheckedInBoardpoint" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CheckedInDestination" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="RPI">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="ReservationStatusCode" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="EBD">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="BLOCK" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="FreeBaggageAllowance" type="xsd:string"/>
            <xsd:element name="PiecesOrWeight" type="xsd:string"/>
            <xsd:element name="KilosOrPounds" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="FTI">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="AirlineDesignator" type="xsd:string" minOccurs="0"/>
      <xsd:element name="FrequentTravelerNumber" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="DAT">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="BLOCK" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="DateTimePeriodQualifier" type="xsd:string"/>
            <xsd:element name="ValidDate" type="xsd:string" minOccurs="0"/>
            <xsd:element name="FirstTimeHhmm" type="xsd:long"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="IFT">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="BLOCK" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="FreeText" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="TextSubjectQualifier" type="xsd:string" minOccurs="0"/>
      <xsd:element name="InformationType" type="xsd:string" minOccurs="0"/>
      <xsd:element name="FareCalculationModeIndicator" type="xsd:string" minOccurs="0"/>
      <xsd:element name="Spare1" type="xsd:long" minOccurs="0"/>
      <xsd:element name="Spare2" type="xsd:long" minOccurs="0"/>
      <xsd:element name="Spare3" type="xsd:long" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="CVR">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="ConversionTypeCoded" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CurrencyCoded" type="xsd:string" minOccurs="0"/>
      <xsd:element name="RateTypeQualifier" type="xsd:string" minOccurs="0"/>
      <xsd:element name="MonetaryAmount1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="MonetaryAmount2" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DutyTaxFeeCategoryCoded" type="xsd:string" minOccurs="0"/>
      <xsd:element name="MeasurementValue" type="xsd:string" minOccurs="0"/>
      <xsd:element name="MeasurementSignificantCode" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="APD">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="TypeOfMeansOfTransport" type="xsd:string" minOccurs="0"/>
      <xsd:element name="NumberOfStops" type="xsd:string" minOccurs="0"/>
      <xsd:element name="LegDuration" type="xsd:string" minOccurs="0"/>
      <xsd:element name="Percentage" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DaysOfOperation" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DateTimePeriod" type="xsd:string" minOccurs="0"/>
      <xsd:element name="ComplexingFlightIndicator" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PlaceLocIdentification1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PlaceLocIdentification2" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PlaceLocIdentification3" type="xsd:string" minOccurs="0"/>
      <xsd:element name="GateDescription1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PlaceLocationOneId1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PlaceLocationTwoId1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="GateDescription2" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PlaceLocationOneId2" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PlaceLocationTwoId2" type="xsd:string" minOccurs="0"/>
      <xsd:element name="MeasurementValue" type="xsd:string" minOccurs="0"/>
      <xsd:element name="MeasureUnitQualifier" type="xsd:string" minOccurs="0"/>
      <xsd:element name="FirstTime1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="FirstTime2" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SecondTime" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CheckInDetails" type="xsd:string" minOccurs="0"/>
      <xsd:element name="FacilityType" type="xsd:string" minOccurs="0"/>
      <xsd:element name="FacilityDescription" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="SSR">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="SpecialRequirementDataDetails" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="SpecialRequirementData" type="xsd:string"/>
            <xsd:element name="MeasureUnitQualifier" type="xsd:string"/>
            <xsd:element name="TravelerReferenceNumber" type="xsd:string"/>
            <xsd:element name="SeatCharacteristicCoded" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="SpecialTypeRequirement" type="xsd:string" minOccurs="0"/>
      <xsd:element name="StatusCoded" type="xsd:string" minOccurs="0"/>
      <xsd:element name="Quantity" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CompanyIdentification" type="xsd:string" minOccurs="0"/>
      <xsd:element name="ProcessingIndicator1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="ProcessingIndicator2" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PlaceLocIdentification1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PlaceLocIdentification2" type="xsd:string" minOccurs="0"/>
      <xsd:element name="FreeText" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="EQI">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="TotalSeatCount" type="xsd:string" minOccurs="0"/>
      <xsd:element name="ConfigurationDetails" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="CabinClassDesingnator" type="xsd:string"/>
            <xsd:element name="NumberOfSeats" type="xsd:string"/>
            <xsd:element name="CharacteristicIdCount" type="xsd:string"/>
            <xsd:element name="CharacteristicIdList" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="TypeOfMeansOfTransport" type="xsd:string" minOccurs="0"/>
      <xsd:element name="AircraftCabinIdentification" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="CompanyIdentification" type="xsd:string" minOccurs="0"/>
      <xsd:element name="EquipmentIdentification" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CabinFacilityCharacter" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="Freetext" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="TII">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="PlacelocationIdentification" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="PlaceLocationIdentification" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="ItemNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="NumericRefIdentification" type="xsd:string" minOccurs="0"/>
      <xsd:element name="FirstDate" type="xsd:string" minOccurs="0"/>
      <xsd:element name="FirstTime" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SecondDate" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SecondTime" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DateVariation" type="xsd:string" minOccurs="0"/>
      <xsd:element name="MovementTypeCoded" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CabinCompartmentDesignator" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SequenceNumber1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SequenceNumber2" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="CP2">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="SequenceNumber1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="StatusCoded1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="MonetaryAmount" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SequenceNumberSourceCoded" type="xsd:string" minOccurs="0"/>
      <xsd:element name="ControlIdentification" type="xsd:string" minOccurs="0"/>
      <xsd:element name="StatusIndicatorCoded" type="xsd:string" minOccurs="0"/>
      <xsd:element name="StatusCoded2" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SequenceNumber2" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CouponItinerarySeqNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="ActionRequestNotificationCoded" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="DID">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="BLOCK" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="CertificateNumber" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="FQU">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="MovementTypeCoded" type="xsd:string" minOccurs="0"/>
      <xsd:element name="RateTypeIdentification1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="RateTypeIdentification2" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="NumberOfUnitsQualifier" type="xsd:string" minOccurs="0"/>
      <xsd:element name="Percentage" type="xsd:long" minOccurs="0"/>
      <xsd:element name="CountryCoded" type="xsd:string" minOccurs="0"/>
      <xsd:element name="FareClassificationCoded" type="xsd:string" minOccurs="0"/>
      <xsd:element name="RateTariffClass1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CommodityRateIdentification" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PricingGroup" type="xsd:string" minOccurs="0"/>
      <xsd:element name="RateTariffClass2" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="DiscountPenaltyInformation" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="FareQualifier" type="xsd:string"/>
            <xsd:element name="MonetaryAmount" type="xsd:string"/>
            <xsd:element name="Percentage" type="xsd:long"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="TKT">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="TicketDocumentNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SpareForExpansion" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DocumentType" type="xsd:string" minOccurs="0"/>
      <xsd:element name="TotalNumberOfBookletsIssued" type="xsd:long" minOccurs="0"/>
      <xsd:element name="IndicatesNewOrExchange" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="CRI">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="TypeOfId" type="xsd:string" minOccurs="0"/>
      <xsd:element name="IdNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PartyName" type="xsd:string" minOccurs="0"/>
      <xsd:element name="TravellerReferenceNumber" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="TK2">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="DocumentMessageNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SpareInTktField" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DocumentMessageNameCoded" type="xsd:string" minOccurs="0"/>
      <xsd:element name="TotalNumberOfItems" type="xsd:long" minOccurs="0"/>
      <xsd:element name="IndicatesInConnectionWith" type="xsd:string" minOccurs="0"/>
      <xsd:element name="ActionRequestNotificationCoded" type="xsd:string" minOccurs="0"/>
      <xsd:element name="InConnectionWithDocNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="StatusCoded" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="TIF">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="TravellerSurname" type="xsd:string" minOccurs="0"/>
      <xsd:element name="NumberOfUnits" type="xsd:string" minOccurs="0"/>
      <xsd:element name="QuantityAgeForUm" type="xsd:long" minOccurs="0"/>
      <xsd:element name="TravellerGivenName" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="FCI">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="DataIndicator" type="xsd:string" minOccurs="0"/>
      <xsd:element name="Quantity" type="xsd:string" minOccurs="0"/>
      <xsd:element name="FirstDate" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PricingGroup" type="xsd:string" minOccurs="0"/>
      <xsd:element name="RateTariffClass" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DocumentMessageNumber" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="STX">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="ProductInformation" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="StatusIndicator" type="xsd:string"/>
            <xsd:element name="ActionRequestNotification" type="xsd:string"/>
            <xsd:element name="StatusType" type="xsd:string"/>
            <xsd:element name="FreeText" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="RTG">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="ProductInformation" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="PlaceLocIdentification1" type="xsd:string"/>
            <xsd:element name="PlaceLocIdentification2" type="xsd:string"/>
            <xsd:element name="PlaceLocIdentification3" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="TPD">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="CompanyIdentification" type="xsd:string" minOccurs="0"/>
      <xsd:element name="RateTarifClassIdentity" type="xsd:string" minOccurs="0"/>
      <xsd:element name="FirstDate" type="xsd:string" minOccurs="0"/>
      <xsd:element name="TravellerReferenceNumber" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="SPI">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="DataInformation" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="DataIndicator" type="xsd:string"/>
            <xsd:element name="Quantity" type="xsd:string"/>
            <xsd:element name="MeasureUnitQualifier" type="xsd:string"/>
            <xsd:element name="FreeText" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="DataTypeCoded" type="xsd:string" minOccurs="0"/>
      <xsd:element name="StatusEventCoded" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="ABI">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="SecSubIdentification1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SecSubIdentification2" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="TravelAgentIdentification" type="xsd:long" minOccurs="0"/>
      <xsd:element name="InHouseIdentification" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="PlaceLocId" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PlaceLocName" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CountryCoded" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CompanyIdentification" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="SDT">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="SelectionDetailsInformation" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="Option" type="xsd:string"/>
            <xsd:element name="AssociatedOptInformation" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="PRT">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="PlaceLocIdentification" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CodeListQualifier" type="xsd:string" minOccurs="0"/>
      <xsd:element name="CodeListResponsibleAgency" type="xsd:string" minOccurs="0"/>
      <xsd:element name="PlaceLocation" type="xsd:string" minOccurs="0"/>
      <xsd:element name="FirstTime" type="xsd:string" minOccurs="0"/>
      <xsd:element name="SecondTime" type="xsd:string" minOccurs="0"/>
      <xsd:element name="UtcLocalTimeVariation" type="xsd:string" minOccurs="0"/>
      <xsd:element name="DateVariation" type="xsd:string" minOccurs="0"/>
      <xsd:element name="RelatedPlaceLocIdentity" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:complexType name="REF">
    <xsd:sequence>
      <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
      <xsd:element name="ReferencingDetails" minOccurs="0" maxOccurs="unbounded">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="LoadSequence" type="xsd:int" minOccurs="0"/>
            <xsd:element name="ReferenceQualifier" type="xsd:string"/>
            <xsd:element name="ReferenceNumber" type="xsd:string"/>
          </xsd:sequence>
          <xsd:attribute name="Hjid" type="xsd:long"/>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="ReferenceQualifier" type="xsd:string" minOccurs="0"/>
      <xsd:element name="ReferenceNumber" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="Hjid" type="xsd:long"/>
  </xsd:complexType>
  <xsd:element name="Ticket" type="ns1:RequestedTicket"/>
</xsd:schema>]]></con:content>
        <con:type>http://www.w3.org/2001/XMLSchema</con:type>
      </con:part>
    </con:definitionCache>
    <con:endpoints>
      <con:endpoint>https://wskurumsaltest.thy.com/edf-ea-ws/TkresSoapXmlService</con:endpoint>
    </con:endpoints>
    <con:operation id="b1d9fe33-0f35-45bd-a4e2-945a7f8b08ac" isOneWay="false" action="" name="displayTicket" bindingOperationName="displayTicket" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="dd5c68aa-dbaa-4a77-aca7-c853141a03d0fileName">displayTicket</con:setting>
        <con:setting id="5c517329-cadb-4bf1-80bc-04f8984bf503fileName">displayTicket</con:setting>
      </con:settings>
    </con:operation>
    <con:operation id="fdcd4eeb-12ed-4e2f-9f1f-0d4479e1a3b5" isOneWay="false" action="" name="displayTickets" bindingOperationName="displayTickets" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="af4653be-4bcc-4538-9768-776c815f54bafileName">displayTickets</con:setting>
        <con:setting id="abb1cf00-8afd-457c-98b8-f5d621920866fileName">displayTickets</con:setting>
      </con:settings>
    </con:operation>
    <con:operation id="7ce5e833-5777-4e2e-9972-52fc1af78107" isOneWay="false" action="" name="getHistoricallyConnectedTickets" bindingOperationName="getHistoricallyConnectedTickets" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="313e372d-2326-47c6-9357-393bdaac26b0fileName">getHistoricallyConnectedTickets</con:setting>
        <con:setting id="8474ef08-fbf7-49b8-a992-3f4549b330b3fileName">getHistoricallyConnectedTickets</con:setting>
      </con:settings>
    </con:operation>
    <con:operation id="b3ea4cb5-2c6f-4a13-848e-fec647d00fb1" isOneWay="false" action="" name="getOldTickets" bindingOperationName="getOldTickets" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="f2e0eeff-e02a-4ffd-9bf6-5f165ab61b9ffileName">getOldTickets</con:setting>
        <con:setting id="d54f232e-d8b9-41db-8480-940242b07be5fileName">getOldTickets</con:setting>
      </con:settings>
    </con:operation>
    <con:operation id="159deb64-b5d7-4fa8-8c8f-4f5cd542408c" isOneWay="false" action="" name="getTicketByID" bindingOperationName="getTicketByID" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="37fcda3e-ef9a-43db-a335-382072e412adfileName">getTicketByID</con:setting>
        <con:setting id="33116244-8196-44c8-83aa-dbebdb28359bfileName">getTicketByID</con:setting>
      </con:settings>
    </con:operation>
    <con:operation id="37651ff5-119e-4e2c-9b11-94e7c03837a5" isOneWay="false" action="" name="getTicketsByCreditCardNumber" bindingOperationName="getTicketsByCreditCardNumber" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="7347b189-42da-4ab1-83b2-3b71cc728900fileName">getTicketsByCreditCardNumber</con:setting>
        <con:setting id="2ccd6577-4fac-44fb-9070-5a04a8bb3d02fileName">getTicketsByCreditCardNumber</con:setting>
      </con:settings>
    </con:operation>
    <con:operation id="7ccfd525-7ee3-4450-95f2-a294c390e540" isOneWay="false" action="" name="getTicketsByEmdNumber" bindingOperationName="getTicketsByEmdNumber" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="f72f1e91-10d8-4371-b40e-d89202ee0326fileName">getTicketsByEmdNumber</con:setting>
        <con:setting id="694b831f-954e-426a-b6d9-a60c654ddacafileName">getTicketsByEmdNumber</con:setting>
      </con:settings>
    </con:operation>
    <con:operation id="c6d35dc7-b51d-461a-becb-fe62df172692" isOneWay="false" action="" name="getTicketsByFFPInfo" bindingOperationName="getTicketsByFFPInfo" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="49e5e513-655c-4067-9773-f8b456dc0f32fileName">getTicketsByFFPInfo</con:setting>
        <con:setting id="8b35a486-eb57-4d45-929f-0fc2fcd968aefileName">getTicketsByFFPInfo</con:setting>
      </con:settings>
    </con:operation>
    <con:operation id="22bcbbd8-f6e7-45a8-9973-83dd8629a566" isOneWay="false" action="" name="getTicketsByFFPInfoAndFlightDate" bindingOperationName="getTicketsByFFPInfoAndFlightDate" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="a93e1668-80b2-4c2f-8b23-8ad2e0077d45fileName">getTicketsByFFPInfoAndFlightDate</con:setting>
        <con:setting id="eadc8768-2682-4bc6-bc06-757f72f692e3fileName">getTicketsByFFPInfoAndFlightDate</con:setting>
      </con:settings>
    </con:operation>
    <con:operation id="0f4c42c4-9bd0-455a-9095-b1da8958e4cd" isOneWay="false" action="" name="getTicketsByFlightDetails" bindingOperationName="getTicketsByFlightDetails" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="75b379d4-e1dc-452c-8ef7-86fe91e6e7a1fileName">getTicketsByFlightDetails</con:setting>
        <con:setting id="074a6bcd-0fd8-4465-b78e-77409c967118fileName">getTicketsByFlightDetails</con:setting>
      </con:settings>
    </con:operation>
    <con:operation id="72334af3-789c-428f-ba4d-e3a2b6611269" isOneWay="false" action="" name="getTicketsByFlightInfo" bindingOperationName="getTicketsByFlightInfo" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="a959fa7c-1b75-42bf-930c-c612a14d7060fileName">getTicketsByFlightInfo</con:setting>
        <con:setting id="179a78e8-8a9d-4d0d-b009-ad68cbf3cf79fileName">getTicketsByFlightInfo</con:setting>
      </con:settings>
    </con:operation>
    <con:operation id="029aafe9-f86a-4d24-9870-4b3c74682c0f" isOneWay="false" action="" name="getTicketsByNameSurname" bindingOperationName="getTicketsByNameSurname" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="090d356d-4dd1-46c9-98da-48f5ea70281cfileName">getTicketsByNameSurname</con:setting>
        <con:setting id="f520a728-ac84-4a85-847a-bc0c9f2bd25ffileName">getTicketsByNameSurname</con:setting>
      </con:settings>
    </con:operation>
    <con:operation id="6fae4ee4-9d17-4ded-94aa-9dce8338f508" isOneWay="false" action="" name="getTicketsByOrderId" bindingOperationName="getTicketsByOrderId" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="0f079db9-567d-4774-a6b0-e9c4321a9ad8fileName">getTicketsByOrderId</con:setting>
        <con:setting id="95863efa-d9da-4ccb-9a26-f424d8f63adefileName">getTicketsByOrderId</con:setting>
      </con:settings>
    </con:operation>
    <con:operation id="727af01e-c017-4f1d-9f35-680cae7fd121" isOneWay="false" action="" name="getTicketsByPNR" bindingOperationName="getTicketsByPNR" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="27dcbeae-a166-4374-9aaa-54f4a11a4892fileName">getTicketsByPNR</con:setting>
        <con:setting id="bb20b09b-bf05-4e43-9d36-ff3b4fc85518fileName">getTicketsByPNR</con:setting>
      </con:settings>
    </con:operation>
    <con:operation id="31b71f1d-eed0-4219-a42f-795bfe2563cc" isOneWay="false" action="" name="getTicketsByTicketNumberAndIssueDate" bindingOperationName="getTicketsByTicketNumberAndIssueDate" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="42e3dc31-01ca-4479-bd8f-e44a511d55c4fileName">getTicketsByTicketNumberAndIssueDate</con:setting>
        <con:setting id="0bfc6a63-c8a3-4ba7-ad79-1ecd16b2b00afileName">getTicketsByTicketNumberAndIssueDate</con:setting>
      </con:settings>
    </con:operation>
    <con:operation id="5b71bb93-f2e1-4873-a986-92e454697c0d" isOneWay="false" action="" name="getTicketsByValidatorInTimeRange" bindingOperationName="getTicketsByValidatorInTimeRange" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="49bf73ed-f82e-4cca-bc3b-28ae379cf692fileName">getTicketsByValidatorInTimeRange</con:setting>
        <con:setting id="731fc80c-61fd-460c-856a-ea7b8272d7a5fileName">getTicketsByValidatorInTimeRange</con:setting>
      </con:settings>
    </con:operation>
    <con:operation id="113acf38-d5b9-4471-801b-cab866498211" isOneWay="false" action="" name="getTroyaDisplayString" bindingOperationName="getTroyaDisplayString" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="b9779669-d3ba-40d3-814f-498eebddcaa8fileName">getTroyaDisplayString</con:setting>
        <con:setting id="d748f2f5-9a65-4c38-b22e-b6a7bda199f3fileName">getTroyaDisplayString</con:setting>
      </con:settings>
    </con:operation>
    <con:operation id="001b5a7a-4228-477f-9116-0adb66fde940" isOneWay="false" action="" name="hotTransform" bindingOperationName="hotTransform" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="5f48c95f-0eb7-4afa-858a-652720624171fileName">hotTransform</con:setting>
        <con:setting id="8e377211-04de-4281-a35c-781e6f5f0bfcfileName">hotTransform</con:setting>
      </con:settings>
    </con:operation>
    <con:operation id="5827d0ba-357a-430d-a3d8-c6f11ab31992" isOneWay="false" action="" name="postConstruct" bindingOperationName="postConstruct" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional">
      <con:settings>
        <con:setting id="1de5cb88-4301-4cfb-a966-901f41514f5afileName">postConstruct</con:setting>
        <con:setting id="2bc2e361-ff92-4ff1-abbf-a5b358982892fileName">postConstruct</con:setting>
      </con:settings>
    </con:operation>
  </con:interface>
  <con:testSuite id="09d95f58-ad6b-4b7b-99c8-b5119b9505a4" name="Pares">
    <con:settings/>
    <con:savedRecentRuns>1</con:savedRecentRuns>
    <con:runType>SEQUENTIAL</con:runType>
    <con:testCase id="edc8b531-0c83-40f0-a231-65e83ccb17a9" discardOkResults="true" failOnError="false" failTestCaseOnErrors="true" keepSession="false" name="FormOfPaymentInfo" searchProperties="true" timeout="0" maxResults="0" wsrmEnabled="false" wsrmVersion="1.0" wsrmAckTo="" zephyrTestName="" zephyrTestId="">
      <con:settings>
        <con:setting id="HttpSettings@socket_timeout">60000</con:setting>
      </con:settings>
      <con:savedRecentRuns>1</con:savedRecentRuns>
      <con:testStep type="request" name="displayTicket" id="86b4f6f9-5b4d-4e85-8f0a-91bed9162a11">
        <con:settings/>
        <con:config xsi:type="con:RequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <con:interface>TkresSoapXmlServicePortBinding</con:interface>
          <con:operation>displayTicket</con:operation>
          <con:request name="displayTicket" id="e917cf07-3fcf-4a54-8cd8-ddaad0d81f2e">
            <con:settings>
              <con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting>
            </con:settings>
            <con:encoding>UTF-8</con:encoding>
            <con:endpoint>https://wskurumsaltest.thy.com/edf-ea-ws/TkresSoapXmlService</con:endpoint>
            <con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ws="http://ws.ea.edf.tk.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <ws:displayTicket>
         <!--Optional:-->
         <ticketNumber>${= context['documentNumber']}</ticketNumber>
      </ws:displayTicket>
   </soapenv:Body>
</soapenv:Envelope>]]></con:request>
            <con:assertion type="GroovyScriptAssertion" id="a130ffa6-2d4e-48e7-88f5-f34d1b1cc131" name="Script Assertion">
              <con:configuration>
                <scriptText>int fopCount = context.expand( '${displayTicket#Response#count(//*:displayTicketResponse[1]/return[1]/trunk[1]/*:K80[1]/*:TKTRES[1]/*:TIF[1]/*:FOP_K80)}' ) as int
if (context.documentType == 'RFND') {
	assert fopCount >= 2
} else {
	assert fopCount >= 1
}
String pnrExists = context.expand( '${displayTicket#Response#exists(//*:displayTicketResponse[1]/return[1]/trunk[1]/*:K80[1]/*:TKTRES[1]/*:TIF[1]/*:RCI[1]/*:BLOCK[1]/*:PnrReference[1])}' )
assert pnrExists == 'true'
String currencyExists = context.expand( '${displayTicket#Response#exists(//*:displayTicketResponse[1]/return[1]/trunk[1]/*:K80[1]/*:TKTRES[1]/*:TIF[1]/*:MON[1]/*:BLOCK[1]/*:IsoCurerencyCode[1])}' )
assert currencyExists == 'true'</scriptText>
              </con:configuration>
            </con:assertion>
            <con:credentials>
              <con:selectedAuthProfile>ykmwstestuser</con:selectedAuthProfile>
              <con:authType>No Authorization</con:authType>
            </con:credentials>
            <con:jmsConfig JMSDeliveryMode="PERSISTENT"/>
            <con:wsaConfig mustUnderstand="NONE" version="200508"/>
            <con:wsrmConfig version="1.2"/>
          </con:request>
        </con:config>
      </con:testStep>
      <con:setupScript/>
      <con:tearDownScript/>
      <con:properties/>
      <con:reportParameters/>
    </con:testCase>
    <con:properties/>
    <con:reportParameters/>
  </con:testSuite>
  <con:savedRecentRuns>1</con:savedRecentRuns>
  <con:properties/>
  <con:wssContainer/>
  <con:databaseConnectionContainer/>
  <con:jmsConnectionContainer/>
  <con:oAuth2ProfileContainer/>
  <con:oAuth1ProfileContainer/>
  <con:reporting>
    <con:xmlTemplates/>
    <con:parameters/>
  </con:reporting>
  <con:beforeRunScript/>
  <con:authRepository>
    <con:basicAuthEntry>
      <con:name>ykmwstestuser</con:name>
      <con:type>Basic</con:type>
      <con:username>ykmwstestuser</con:username>
      <con:password>wsc.Ob16+</con:password>
    </con:basicAuthEntry>
  </con:authRepository>
  <con:tags/>
</con:soapui-project>
